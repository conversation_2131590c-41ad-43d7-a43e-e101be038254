# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于AI驱动的RPA（机器人流程自动化）平台，通过集成LangGraph工作流引擎、阿里云百炼大语言模型和Playwright浏览器自动化技术，实现管理后台操作的智能自动化。

**v2.0 重大更新**: 已完成多Agent架构重构，实现零硬编码的AI驱动执行流程。

**v2.1 优化更新**: 解决了系统稳定性问题，添加了执行控制、错误恢复和性能监控功能。

## 常用命令

### 开发环境
```bash
# 安装所有依赖
npm run install:all

# 启动所有服务（开发模式）
npm run dev

# 分别启动各服务
npm run dev:workorder-backend   # 工单系统后端 (端口: 3001)
npm run dev:workorder-frontend  # 工单系统前端 (端口: 3000)
npm run dev:ai-assistant       # AI助手服务 (端口: 3002)
```

### 测试
```bash
# 运行所有测试
npm test

# AI助手专项测试
npm run test:ai-quick   # 快速测试（基础功能）
npm run test:ai-full   # 完整测试（包含所有场景）

# 核心测试文件
node test-system-basic.js             # 基础系统功能测试
node test-simple-llm-client.js        # 测试LLM客户端
node test-api-key.js                  # API密钥验证测试

# LangGraph工作流测试
node test-langgraph-suspend-resume.js  # 测试LangGraph暂停恢复
node test-langgraph-persistence.js     # 测试数据库状态持久化
node test-simple-langgraph.js         # 测试真实LangGraph库

# 多Agent架构测试
node test-multi-agent-e2e.js          # 测试多Agent端到端流程
node test-multi-agent-complete.js     # 完整的多Agent系统测试
node test-three-layer-validation.js    # 测试三层验证系统

# 集成和优化测试（v2.1）
node test-mcp-integration.js          # 测试MCP客户端集成
node test-fixes-validation.js         # 测试第一阶段修复效果
node test-optimization-features.js    # 测试第二阶段优化功能

# 端到端测试
node test-real-e2e.js                 # 真实环境端到端测试
node test-complete-workflow.js        # 完整工作流程测试
```

### 数据库
```bash
# 初始化数据库
npm run db:init

# 插入种子数据
npm run db:seed

# 重置数据库（删除并重新创建）
npm run db:reset
```

### 生产环境
```bash
# 构建项目
npm run build

# 启动生产服务
npm start
```

## 核心架构

### 1. 工单系统 (`src/workorder-system/`)
- **后端**: Express.js + SQLite + WebSocket
- **前端**: Vue 3 + Element Plus + Pinia
- **功能**: 工单创建、状态管理、实时同步、AI助手集成

### 2. AI助手系统 (`src/ai-assistant/`)

#### 多Agent架构 (v2.0新增)
- **Agent实现**: `src/agents/` - 5个核心Agent
  - Orchestrator Agent: 总控制器，负责任务分解和协调
  - Observer Agent: 观察分析页面，识别可操作元素
  - Decision Agent: 制定执行策略，生成具体操作步骤
  - Executor Agent: 执行浏览器操作，处理操作结果
  - Validator Agent: 验证操作结果，执行三层验证
- **Agent协调器**: `src/core/agent-coordinator.js`
- **多Agent处理器**: `src/langgraph/processors/multi-agent-processor.js`

#### 核心组件
- **LangGraph工作流**: `src/langgraph/` - 基于状态机的工作流引擎
  - 工作流定义: `workflows/rpa-workflow.js`
  - 状态管理: `state/workflow-state.js` 和 `state/workflow-state-manager.js`
  - 节点实现: `nodes/` 目录
- **MCP客户端**: `src/playwright-mcp/enhanced-simple-mcp-client.js`
- **提示词体系**: `prompts/` - 结构化提示词模板
- **LLM客户端**: `src/pdca/simple-llm-client.js` - 统一的模型调用接口

#### 新增组件（v2.1）
- **执行管理器**: `src/core/execution-manager.js` - 全局执行状态管理和超时控制
- **错误恢复引擎**: `src/core/error-recovery-engine.js` - 智能错误分类和恢复
- **性能监控器**: `src/monitoring/performance-monitor.js` - 实时性能监控和分析

### 3. 核心流程
```
工单创建 → 多Agent协同理解 → LangGraph工作流编排 → 智能执行与验证 → 结果反馈
```

**多Agent工作流程**:
1. Orchestrator接收任务并制定计划
2. Observer分析页面状态
3. Decision制定操作策略
4. Executor执行具体操作
5. Validator验证结果正确性

### 4. 状态管理
- 使用SQLite持久化工作流状态（`workflow_states`表）
- 支持任务挂起/恢复（通过LangGraph检查点机制）
- WebSocket实时状态同步
- 状态包含：工作流ID、当前节点、PDCA阶段、执行上下文等

## 环境配置

必需的环境变量（复制 `.env.example` 到 `.env`）：
- `DASHSCOPE_API_KEY`: 阿里云百炼API密钥
- `MAIN_MODEL`: qwen-plus（主模型）
- `VL_MODEL`: qwen-vl-plus（视觉模型）
- `BROWSER_HEADLESS`: false（开发时建议关闭无头模式）

## 关键技术点

### LangGraph工作流
- 节点: 恢复检查、任务队列、PDCA执行、验证、清理
- 状态持久化: 支持中断恢复
- 条件边: 基于执行结果的动态路由

### 浏览器自动化
- 使用Playwright进行页面操作
- MCP协议实现标准化控制
- 支持截图和视觉验证

### AI模型集成
- 任务理解: qwen-plus分析工单内容
- 视觉验证: qwen-vl-plus验证操作结果
- 提示词模板: `prompts/`目录（包含Agent专属提示词）

### 三层验证系统
- **L1 基础层**: DOM元素存在性验证
- **L2 功能层**: 业务逻辑正确性验证
- **L3 智能层**: AI视觉分析验证

### 数据库表结构
- `tickets`: 工单信息（ID、内容、状态、优先级等）
- `tasks`: 拆解后的任务（关联工单ID、任务描述、状态）
- `workflow_states`: LangGraph工作流状态（工作流ID、当前节点、检查点数据）
- `ai_workers`: AI工作线程状态（worker ID、状态、当前处理的工单）
- `operation_logs`: 操作日志（时间戳、操作类型、结果、截图等）

## 开发注意事项

1. **多Agent系统**: 所有决策都由Agent动态生成，无需硬编码
2. **模型调用**: 所有AI模型调用通过 `src/ai-assistant/src/pdca/simple-llm-client.js`
3. **浏览器操作**: 使用Enhanced Simple MCP Client，确保操作的标准化
4. **错误处理**: 通过Agent协同机制智能处理错误，支持重试和恢复
5. **日志记录**: 重要操作都会记录到 `operation_logs` 表
6. **状态同步**: 使用WebSocket确保前端实时显示处理进度
7. **LangGraph**: 确保节点函数返回新状态对象，不要修改原状态

## v2.1 新增注意事项

8. **执行超时**: 单操作30秒超时，总任务5分钟超时（可在ExecutionManager中配置）
9. **重复失败**: OrchestratorAgent会记录失败操作，避免重复执行相同的失败操作
10. **错误恢复**: ErrorRecoveryEngine会自动分类错误并尝试恢复，减少人工干预
11. **性能监控**: PerformanceMonitor提供实时性能数据，帮助优化系统性能
12. **验证次数限制**: ValidatorAgent最多验证3次，防止无限循环

## 调试技巧

1. 查看AI助手日志: `src/ai-assistant/server.js` 控制台输出
2. 检查工作流状态: 查询 `workflow_states` 表
3. 浏览器调试: 设置 `BROWSER_HEADLESS=false` 观察操作过程
4. 测试单个功能: 使用 `test-*.js` 文件进行针对性测试
5. Agent日志: 查看各Agent的决策过程和结果
6. LangGraph调试: 使用 `graph.getState()` 查看当前状态
7. 错误追踪: 查看 `error_history` 字段了解历史错误

## 🧪 测试驱动开发 (TDD)

### 当前测试状态 (2025-07-14)

#### ✅ 通过的测试 (20/28)
- **基础设施**: 服务启动、数据库连接、WebSocket通信 (8/8)
- **工单管理**: 创建、检测、队列管理、状态同步 (6/6)
- **LangGraph核心**: 基础工作流、挂起恢复机制 (4/4)
- **组件单元测试**: Logger、ExecutionManager、ErrorRecoveryEngine (2/4)

#### ❌ 发现的问题 (4/28)
- **关键阻塞**: LangGraph工作流初始化失败 (`TypeError: Cannot read properties of undefined (reading 'bind')`)
- **测试依赖**: 多个test文件引用已删除的模块
- **数据库Schema**: 已修复 `error_details` 和 `failed_at` 列缺失问题
- **节点方法**: 已补充缺失的 `agentExecutionNode` 等方法

#### 🟡 待完成测试 (4/28)
- **多Agent系统**: Agent协调、任务执行、验证机制
- **端到端集成**: 完整业务流程验证
- **AI行为验证**: 决策一致性、智能验证准确性
- **性能和负载**: 高并发、长时间运行稳定性

### TDD开发流程

#### 1. Red-Green-Refactor循环
```bash
# 1. 🔴 Red: 先写失败测试
npm run test:watch  # 持续监控测试状态

# 2. 🟢 Green: 写最小代码让测试通过
npm run test:unit   # 运行单元测试

# 3. ⭐ Refactor: 重构优化代码
npm run test:all    # 确保所有测试仍然通过
```

#### 2. 测试优先级
```bash
# P0 - 关键功能 (必须先修复)
npm run test:critical    # LangGraph初始化、Agent协调

# P1 - 核心功能 
npm run test:core       # 工单处理、多Agent系统

# P2 - 增强功能
npm run test:enhanced   # AI优化、性能监控
```

### 修复指导

#### 当前优先修复项
1. **LangGraph初始化问题** (P0)
   - 检查 `rpa-workflow.js` 中的节点方法绑定
   - 验证 StateAnnotation 配置正确性
   - 确保所有节点方法都已实现

2. **测试文件依赖更新** (P1)
   - 更新 `test-*.js` 文件的import路径
   - 移除对已删除模块的引用
   - 验证所有测试可执行

#### 开发约定
1. **测试先行**: 新功能必须先写测试
2. **测试覆盖**: 单元测试覆盖率 ≥90%
3. **集成验证**: PR必须通过所有测试
4. **AI测试**: AI决策需要一致性验证
5. **文档同步**: 测试即文档，保持同步