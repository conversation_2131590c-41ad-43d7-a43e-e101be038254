/**
 * 基础系统功能测试
 * 测试核心组件是否正常工作
 */

const logger = require('./src/ai-assistant/src/utils/logger');

async function testBasicComponents() {
    console.log('🧪 开始基础系统功能测试...\n');
    
    const results = {
        passed: [],
        failed: []
    };
    
    try {
        // 测试1: Logger功能
        console.log('📋 测试1: Logger功能');
        try {
            logger.info('测试INFO日志');
            logger.warn('测试WARN日志');
            logger.error('测试ERROR日志');
            results.passed.push({
                test: 'Logger功能',
                reason: '日志记录正常'
            });
            console.log('✅ 测试1通过\n');
        } catch (error) {
            results.failed.push({
                test: 'Logger功能',
                error: error.message
            });
            console.log('❌ 测试1失败\n');
        }
        
        // 测试2: SimpleLLMClient
        console.log('📋 测试2: SimpleLLMClient');
        try {
            const SimpleLLMClient = require('./src/ai-assistant/src/pdca/simple-llm-client');
            const llmClient = new SimpleLLMClient({
                model: process.env.MAIN_MODEL || 'qwen-plus'
            });
            
            const response = await llmClient.chat('请回复"测试成功"');
            
            if (response && response.content) {
                results.passed.push({
                    test: 'SimpleLLMClient',
                    reason: 'LLM调用成功'
                });
                console.log('✅ 测试2通过\n');
            }
        } catch (error) {
            results.failed.push({
                test: 'SimpleLLMClient',
                error: error.message
            });
            console.log('❌ 测试2失败:', error.message, '\n');
        }
        
        // 测试3: 执行管理器
        console.log('📋 测试3: ExecutionManager');
        try {
            const ExecutionManager = require('./src/ai-assistant/src/core/execution-manager');
            const execManager = new ExecutionManager({
                operationTimeout: 1000,
                enableTimeout: true
            });
            
            // 测试正常操作
            const result = await execManager.executeOperation(
                'test_op',
                'TestAgent',
                async () => {
                    return { success: true };
                }
            );
            
            if (result.success) {
                results.passed.push({
                    test: 'ExecutionManager',
                    reason: '执行管理器工作正常'
                });
                console.log('✅ 测试3通过\n');
            }
        } catch (error) {
            results.failed.push({
                test: 'ExecutionManager',
                error: error.message
            });
            console.log('❌ 测试3失败\n');
        }
        
        // 测试4: ErrorRecoveryEngine
        console.log('📋 测试4: ErrorRecoveryEngine');
        try {
            const ErrorRecoveryEngine = require('./src/ai-assistant/src/core/error-recovery-engine');
            const errorEngine = new ErrorRecoveryEngine();
            
            const networkError = new Error('Network timeout');
            const errorInfo = errorEngine.classifyError(networkError);
            
            if (errorInfo.category === 'NETWORK' && errorInfo.recoverable) {
                results.passed.push({
                    test: 'ErrorRecoveryEngine',
                    reason: '错误恢复引擎工作正常'
                });
                console.log('✅ 测试4通过\n');
            }
        } catch (error) {
            results.failed.push({
                test: 'ErrorRecoveryEngine',
                error: error.message
            });
            console.log('❌ 测试4失败\n');
        }
        
        // 测试5: PerformanceMonitor
        console.log('📋 测试5: PerformanceMonitor');
        try {
            const PerformanceMonitor = require('./src/ai-assistant/src/monitoring/performance-monitor');
            const perfMonitor = new PerformanceMonitor({
                enableReporting: false
            });
            
            const callId = perfMonitor.recordAgentCallStart('test', 'testMethod');
            await new Promise(resolve => setTimeout(resolve, 100));
            perfMonitor.recordAgentCallEnd(callId, true);
            
            const report = perfMonitor.generatePerformanceReport();
            if (report.summary.totalRequests === 1) {
                results.passed.push({
                    test: 'PerformanceMonitor',
                    reason: '性能监控器工作正常'
                });
                console.log('✅ 测试5通过\n');
            }
            
            perfMonitor.stopMonitoring();
        } catch (error) {
            results.failed.push({
                test: 'PerformanceMonitor',
                error: error.message
            });
            console.log('❌ 测试5失败\n');
        }
        
        // 测试6: Agent基础类
        console.log('📋 测试6: Agent基础类');
        try {
            const OrchestratorAgent = require('./src/ai-assistant/src/agents/orchestrator-agent');
            const orchestrator = new OrchestratorAgent();
            
            if (orchestrator.agentType === 'orchestrator') {
                results.passed.push({
                    test: 'Agent基础类',
                    reason: 'Agent类加载正常'
                });
                console.log('✅ 测试6通过\n');
            }
        } catch (error) {
            results.failed.push({
                test: 'Agent基础类',
                error: error.message
            });
            console.log('❌ 测试6失败\n');
        }
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
    }
    
    // 生成报告
    console.log('\n' + '='.repeat(50));
    console.log('📊 基础系统功能测试报告');
    console.log('='.repeat(50));
    console.log(`通过: ${results.passed.length}`);
    console.log(`失败: ${results.failed.length}`);
    console.log(`总测试: ${results.passed.length + results.failed.length}`);
    
    if (results.passed.length > 0) {
        console.log('\n✅ 通过的测试:');
        results.passed.forEach(test => {
            console.log(`  - ${test.test}: ${test.reason}`);
        });
    }
    
    if (results.failed.length > 0) {
        console.log('\n❌ 失败的测试:');
        results.failed.forEach(test => {
            console.log(`  - ${test.test}: ${test.error}`);
        });
    }
    
    console.log('\n' + '='.repeat(50));
    
    return results.failed.length === 0;
}

// 执行测试
if (require.main === module) {
    testBasicComponents()
        .then(success => {
            console.log(success ? '\n🎉 所有基础功能测试通过！' : '\n⚠️  部分测试失败');
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('\n🔥 测试异常:', error);
            process.exit(1);
        });
}

module.exports = { testBasicComponents };