/**
 * 测试用例：验证关键问题修复效果
 * 测试重点：
 * 1. ValidatorAgent不会无限递归
 * 2. 浏览器正确导航到目标页面
 * 3. ObserverAgent能识别异常页面
 * 4. 验证循环会被正确中断
 */

const { MultiAgentLangGraphProcessor } = require('./src/ai-assistant/src/langgraph/multi-agent-langgraph-processor');
const { createTicket } = require('./src/shared/core/ticket-manager');

async function runFixValidationTest() {
    console.log('🧪 开始验证修复效果测试...\n');
    
    const testResults = {
        passed: [],
        failed: [],
        startTime: Date.now()
    };
    
    try {
        // 测试1: 验证循环控制
        console.log('📋 测试1: 验证循环控制');
        const ticket1 = await createTicket({
            title: '测试验证循环控制',
            content: '导航到 https://example.com/admin/products 并搜索不存在的商品"TEST_NONEXISTENT_PRODUCT_12345"',
            type: 'rpa_task',
            priority: 'high'
        });
        
        const processor1 = new MultiAgentLangGraphProcessor();
        const startTime1 = Date.now();
        
        try {
            const result1 = await processor1.process(ticket1);
            const duration1 = Date.now() - startTime1;
            
            // 验证执行时间合理（不应该因为无限循环而超时）
            if (duration1 < 300000) { // 5分钟内完成
                testResults.passed.push({
                    test: '验证循环控制',
                    reason: `任务在${duration1/1000}秒内完成，未陷入无限循环`
                });
            } else {
                testResults.failed.push({
                    test: '验证循环控制',
                    reason: `执行时间过长：${duration1/1000}秒`
                });
            }
            
            // 检查是否有验证限制错误
            const hasValidationLimit = JSON.stringify(result1).includes('VALIDATION_LIMIT_EXCEEDED');
            if (hasValidationLimit) {
                testResults.passed.push({
                    test: '验证次数限制',
                    reason: '正确触发了验证次数限制'
                });
            }
            
        } catch (error) {
            testResults.failed.push({
                test: '验证循环控制',
                error: error.message
            });
        }
        
        console.log('✅ 测试1完成\n');
        
        // 测试2: 异常页面识别
        console.log('📋 测试2: 异常页面识别');
        const ticket2 = await createTicket({
            title: '测试异常页面识别',
            content: '在空白页面执行任务（不提供URL）',
            type: 'rpa_task',
            priority: 'high'
        });
        
        const processor2 = new MultiAgentLangGraphProcessor();
        
        try {
            const result2 = await processor2.process(ticket2);
            
            // 检查是否识别了异常页面
            const hasAbnormalDetection = JSON.stringify(result2).includes('isAbnormal') || 
                                        JSON.stringify(result2).includes('about:blank') ||
                                        JSON.stringify(result2).includes('空白页');
            
            if (hasAbnormalDetection) {
                testResults.passed.push({
                    test: '异常页面识别',
                    reason: '成功识别了空白页面异常'
                });
            } else {
                testResults.failed.push({
                    test: '异常页面识别',
                    reason: '未能识别空白页面'
                });
            }
            
        } catch (error) {
            // 在空白页面失败是预期的
            if (error.message.includes('空白') || error.message.includes('blank')) {
                testResults.passed.push({
                    test: '异常页面识别',
                    reason: '正确处理了空白页面错误'
                });
            } else {
                testResults.failed.push({
                    test: '异常页面识别',
                    error: error.message
                });
            }
        }
        
        console.log('✅ 测试2完成\n');
        
        // 测试3: 浏览器导航
        console.log('📋 测试3: 浏览器导航功能');
        const ticket3 = await createTicket({
            title: '测试浏览器导航',
            content: '导航到 https://www.example.com 并获取页面标题',
            type: 'rpa_task',
            priority: 'high',
            metadata: {
                targetUrl: 'https://www.example.com'
            }
        });
        
        const processor3 = new MultiAgentLangGraphProcessor();
        
        try {
            const result3 = await processor3.process(ticket3);
            
            // 检查是否成功导航
            const hasNavigation = JSON.stringify(result3).includes('example.com') ||
                                 JSON.stringify(result3).includes('Example Domain');
            
            if (hasNavigation) {
                testResults.passed.push({
                    test: '浏览器导航',
                    reason: '成功导航到目标网站'
                });
            } else {
                testResults.failed.push({
                    test: '浏览器导航',
                    reason: '未能导航到目标网站'
                });
            }
            
        } catch (error) {
            testResults.failed.push({
                test: '浏览器导航',
                error: error.message
            });
        }
        
        console.log('✅ 测试3完成\n');
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
    }
    
    // 生成测试报告
    const duration = Date.now() - testResults.startTime;
    
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试报告');
    console.log('='.repeat(50));
    console.log(`总耗时: ${(duration/1000).toFixed(2)}秒`);
    console.log(`通过: ${testResults.passed.length}`);
    console.log(`失败: ${testResults.failed.length}`);
    
    if (testResults.passed.length > 0) {
        console.log('\n✅ 通过的测试:');
        testResults.passed.forEach(test => {
            console.log(`  - ${test.test}: ${test.reason}`);
        });
    }
    
    if (testResults.failed.length > 0) {
        console.log('\n❌ 失败的测试:');
        testResults.failed.forEach(test => {
            console.log(`  - ${test.test}: ${test.reason || test.error}`);
        });
    }
    
    console.log('\n' + '='.repeat(50));
    
    // 返回测试结果
    return {
        success: testResults.failed.length === 0,
        summary: {
            total: testResults.passed.length + testResults.failed.length,
            passed: testResults.passed.length,
            failed: testResults.failed.length,
            duration: duration
        },
        details: testResults
    };
}

// 执行测试
if (require.main === module) {
    runFixValidationTest()
        .then(result => {
            if (result.success) {
                console.log('\n🎉 所有修复验证测试通过！');
                process.exit(0);
            } else {
                console.log('\n⚠️ 部分测试失败，请检查修复效果');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('\n🔥 测试执行异常:', error);
            process.exit(1);
        });
}

module.exports = { runFixValidationTest };