/**
 * MCP客户端集成测试
 * 验证浏览器操作的实际执行效果和系统稳定性
 */

require('dotenv').config();
const logger = require('./src/ai-assistant/src/utils/logger');
const TrueMCPExecutor = require('./src/ai-assistant/src/execution/true-mcp-executor');
const IntelligentValidationEngine = require('./src/ai-assistant/src/langgraph/nodes/intelligent-validation-engine');
const EdgeCaseHandler = require('./src/ai-assistant/src/langgraph/nodes/edge-case-handler');

// 模拟LLM客户端
class MockLLMClient {
    async chat(prompt) {
        // 根据不同的提示词类型返回不同的响应
        if (prompt.includes('L2功能层验证')) {
            return JSON.stringify({
                goalRelevance: 0.85,
                stateConsistency: 0.90,
                sideEffectCheck: 0.95,
                progressAssessment: 0.78,
                score: 0.87,
                reason: "业务操作执行成功，符合预期目标",
                suggestions: "继续当前执行策略"
            });
        } else if (prompt.includes('L3智能层验证')) {
            return JSON.stringify({
                completionAssessment: 0.75,
                pathOptimality: 0.82,
                contextCoherence: 0.88,
                completionProbability: 0.81,
                score: 0.82,
                reason: "任务进展良好，上下文连贯性强",
                nextStepRecommendation: "CONTINUE",
                strategicAdvice: "维持当前执行策略，继续推进"
            });
        } else if (prompt.includes('RPA任务规划器')) {
            return JSON.stringify({
                intent: "NAVIGATE",
                parameters: { url: "https://uat-merchant.aomiapp.com/#/bdlogin" },
                reasoning: "首先导航到登录页面开始任务",
                confidence: 0.9
            });
        }
        
        // 默认响应
        return JSON.stringify({
            intent: "SCREENSHOT",
            parameters: {},
            reasoning: "截图观察当前页面状态",
            confidence: 0.7
        });
    }
}

// MCP客户端集成测试类
class MCPIntegrationTest {
    constructor() {
        this.llmClient = new MockLLMClient();
        this.mcpExecutor = new TrueMCPExecutor();
        this.validationEngine = new IntelligentValidationEngine({
            llmClient: this.llmClient,
            mcpClient: this.mcpExecutor.mcpClient
        });
        this.edgeHandler = new EdgeCaseHandler({
            llmClient: this.llmClient,
            mcpClient: this.mcpExecutor.mcpClient
        });
        
        this.testResults = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            testDetails: []
        };
    }
    
    /**
     * 运行完整的MCP集成测试套件
     */
    async runComprehensiveTest() {
        try {
            logger.info('🚀 开始MCP客户端集成测试');
            
            console.log('\n=== MCP客户端集成测试 ===\n');
            
            // 测试MCP客户端基础功能
            await this.testMCPClientBasics();
            
            // 测试浏览器操作序列
            await this.testBrowserOperationSequence();
            
            // 测试智能验证引擎集成
            await this.testValidationEngineIntegration();
            
            // 测试边缘场景处理集成
            await this.testEdgeCaseHandlerIntegration();
            
            // 测试错误恢复机制
            await this.testErrorRecoveryMechanisms();
            
            // 测试性能和稳定性
            await this.testPerformanceAndStability();
            
            // 输出测试结果
            this.outputTestSummary();
            
        } catch (error) {
            logger.error('MCP集成测试执行失败:', error);
            throw error;
        }
    }
    
    /**
     * 测试MCP客户端基础功能
     */
    async testMCPClientBasics() {
        console.log('📋 测试MCP客户端基础功能...');
        
        const testCases = [
            {
                name: '浏览器截图功能',
                operation: 'browser_screenshot',
                params: {}
            },
            {
                name: '浏览器快照功能',
                operation: 'browser_snapshot',
                params: {}
            },
            {
                name: '浏览器导航功能',
                operation: 'browser_navigate',
                params: { url: 'https://uat-merchant.aomiapp.com/#/bdlogin' }
            }
        ];
        
        for (const testCase of testCases) {
            try {
                const startTime = Date.now();
                const result = await this.mcpExecutor.mcpClient.callTool(
                    testCase.operation, 
                    testCase.params
                );
                const duration = Date.now() - startTime;
                
                const passed = result !== null && result !== undefined;
                this.recordTestResult(`MCP基础-${testCase.name}`, passed, {
                    operation: testCase.operation,
                    duration: duration,
                    resultLength: typeof result === 'string' ? result.length : 0,
                    hasResult: !!result
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${testCase.name}: ${duration}ms`);
                
                // 操作间隔等待
                await this.sleep(2000);
                
            } catch (error) {
                this.recordTestResult(`MCP基础-${testCase.name}`, false, { 
                    error: error.message,
                    operation: testCase.operation
                });
                console.log(`  ❌ ${testCase.name}: ${error.message}`);
            }
        }
    }
    
    /**
     * 测试浏览器操作序列
     */
    async testBrowserOperationSequence() {
        console.log('\n📋 测试浏览器操作序列...');
        
        const operationSequence = [
            {
                name: '导航到登录页面',
                operation: 'browser_navigate',
                params: { url: 'https://uat-merchant.aomiapp.com/#/bdlogin' },
                waitAfter: 3000
            },
            {
                name: '页面加载后截图',
                operation: 'browser_screenshot',
                params: {},
                waitAfter: 1000
            },
            {
                name: '获取页面快照',
                operation: 'browser_snapshot',
                params: {},
                waitAfter: 1000
            },
            {
                name: '最终状态截图',
                operation: 'browser_screenshot',
                params: {},
                waitAfter: 1000
            }
        ];
        
        let sequenceSuccess = true;
        const sequenceResults = [];
        
        for (const [index, operation] of operationSequence.entries()) {
            try {
                console.log(`    ${index + 1}. ${operation.name}...`);
                
                const startTime = Date.now();
                const result = await this.mcpExecutor.mcpClient.callTool(
                    operation.operation, 
                    operation.params
                );
                const duration = Date.now() - startTime;
                
                const stepSuccess = result !== null && result !== undefined;
                sequenceResults.push({
                    step: operation.name,
                    success: stepSuccess,
                    duration: duration,
                    resultSize: typeof result === 'string' ? result.length : 0
                });
                
                if (!stepSuccess) {
                    sequenceSuccess = false;
                    console.log(`       ❌ 失败: 无结果返回`);
                } else {
                    console.log(`       ✅ 成功: ${duration}ms`);
                }
                
                // 等待指定时间
                if (operation.waitAfter) {
                    await this.sleep(operation.waitAfter);
                }
                
            } catch (error) {
                sequenceSuccess = false;
                sequenceResults.push({
                    step: operation.name,
                    success: false,
                    error: error.message,
                    duration: 0
                });
                console.log(`       ❌ 异常: ${error.message}`);
            }
        }
        
        this.recordTestResult('浏览器操作序列', sequenceSuccess, {
            totalSteps: operationSequence.length,
            successfulSteps: sequenceResults.filter(r => r.success).length,
            sequenceResults: sequenceResults
        });
        
        console.log(`  ${sequenceSuccess ? '✅' : '❌'} 操作序列完成`);
    }
    
    /**
     * 测试智能验证引擎集成
     */
    async testValidationEngineIntegration() {
        console.log('\n📋 测试智能验证引擎集成...');
        
        const testCases = [
            {
                name: '成功操作的三层验证',
                plan: {
                    intent: 'NAVIGATE',
                    parameters: { url: 'https://uat-merchant.aomiapp.com/#/bdlogin' },
                    reasoning: '导航到登录页面'
                },
                evidence: {
                    success: true,
                    executionResult: { action: 'navigate', url: 'https://uat-merchant.aomiapp.com/#/bdlogin' },
                    duration: 2000
                },
                state: {
                    taskGoal: '登录系统',
                    cycleCount: 1,
                    maxCycles: 5
                }
            },
            {
                name: '失败操作的三层验证',
                plan: {
                    intent: 'CLICK',
                    parameters: { element: '不存在的按钮', ref: 'non-existent-btn' },
                    reasoning: '点击不存在的按钮'
                },
                evidence: {
                    success: false,
                    error: { message: '元素未找到' },
                    duration: 5000
                },
                state: {
                    taskGoal: '点击按钮',
                    cycleCount: 2,
                    maxCycles: 5
                }
            }
        ];
        
        for (const testCase of testCases) {
            try {
                const startTime = Date.now();
                const validationResult = await this.validationEngine.performThreeLayerValidation(
                    testCase.plan,
                    testCase.evidence,
                    testCase.state
                );
                const duration = Date.now() - startTime;
                
                const passed = validationResult && validationResult.score >= 0;
                this.recordTestResult(`验证引擎-${testCase.name}`, passed, {
                    validationStatus: validationResult.status,
                    validationScore: validationResult.score,
                    confidence: validationResult.confidence,
                    l1Passed: validationResult.layerResults?.l1?.passed,
                    l2Passed: validationResult.layerResults?.l2?.passed,
                    l3Passed: validationResult.layerResults?.l3?.passed,
                    duration: duration
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${testCase.name}: 状态=${validationResult.status}, 分数=${validationResult.score?.toFixed(2)}`);
                
            } catch (error) {
                this.recordTestResult(`验证引擎-${testCase.name}`, false, { error: error.message });
                console.log(`  ❌ ${testCase.name}: 测试异常 - ${error.message}`);
            }
        }
    }
    
    /**
     * 测试边缘场景处理集成
     */
    async testEdgeCaseHandlerIntegration() {
        console.log('\n📋 测试边缘场景处理集成...');
        
        const testCases = [
            {
                name: '页面加载动态等待',
                context: { type: 'PAGE_LOAD', maxWait: 5000 },
                testType: 'dynamicWait'
            },
            {
                name: '元素可见性等待',
                context: { type: 'ELEMENT_VISIBLE', selector: '#login-form', maxWait: 3000 },
                testType: 'dynamicWait'
            },
            {
                name: '网络错误恢复',
                error: { message: '网络连接超时', type: 'NETWORK_ERROR' },
                context: { currentNode: 'execution_node', ticketId: 'test_123' },
                testType: 'errorRecovery'
            },
            {
                name: '元素未找到错误恢复',
                error: { message: '元素未找到', type: 'ELEMENT_ERROR' },
                context: { currentNode: 'execution_node', ticketId: 'test_123' },
                testType: 'errorRecovery'
            }
        ];
        
        for (const testCase of testCases) {
            try {
                const startTime = Date.now();
                let result;
                
                if (testCase.testType === 'dynamicWait') {
                    result = await this.edgeHandler.performDynamicWait(testCase.context);
                } else if (testCase.testType === 'errorRecovery') {
                    result = await this.edgeHandler.performErrorRecovery(testCase.error, testCase.context);
                }
                
                const duration = Date.now() - startTime;
                
                const passed = result && (result.satisfied !== false) && (result.success !== false);
                this.recordTestResult(`边缘场景-${testCase.name}`, passed, {
                    testType: testCase.testType,
                    duration: duration,
                    result: result,
                    satisfied: result?.satisfied,
                    success: result?.success
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${testCase.name}: ${duration}ms`);
                
            } catch (error) {
                this.recordTestResult(`边缘场景-${testCase.name}`, false, { error: error.message });
                console.log(`  ❌ ${testCase.name}: 测试异常 - ${error.message}`);
            }
        }
    }
    
    /**
     * 测试错误恢复机制
     */
    async testErrorRecoveryMechanisms() {
        console.log('\n📋 测试错误恢复机制...');
        
        const errorScenarios = [
            {
                name: '模拟导航超时',
                operation: async () => {
                    // 模拟超时错误
                    throw new Error('Navigation timeout after 30000ms');
                },
                expectedRecovery: true
            },
            {
                name: '模拟点击失败',
                operation: async () => {
                    // 模拟点击失败
                    throw new Error('Element not found: #submit-button');
                },
                expectedRecovery: true
            },
            {
                name: '模拟网络错误',
                operation: async () => {
                    // 模拟网络错误
                    throw new Error('Network connection failed');
                },
                expectedRecovery: true
            }
        ];
        
        for (const scenario of errorScenarios) {
            try {
                console.log(`    测试: ${scenario.name}`);
                
                let operationError;
                try {
                    await scenario.operation();
                } catch (error) {
                    operationError = error;
                }
                
                if (operationError) {
                    const recoveryResult = await this.edgeHandler.performErrorRecovery(
                        operationError,
                        { currentNode: 'test_node', ticketId: 'test_recovery' }
                    );
                    
                    const recoveryWorked = recoveryResult && (recoveryResult.success !== false);
                    this.recordTestResult(`错误恢复-${scenario.name}`, recoveryWorked, {
                        originalError: operationError.message,
                        recoveryResult: recoveryResult,
                        recoverySuccess: recoveryResult?.success
                    });
                    
                    console.log(`       ${recoveryWorked ? '✅' : '❌'} 恢复${recoveryWorked ? '成功' : '失败'}`);
                } else {
                    console.log(`       ⚠️ 未产生预期错误`);
                }
                
            } catch (error) {
                this.recordTestResult(`错误恢复-${scenario.name}`, false, { error: error.message });
                console.log(`       ❌ 测试异常: ${error.message}`);
            }
        }
    }
    
    /**
     * 测试性能和稳定性
     */
    async testPerformanceAndStability() {
        console.log('\n📋 测试性能和稳定性...');
        
        const performanceTests = [
            {
                name: '连续截图性能',
                testFunc: () => this.testContinuousScreenshots(5)
            },
            {
                name: '快速操作序列',
                testFunc: () => this.testRapidOperationSequence()
            },
            {
                name: '内存使用稳定性',
                testFunc: () => this.testMemoryStability()
            }
        ];
        
        for (const test of performanceTests) {
            try {
                const startTime = Date.now();
                const result = await test.testFunc();
                const duration = Date.now() - startTime;
                
                const passed = result && duration < 30000; // 30秒内完成
                this.recordTestResult(`性能测试-${test.name}`, passed, {
                    duration: duration,
                    result: result
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${test.name}: ${duration}ms`);
                
            } catch (error) {
                this.recordTestResult(`性能测试-${test.name}`, false, { error: error.message });
                console.log(`  ❌ ${test.name}: 测试异常 - ${error.message}`);
            }
        }
    }
    
    /**
     * 测试连续截图性能
     */
    async testContinuousScreenshots(count) {
        const results = [];
        
        for (let i = 0; i < count; i++) {
            try {
                const startTime = Date.now();
                const screenshot = await this.mcpExecutor.mcpClient.callTool('browser_screenshot', {});
                const duration = Date.now() - startTime;
                
                results.push({
                    index: i,
                    success: !!screenshot,
                    duration: duration,
                    size: screenshot ? screenshot.length : 0
                });
                
                // 短暂间隔
                await this.sleep(500);
                
            } catch (error) {
                results.push({
                    index: i,
                    success: false,
                    error: error.message,
                    duration: 0
                });
            }
        }
        
        const successfulScreenshots = results.filter(r => r.success).length;
        const averageDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
        
        return {
            total: count,
            successful: successfulScreenshots,
            successRate: successfulScreenshots / count,
            averageDuration: averageDuration,
            results: results
        };
    }
    
    /**
     * 测试快速操作序列
     */
    async testRapidOperationSequence() {
        const operations = [
            { tool: 'browser_screenshot', params: {} },
            { tool: 'browser_snapshot', params: {} },
            { tool: 'browser_screenshot', params: {} },
            { tool: 'browser_snapshot', params: {} }
        ];
        
        const results = [];
        
        for (const operation of operations) {
            try {
                const startTime = Date.now();
                const result = await this.mcpExecutor.mcpClient.callTool(operation.tool, operation.params);
                const duration = Date.now() - startTime;
                
                results.push({
                    tool: operation.tool,
                    success: !!result,
                    duration: duration
                });
                
                // 极短间隔
                await this.sleep(200);
                
            } catch (error) {
                results.push({
                    tool: operation.tool,
                    success: false,
                    error: error.message,
                    duration: 0
                });
            }
        }
        
        const successful = results.filter(r => r.success).length;
        return {
            total: operations.length,
            successful: successful,
            successRate: successful / operations.length,
            results: results
        };
    }
    
    /**
     * 测试内存使用稳定性
     */
    async testMemoryStability() {
        const startMemory = process.memoryUsage();
        
        // 执行一系列操作
        for (let i = 0; i < 10; i++) {
            try {
                await this.mcpExecutor.mcpClient.callTool('browser_screenshot', {});
                await this.mcpExecutor.mcpClient.callTool('browser_snapshot', {});
                await this.sleep(100);
            } catch (error) {
                // 忽略错误，关注内存使用
            }
        }
        
        const endMemory = process.memoryUsage();
        const memoryIncrease = endMemory.heapUsed - startMemory.heapUsed;
        
        return {
            startMemory: startMemory.heapUsed,
            endMemory: endMemory.heapUsed,
            memoryIncrease: memoryIncrease,
            memoryIncreasesMB: memoryIncrease / 1024 / 1024,
            acceptable: memoryIncrease < 50 * 1024 * 1024 // 50MB以内
        };
    }
    
    /**
     * 记录测试结果
     */
    recordTestResult(testName, passed, details = {}) {
        this.testResults.totalTests++;
        if (passed) {
            this.testResults.passedTests++;
        } else {
            this.testResults.failedTests++;
        }
        
        this.testResults.testDetails.push({
            name: testName,
            passed: passed,
            details: details,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * 输出测试总结
     */
    outputTestSummary() {
        console.log('\n=== MCP集成测试总结 ===');
        console.log(`总测试数: ${this.testResults.totalTests}`);
        console.log(`通过: ${this.testResults.passedTests}`);
        console.log(`失败: ${this.testResults.failedTests}`);
        console.log(`通过率: ${(this.testResults.passedTests / this.testResults.totalTests * 100).toFixed(1)}%`);
        
        // 输出失败的测试详情
        const failedTests = this.testResults.testDetails.filter(test => !test.passed);
        if (failedTests.length > 0) {
            console.log('\n失败的测试:');
            failedTests.forEach(test => {
                console.log(`  ❌ ${test.name}: ${test.details.error || '检查详细信息'}`);
            });
        }
        
        // 输出系统统计
        console.log('\n=== 系统统计 ===');
        try {
            const edgeStats = this.edgeHandler.getEdgeStats();
            const validationStats = this.validationEngine.getValidationStats();
            
            console.log(`边缘场景处理: 恢复成功率 ${(edgeStats.recoverySuccessRate * 100).toFixed(1)}%`);
            console.log(`验证引擎: 总验证次数 ${validationStats.totalValidations}`);
            console.log(`动态等待: 总等待次数 ${edgeStats.dynamicWaits}`);
        } catch (error) {
            console.log('系统统计获取失败:', error.message);
        }
        
        logger.info('🎉 MCP客户端集成测试完成', {
            totalTests: this.testResults.totalTests,
            passedTests: this.testResults.passedTests,
            failedTests: this.testResults.failedTests,
            passRate: (this.testResults.passedTests / this.testResults.totalTests * 100).toFixed(1)
        });
    }
    
    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 运行测试
if (require.main === module) {
    const test = new MCPIntegrationTest();
    test.runComprehensiveTest().then(() => {
        console.log('\n✅ MCP集成测试执行完成');
        process.exit(0);
    }).catch(error => {
        console.error('\n❌ MCP集成测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = MCPIntegrationTest;