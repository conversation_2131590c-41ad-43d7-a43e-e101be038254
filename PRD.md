
# 项目需求文档 (PRD) - AI驱动的RPA自动化平台 v2.0

## 更新说明
- **版本**: 2.0
- **更新日期**: 2025-01-13
- **主要变更**: 采用多Agent架构，完全摒弃硬编码执行逻辑，实现真正的AI驱动RPA

## 1. 项目概述

### 1.1. 项目目标

构建一个完全由AI驱动的RPA（机器人流程自动化）平台，通过多个协同工作的AI Agent来理解、规划和执行自动化任务。系统能够智能地处理各种管理后台操作，无需硬编码任何业务逻辑，真正实现自适应的流程自动化。

### 1.2. 核心特性

- **多Agent协同**: 5个专门化的AI Agent分工合作，各司其职
- **零硬编码**: 所有决策和执行逻辑完全由AI动态生成
- **自适应能力**: 能够处理未见过的页面和流程
- **智能决策**: 基于实时观察和理解做出最优决策
- **人机协作**: 智能识别需要人工协助的场景

### 1.3. 系统架构

```mermaid
graph TB
    A[工单系统] -->|提交任务| B[多Agent RPA系统]
    B --> C[总控Agent]
    C --> D[观察Agent]
    C --> E[决策Agent]
    C --> F[执行Agent]
    C --> G[验证Agent]
    D -->|页面分析| E
    E -->|执行计划| F
    F -->|操作结果| G
    G -->|验证反馈| C
    B -->|更新状态| A
```

---

## 2. 多Agent系统设计

### 2.1. Agent架构概览

系统采用5个专门化的AI Agent，通过LangGraph工作流引擎协调工作：

| Agent | 职责 | 核心能力 |
|-------|------|----------|
| **总控Agent** | 任务理解与协调 | 任务分解、Agent调度、进度监控 |
| **观察Agent** | 页面状态分析 | 页面理解、元素识别、变化检测 |
| **决策Agent** | 策略制定 | 路径规划、风险评估、策略优化 |
| **执行Agent** | 操作执行 | 元素定位、精确操作、异常处理 |
| **验证Agent** | 结果验证 | 多层验证、完成度评估、异常检测 |

### 2.2. Agent详细设计

#### 2.2.1. 总控Agent (Orchestrator Agent)

**主要职责**：
- 理解和分析工单需求
- 将复杂任务分解为可执行步骤
- 协调其他Agent的工作
- 监控整体执行进度
- 处理异常情况和人工介入请求

**工作流程**：
1. 接收并分析工单
2. 制定执行策略（非硬编码）
3. 循环调度其他Agent
4. 评估执行结果
5. 决定下一步行动

#### 2.2.2. 观察Agent (Observer Agent)

**主要职责**：
- 深入分析页面DOM结构
- 理解页面的业务含义
- 识别所有可交互元素
- 检测页面动态变化
- 提供结构化的页面信息

**观察重点**：
- URL和页面标题
- 输入框、按钮、链接等交互元素
- 文本内容和提示信息
- 弹窗、下拉菜单等动态元素
- 页面加载状态

#### 2.2.3. 决策Agent (Decision Agent)

**主要职责**：
- 基于当前状态制定行动计划
- 评估多种可能的执行路径
- 选择最优策略
- 制定失败恢复方案
- 处理特殊交互模式

**决策原则**：
- 目标导向：始终以完成任务为目标
- 风险评估：优先选择低风险操作
- 效率优化：选择最直接的路径
- 适应性：根据实际情况调整策略

#### 2.2.4. 执行Agent (Executor Agent)

**主要职责**：
- 将决策转化为具体的浏览器操作
- 精确定位目标元素
- 控制操作时序和节奏
- 处理操作异常
- 收集执行证据

**执行策略**：
- 多种元素定位方法（ID、文本、属性、位置等）
- 智能等待机制
- 错误重试策略
- 证据收集（截图、日志等）

#### 2.2.5. 验证Agent (Validator Agent)

**主要职责**：
- 验证单个操作的结果
- 评估业务步骤的完成度
- 判断整体任务是否达成
- 识别异常和错误
- 提供改进建议

**验证层次**：
- 操作级：单个动作是否成功
- 步骤级：业务步骤是否完成
- 任务级：整体目标是否达成

---

## 3. 核心功能规格

### 3.1. 工单系统

工单系统保持原有的基本结构，但与AI助手的集成方式完全改变为多Agent协同模式。

#### 3.1.1. 功能需求

##### 3.1.1.1. 工单列表页

| 模块 | 功能/字段 | 描述 |
| :--- | :--- | :--- |
| **筛选** | 工单号 | 支持部分匹配查询。 |
| | 内容 | 支持对工单内容进行模糊搜索。 |
| | 状态 | 支持按工单状态（待开始、处理中、待补充信息、已完成、已挂起）进行筛选。 |
| **操作** | 新建工单 | 点击后弹出新建工单窗口。 |
| | 批量设置状态 | 允许用户选择多个工单并统一变更其状态。 |
| **列表字段** | 序号 | 列表的顺序编号。 |
| | 工单号 | 唯一标识一个工单。 |
| | 工单内容 | 仅显示一行的摘要信息。 |
| | 工单状态 | 当前工单的处理状态。 |
| | Agent状态 | 显示当前活跃的Agent及其工作状态。 |
| | 执行进度 | 实时显示Agent执行循环次数和决策路径。 |
| | 处理时间 | 已完成的工单显示创建到完成的时间差；未完成的显示创建到当前的时间差（单位：小时）。 |
| | 创建日期 | 工单的创建时间。 |
| | 更新日期 | 工单最后一次更新的时间。 |
| | 操作 | 查看、编辑、设置状态、删除（仅"待开始"、"已挂起"、"已完成"状态的工单可删除）。 |

##### 3.1.1.2. 新建/编辑/查看工单详情页 (弹窗)

| 字段/功能 | 描述 |
| :--- | :--- |
| **工单号** | 仅在编辑和查看时显示。 |
| **工单状态** | 下拉选择工单状态。 |
| **内容** | 支持Markdown语法的富文本编辑器。 |
| **Agent执行日志** | 实时显示各Agent的决策过程和执行结果。 |
| **人机交互记录** | 记录Agent请求人工协助的历史和用户响应。 |
| **完单总结** | 工单完成后由验证Agent生成的综合报告。 |
| **执行证据** | 包含关键操作的截图、页面状态快照等。 |
| **功能按钮** | 保存、取消、人工介入。在查看模式下，仅显示"关闭"按钮。 |

#### 3.1.2. 工单状态流转

```mermaid
stateDiagram-v2
    [*] --> 待开始
    待开始 --> 已挂起: 用户操作
    待开始 --> 处理中: 多Agent系统接管
    处理中 --> 已挂起: 用户操作
    处理中 --> 待补充信息: Agent请求协助
    处理中 --> 已完成: Agent验证通过
    待补充信息 --> 已挂起: 用户操作
    待补充信息 --> 处理中: 用户提供信息
    已挂起 --> 待开始: 用户操作
```

| 状态 | 描述 | 触发条件 | 用户可执行操作 |
| :--- | :--- | :--- | :--- |
| **待开始** | 新建工单或用户补充信息后 | 系统初始状态 | 查看、编辑、删除、更改为"已挂起" |
| **处理中** | 多Agent系统正在协同工作 | Agent系统接管 | 查看、更改为"已挂起"（需二次确认）、人工介入 |
| **待补充信息** | Agent识别需要人工协助 | 决策Agent判断 | 查看、编辑、提供信息、更改为"已挂起" |
| **已完成** | Agent验证任务完成 | 验证Agent确认 | 查看、删除 |
| **已挂起** | 用户暂停工单 | 用户主动操作 | 查看、编辑、删除、更改为"待开始" |

---

### 3.2. 多Agent RPA系统

#### 3.2.1. Agent执行流程

```mermaid
graph TB
    A[工单输入] --> B[总控Agent分析]
    B --> C{循环开始}
    C --> D[观察Agent获取页面状态]
    D --> E[决策Agent制定策略]
    E --> F[执行Agent操作页面]
    F --> G[验证Agent检查结果]
    G --> H{任务完成?}
    H -->|否| I{需要人工?}
    I -->|是| J[请求人工协助]
    I -->|否| C
    H -->|是| K[生成执行报告]
    J --> L[等待用户响应]
    L --> C
```

#### 3.2.2. Agent交互协议

每个Agent之间通过标准化的消息格式进行通信：

```javascript
{
    "from": "agent_name",
    "to": "agent_name",
    "type": "request|response|notification",
    "timestamp": "ISO 8601 timestamp",
    "content": {
        "action": "specific_action",
        "data": {},
        "context": {},
        "confidence": 0.95
    }
}
```

#### 3.2.3. 执行保障机制

1. **智能重试机制**：每个Agent都具备错误识别和重试能力
2. **断点续传**：通过LangGraph的checkpoint机制保存执行状态
3. **人机协作**：Agent主动识别需要人工介入的场景
4. **证据链**：每个关键操作都保留执行证据（截图、日志等）

---

## 4. 技术规格

### 4.1. 系统架构

```
多Agent RPA系统
├── Agent层
│   ├── 总控Agent (Orchestrator)
│   ├── 观察Agent (Observer)
│   ├── 决策Agent (Decision)
│   ├── 执行Agent (Executor)
│   └── 验证Agent (Validator)
├── 协调层
│   ├── Agent协调器 (Agent Coordinator)
│   └── LangGraph工作流引擎
├── 基础设施层
│   ├── MCP浏览器控制 (Playwright)
│   ├── LLM服务 (阿里云百炼)
│   └── 状态持久化 (SQLite)
└── 接口层
    ├── 工单系统API
    └── WebSocket实时通信
```

### 4.2. 技术栈

- **Agent框架**: 基于LangGraph的多Agent架构
- **LLM服务**: 阿里云百炼 (qwen-plus / qwen-vl-plus)
- **浏览器自动化**: Playwright with MCP
- **后端**: Node.js + Express
- **数据库**: SQLite
- **通信**: WebSocket
- **前端**: Vue 3 + Element Plus

### 4.3. Agent技术规格

#### 4.3.1. Agent基础能力

每个Agent都具备以下基础能力：
- **LLM交互**: 通过统一接口调用大语言模型
- **上下文管理**: 维护执行上下文和历史记录
- **错误处理**: 智能识别和处理异常情况
- **日志记录**: 详细记录决策过程和执行结果

#### 4.3.2. Agent专有能力

| Agent | 专有能力 | 技术实现 |
|-------|---------|----------|
| **总控Agent** | 任务分解、流程编排 | 基于Chain-of-Thought的任务分析 |
| **观察Agent** | 页面理解、变化检测 | DOM分析 + 语义理解 |
| **决策Agent** | 策略制定、路径规划 | 强化学习 + 规则引擎 |
| **执行Agent** | 精确操作、时序控制 | MCP协议 + 智能等待 |
| **验证Agent** | 多层验证、结果判定 | 视觉验证 + 逻辑验证 |

### 4.4. 大模型配置

```env
# 主模型（用于Agent决策）
MAIN_MODEL=qwen-plus
MAIN_MODEL_TEMPERATURE=0.7
MAIN_MODEL_MAX_TOKENS=2000

# 视觉模型（用于页面理解和验证）
VL_MODEL=qwen-vl-plus
VL_MODEL_TEMPERATURE=0.3
VL_MODEL_MAX_TOKENS=1000

# API配置
DASHSCOPE_API_KEY=your-api-key
BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
```

### 4.5. Agent提示词架构

```
src/ai-assistant/prompts/
├── agents/
│   ├── orchestrator-prompt.md    # 总控Agent提示词
│   ├── observer-prompt.md        # 观察Agent提示词
│   ├── decision-prompt.md        # 决策Agent提示词
│   ├── executor-prompt.md        # 执行Agent提示词
│   └── validator-prompt.md       # 验证Agent提示词
├── shared/
│   ├── operation-patterns.md     # 通用操作模式
│   ├── system-capabilities.md    # 系统能力说明
│   └── error-handling.md         # 错误处理指南
└── templates/
    ├── task-analysis.md          # 任务分析模板
    └── report-generation.md      # 报告生成模板
```

### 4.6. 数据持久化

#### 4.6.1. Agent执行状态表

```sql
CREATE TABLE agent_execution_states (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    cycle_number INTEGER NOT NULL,
    input_data TEXT,
    output_data TEXT,
    decision_reason TEXT,
    confidence_score REAL,
    execution_time INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4.6.2. Agent交互记录表

```sql
CREATE TABLE agent_interactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_id TEXT NOT NULL,
    from_agent TEXT NOT NULL,
    to_agent TEXT NOT NULL,
    message_type TEXT NOT NULL,
    message_content TEXT,
    response_content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 5. 运行机制

### 5.1. Agent生命周期

1. **初始化**: Agent加载专属提示词和配置
2. **激活**: 接收来自协调器的任务
3. **执行**: 调用LLM进行决策和操作
4. **通信**: 与其他Agent交换信息
5. **休眠**: 完成任务后等待下次调用

### 5.2. 决策透明度

系统提供完整的决策追踪：
- 每个Agent的思考过程
- 决策的置信度评分
- 选择特定策略的原因
- 失败时的诊断信息

### 5.3. 性能优化

1. **并行处理**: 观察和决策可以并行进行
2. **缓存机制**: 相似页面的理解结果可复用
3. **智能等待**: 基于页面特征动态调整等待时间
4. **资源管理**: 自动释放不活跃的浏览器实例

---

## 6. 安全与合规

### 6.1. 数据安全

- 所有敏感信息通过环境变量管理
- Agent之间的通信采用内部协议
- 执行日志脱敏处理

### 6.2. 操作安全

- Agent具有操作边界意识
- 关键操作需要多Agent确认
- 支持操作回滚和恢复

### 6.3. 审计追踪

- 完整的Agent决策日志
- 所有操作的时间戳记录
- 关键步骤的证据保存

---
