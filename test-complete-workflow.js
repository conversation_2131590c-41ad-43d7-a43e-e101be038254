/**
 * 完整工单处理流程测试
 * 测试从工单创建到结单的完整流程
 */

require('dotenv').config();
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;
const LangGraphTaskProcessor = require('./src/ai-assistant/src/langgraph/langgraph-task-processor');
const logger = require('./src/ai-assistant/src/utils/logger');

class CompleteWorkflowTest {
    constructor() {
        this.dbPath = process.env.DATABASE_PATH || './data/rpa_platform.db';
        this.db = null;
        this.taskProcessor = new LangGraphTaskProcessor();
    }

    /**
     * 初始化测试环境
     */
    async initialize() {
        console.log('🚀 初始化测试环境...');
        
        // 初始化数据库连接
        await this.initDatabase();
        
        // 初始化任务处理器
        await this.taskProcessor.initialize();
        
        console.log('✅ 初始化完成\n');
    }

    /**
     * 初始化数据库连接
     */
    async initDatabase() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('📊 数据库连接成功');
                    resolve();
                }
            });
        });
    }

    /**
     * 创建测试工单
     */
    async createTestTicket() {
        const ticket = {
            ticket_number: `WO-${Date.now()}`,
            title: '上架外卖商品',
            content: '上架榮記豆腐麵食(官也街、招牌煲仔飯、嫩滑豆腐花)的外卖商品: 菜遠牛肉飯',
            priority: 2, // 高优先级
            status: '待处理',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        return new Promise((resolve, reject) => {
            const sql = `
                INSERT INTO tickets (
                    ticket_number, title, content, priority, 
                    status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `;
            
            this.db.run(sql, [
                ticket.ticket_number,
                ticket.title,
                ticket.content,
                ticket.priority,
                ticket.status,
                ticket.created_at,
                ticket.updated_at
            ], function(err) {
                if (err) {
                    reject(err);
                } else {
                    ticket.id = this.lastID;
                    console.log('📝 创建工单:', ticket.ticket_number);
                    resolve(ticket);
                }
            });
        });
    }

    /**
     * 监控工单处理进度
     */
    async monitorTicketProgress(ticketId) {
        return new Promise((resolve, reject) => {
            let checkCount = 0;
            const maxChecks = 60; // 最多检查60次（5分钟）
            
            const interval = setInterval(async () => {
                checkCount++;
                
                // 查询工单状态
                this.db.get(
                    'SELECT * FROM tickets WHERE id = ?',
                    [ticketId],
                    async (err, ticket) => {
                        if (err) {
                            clearInterval(interval);
                            reject(err);
                            return;
                        }

                        console.log(`⏳ [${checkCount}/${maxChecks}] 工单状态: ${ticket.status}`);

                        // 检查是否完成
                        if (ticket.status === '已完成' || ticket.status === '失败') {
                            clearInterval(interval);
                            
                            // 获取处理结果
                            const result = await this.getTicketResult(ticketId);
                            resolve({
                                ticket,
                                result
                            });
                        }

                        // 超时检查
                        if (checkCount >= maxChecks) {
                            clearInterval(interval);
                            reject(new Error('工单处理超时'));
                        }
                    }
                );
            }, 5000); // 每5秒检查一次
        });
    }

    /**
     * 获取工单处理结果
     */
    async getTicketResult(ticketId) {
        return new Promise((resolve, reject) => {
            // 查询任务执行记录
            this.db.all(
                'SELECT * FROM tasks WHERE ticket_id = ? ORDER BY created_at DESC',
                [ticketId],
                (err, tasks) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    // 查询操作日志
                    this.db.all(
                        'SELECT * FROM operation_logs WHERE ticket_id = ? ORDER BY created_at DESC LIMIT 10',
                        [ticketId],
                        (err, logs) => {
                            if (err) {
                                reject(err);
                                return;
                            }

                            resolve({
                                tasks,
                                logs,
                                screenshots: this.findScreenshots(ticketId)
                            });
                        }
                    );
                }
            );
        });
    }

    /**
     * 查找截图文件
     */
    findScreenshots(ticketId) {
        const screenshotDir = './data/screenshots';
        try {
            const files = require('fs').readdirSync(screenshotDir);
            return files.filter(file => file.includes(ticketId.toString()));
        } catch (error) {
            return [];
        }
    }

    /**
     * 生成结单总结
     */
    async generateCompletionSummary(ticket, result) {
        const summary = {
            ticket_number: ticket.ticket_number,
            title: ticket.title,
            status: ticket.status,
            completion_time: ticket.updated_at,
            
            execution_summary: {
                total_tasks: result.tasks.length,
                completed_tasks: result.tasks.filter(t => t.status === 'completed').length,
                failed_tasks: result.tasks.filter(t => t.status === 'failed').length
            },
            
            key_actions: result.logs.slice(0, 5).map(log => ({
                time: log.created_at,
                action: log.action,
                details: log.details
            })),
            
            screenshots: result.screenshots,
            
            ai_summary: await this.generateAISummary(ticket, result)
        };

        return summary;
    }

    /**
     * 使用AI生成处理总结
     */
    async generateAISummary(ticket, result) {
        try {
            const prompt = `
基于以下工单处理结果，生成简洁的处理总结：

工单内容：${ticket.content}
工单状态：${ticket.status}
执行任务数：${result.tasks.length}
关键操作：${result.logs.slice(0, 3).map(l => l.action).join('、')}

请用2-3句话总结处理过程和结果。`;

            const response = await this.taskProcessor.llmClient.chat(prompt);
            return response;
        } catch (error) {
            return '处理总结生成失败';
        }
    }

    /**
     * 运行完整测试
     */
    async runTest() {
        try {
            console.log('============================================================');
            console.log('完整工单处理流程测试');
            console.log('============================================================\n');

            // 1. 创建工单
            console.log('1️⃣ 创建测试工单...');
            const ticket = await this.createTestTicket();
            console.log('工单信息:', JSON.stringify(ticket, null, 2));
            console.log();

            // 2. 触发处理
            console.log('2️⃣ 开始处理工单...');
            this.taskProcessor.processTicket(ticket).catch(err => {
                console.error('处理错误:', err);
            });
            console.log();

            // 3. 监控进度
            console.log('3️⃣ 监控处理进度...');
            const completionData = await this.monitorTicketProgress(ticket.id);
            console.log();

            // 4. 生成结单总结
            console.log('4️⃣ 生成结单总结...');
            const summary = await this.generateCompletionSummary(
                completionData.ticket,
                completionData.result
            );
            console.log();

            // 5. 输出结果
            console.log('📊 处理结果总结');
            console.log('================');
            console.log(JSON.stringify(summary, null, 2));

            // 6. 保存总结到文件
            const summaryPath = `./data/summaries/${ticket.ticket_number}_summary.json`;
            await fs.mkdir(path.dirname(summaryPath), { recursive: true });
            await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2));
            console.log(`\n📄 总结已保存到: ${summaryPath}`);

            return true;

        } catch (error) {
            console.error('❌ 测试失败:', error);
            return false;
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        if (this.db) {
            this.db.close();
        }
        
        if (this.taskProcessor.simpleExecutor && this.taskProcessor.simpleExecutor.client) {
            await this.taskProcessor.simpleExecutor.client.close();
        }
    }
}

// 执行测试
async function main() {
    const test = new CompleteWorkflowTest();
    
    try {
        await test.initialize();
        const success = await test.runTest();
        
        console.log('\n' + '='.repeat(60));
        console.log('测试结果:', success ? '✅ 成功' : '❌ 失败');
        console.log('='.repeat(60));
        
        process.exit(success ? 0 : 1);
        
    } catch (error) {
        console.error('测试异常:', error);
        process.exit(1);
        
    } finally {
        await test.cleanup();
    }
}

// 启动测试
main();