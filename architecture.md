
# RPA 自动化平台 - 技术架构文档

**版本**: v2.0 - 多Agent架构
**更新时间**: 2025-01-13

## 1. 概述

本文档描述了RPA自动化平台的多Agent架构设计。该平台通过AI驱动的多Agent协同系统，实现了零硬编码的智能化业务流程自动化。

## 2. 架构设计原则

- **零硬编码**: 所有执行逻辑由AI动态生成，无预设流程
- **多Agent协同**: 专门化Agent分工合作，提高执行质量
- **自适应执行**: 能够处理未见过的页面和业务流程
- **智能容错**: 三层验证机制和PDCA循环确保成功率
- **人机协作**: 智能识别需要人工协助的场景
- **可观测性**: 全程监控和详细日志，便于调试和优化

## 3. 系统架构图

### v2.0 - 多Agent协同架构 (2025-07-13)

这是一个高层级的架构图，展示了系统的核心组件和交互流程。

```mermaid
graph TD
    subgraph "外部系统"
        A[工单系统]
        B[目标页面]
    end

    subgraph "AI Assistant (多Agent RPA核心)"
        C[Ticket Poller]
        D[Multi-Agent LangGraph Processor]
        
        subgraph "Agent层"
            E[Agent Coordinator]
            F[Orchestrator Agent<br/>🤖 总控]
            G[Observer Agent<br/>👁️ 观察]
            H[Decision Agent<br/>🧠 决策]
            I[Executor Agent<br/>⚙️ 执行]
            J[Validator Agent<br/>✅ 验证]
        end
        
        K[三层验证系统<br/>L1: DOM 验证<br/>L2: 业务验证<br/>L3: 视觉验证]
    end

    subgraph "浏览器控制"
        L[Enhanced Simple MCP Client]
        M[Playwright Browser]
    end

    subgraph "数据与状态"
        N[SQLite Database]
        O[LangGraph Workflow State]
        P[提示词模板库]
    end

    A --> C
    C --> D
    D --> E
    D -- "状态持久化" --> O
    O --> N
    
    E <--> F
    E <--> G
    E <--> H
    E <--> I
    E <--> J
    
    G -- "页面观察" --> B
    G -- "状态分析" --> E
    H -- "策略制定" --> E
    I -- "执行操作" --> L
    J -- "验证结果" --> K
    K -- "验证报告" --> E
    F -- "任务协调" --> E
    
    L --> M
    M --> B
    
    P -- "提示词" --> E

    style F fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#ccf,stroke:#333,stroke-width:2px
    style H fill:#cfc,stroke:#333,stroke-width:2px
    style I fill:#fcf,stroke:#333,stroke-width:2px
    style J fill:#ffc,stroke:#333,stroke-width:2px
    style K fill:#fcc,stroke:#333,stroke-width:2px
```

## 4. 模块详解

### 4.1. 工单系统

#### 4.1.1. 前端

- **技术栈**: 推荐使用 Vue.js 或 React.js 等现代前端框架，配合 Element Plus 或 Ant Design 等组件库，快速构建高质量的管理界面。
- **核心功能**:
    - 工单的增删改查界面。
    - 实时状态更新（通过 WebSocket 接收来自 AI 助手的通知）。
    - 富文本编辑器（集成 Markdown-It 或其他类似库）。
    - 文件上传（用于在工单中附加图片）。

#### 4.1.2. 后端

- **技术栈**: Node.js + Express.js 或 NestJS。
- **API**: 提供 RESTful API 供前端调用，用于工单管理、用户认证等。
- **数据库**: 推荐使用 SQLite（轻量级，适合演示）。
    - **数据表/集合**:
        - `tickets`: 存储工单信息（工单号、内容、状态、备注、总结等）。
        - `tasks`: 存储每个工单关联的待办任务列表及状态。
        - `users`: 用户信息及权限管理。
- **WebSocket 服务**: 建立与 AI 助手的长连接，用于实时推送工单更新和接收状态变更。

### 4.2. AI 助手 (多Agent系统)

AI助手是整个系统的“大脑”，采用多Agent协同架构。

#### 4.2.1. 多Agent系统

**核心Agent**:

1. **Orchestrator Agent (总控Agent)**
   - 统筹规划整个任务流程
   - 协调其他Agent的工作
   - 决定何时需要人工介入
   - 管理任务的优先级和执行顺序

2. **Observer Agent (观察Agent)**
   - 分析当前页面状态
   - 识别可操作元素
   - 检测页面变化
   - 提供环境上下文信息

3. **Decision Agent (决策Agent)**
   - 基于观察结果制定操作策略
   - 生成具体执行步骤
   - 处理异常情况的应对方案
   - 评估不同方案的可行性

4. **Executor Agent (执行Agent)**
   - 执行具体的浏览器操作
   - 调用MCP客户端进行页面交互
   - 处理操作超时和重试
   - 记录操作日志

5. **Validator Agent (验证Agent)**
   - 验证操作结果的正确性
   - 执行三层验证逻辑
   - 判断任务是否成功完成
   - 生成验证报告

#### 4.2.2. LangGraph工作流引擎

**工作流节点**:
1. **恢复检查节点**: 检查是否有需要恢复的任务
2. **任务队列节点**: 从工单获取待处理任务
3. **多Agent处理节点**: 调用Agent协同处理
4. **PDCA执行节点**: 循环执行直到成功
5. **验证节点**: 三层验证确保结果正确
6. **清理节点**: 更新状态和清理资源

**状态管理**:
- 使用SQLite持久化工作流状态
- 支持任务暂停/恢复
- 保存Agent决策历史
- 跟踪执行进度

#### 4.2.3. PDCA执行系统

**Plan (计划)**:
- Decision Agent分析任务需求
- 制定详细执行计划
- 识别潜在风险点

**Do (执行)**:
- Executor Agent执行操作
- 实时记录执行过程
- 处理异常情况

**Check (检查)**:
- Validator Agent验证结果
- 执行三层验证
- 生成验证报告

**Act (处理)**:
- Orchestrator Agent决策下一步
- 成功则继续，失败则重试
- 超过重试次数则请求人工介入

#### 4.2.4. 三层验证系统

**L1 基础层 (DOM验证)**:
- 检查元素是否存在
- 验证元素状态（可见、可点击等）
- 确认页面加载完成

**L2 功能层 (业务验证)**:
- 验证业务逻辑正确性
- 检查数据是否正确提交
- 确认操作结果符合预期

**L3 智能层 (AI视觉验证)**:
- 使用qwen-vl-plus分析截图
- 验证页面内容的正确性
- 识别意外情况和错误提示

#### 4.2.5. 提示词体系

**提示词组织结构**:
```
src/ai-assistant/prompts/
├── agents/          # Agent专属提示词
│   ├── orchestrator.md
│   ├── observer.md
│   ├── decision.md
│   ├── executor.md
│   └── validator.md
├── shared/          # 共享资源
│   ├── capability-references.json
│   ├── operation-modes.json
│   └── common-instructions.md
└── templates/       # 报告模板
    ├── validation-report.md
    └── execution-summary.md
```

**动态加载机制**:
- 每个Agent加载对应的提示词文件
- 根据任务类型选择合适的模板
- 支持热更新，无需重启服务

#### 4.2.6. MCP浏览器控制

**Enhanced Simple MCP Client**:
- 标准化的浏览器操作接口
- 支持常见操作：点击、输入、滚动、截图
- 内置等待机制和错误处理
- 与Playwright无缝集成

**操作日志**:
- 记录每个操作的详细信息
- 保存截图和页面快照
- 支持操作回放和调试

### 4.3. 通信机制

#### 4.3.1. 工单状态流转实现

工单状态流转遵循以下状态机逻辑：

```mermaid
stateDiagram-v2
    [*] --> 待开始
    待开始 --> 已挂起: 用户操作
    待开始 --> 处理中: AI助手开始处理
    处理中 --> 已挂起: 用户操作(需二次确认)
    处理中 --> 待补充信息: AI助手发现信息不足
    处理中 --> 已完成: AI助手完成所有任务
    待补充信息 --> 已挂起: 用户操作
    待补充信息 --> 待开始: 用户补充信息后
    已挂起 --> 待开始: 用户操作
    已完成 --> [*]: 工单生命周期结束
```

**状态流转规则**:
- 只有"待开始"、"已挂起"、"已完成"状态的工单可以删除
- "处理中"状态的工单用户只能查看和挂起（需二次确认）
- "待补充信息"状态仅由AI助手设置，用户补充信息后自动转为"待开始"
- AI助手按队列顺序处理"待开始"状态的工单

#### 4.3.2. 系统间通信

- **工单系统与 AI 助手**: 使用 WebSocket 进行双向实时通信。
    - **工单系统 -> AI 助手**: 推送新工单、用户操作（如“挂起”）。
    - **AI 助手 -> 工单系统**: 实时更新工单状态、任务进度、完单报告等。
- **AI 助手与大模型**: 通过 HTTPS 调用阿里云百炼的 API。

## 5. 技术栈

### 核心框架
- **后端框架**: Express.js
- **前端框架**: Vue 3 + Element Plus
- **状态管理**: Pinia
- **数据库**: SQLite
- **工作流引擎**: LangGraph
- **消息队列**: 内存队列（MessageQueue）
- **实时通信**: Socket.IO

### AI技术栈
- **主模型**: 阿里云百炼 qwen-plus（文本理解与决策）
- **视觉模型**: 阿里云百炼 qwen-vl-plus（截图验证）
- **Agent框架**: 自研多Agent协同系统
- **提示词管理**: 结构化提示词模板系统

### 自动化技术
- **浏览器自动化**: Playwright
- **MCP协议**: 标准化浏览器控制接口
- **截图与分析**: 自动截图 + AI视觉验证

## 6. 数据管理

### 数据库表结构

**核心表**:
- `tickets` - 工单信息
- `tasks` - 拆解后的任务
- `workflow_states` - LangGraph工作流状态
- `ai_workers` - AI工作线程状态
- `operation_logs` - 操作日志

### 状态持久化

- **工作流状态**: 使用SQLite保存LangGraph状态
- **Agent决策**: 记录每个Agent的决策历史
- **执行日志**: 详细记录操作过程和结果

### 配置管理

- **环境变量**: 通过`.env`文件管理敏感信息
- **提示词模板**: 动态加载`prompts/`目录下的文件
- **操作指南**: 作为共享资源供Agent参考

## 7. 关键设计特性

### 架构特性
1. **零硬编码**: 完全AI驱动的决策流程，无预设执行逻辑
2. **多Agent协同**: 专门化Agent分工合作，提高执行质量
3. **自适应执行**: 能够处理未见过的页面结构和业务流程
4. **智能容错**: 三层验证 + PDCA循环确保任务成功
5. **人机协作**: 智能识别需要人工介入的场景

### 技术优势
1. **LangGraph工作流**: 完整的状态管理和持久化
2. **提示词工程**: 结构化、模块化的提示词体系
3. **实时监控**: WebSocket + 日志系统提供全程监控
4. **灵活扩展**: 通过添加新Agent扩展能力
5. **标准化接口**: MCP协议确保操作的一致性

## 8. 部署架构

### 当前部署模式
单机部署：
- 工单系统前端: 端口 3000
- 工单系统后端: 端口 3001  
- AI 助手服务: 端口 3002
- 数据库: SQLite 本地文件

### 生产部署建议
1. **服务拆分**: 将Agent服务独立部署，支持横向扩展
2. **数据库升级**: 迁移到PostgreSQL或MySQL
3. **消息队列**: 引入Redis/RabbitMQ替代内存队列
4. **负载均衡**: 多实例部署 + Nginx负载均衡
5. **监控系统**: 集成Prometheus + Grafana

## 9. 项目结构

```
project_RPA_langGraph/
├── src/
│   ├── workorder-system/     # 工单系统
│   │   ├── frontend/        # Vue 3前端
│   │   └── backend/         # Express后端
│   ├── ai-assistant/        # AI助手系统
│   │   ├── src/
│   │   │   ├── agents/     # 多Agent实现
│   │   │   ├── core/       # 核心组件
│   │   │   ├── langgraph/  # LangGraph集成
│   │   │   └── playwright-mcp/ # MCP客户端
│   │   ├── prompts/         # 提示词体系
│   │   └── data/            # SQLite数据库
│   └── shared/              # 共享组件
├── config/                   # 配置文件
├── test-*.js                # 测试文件
├── .env                     # 环境变量
└── *.md                     # 文档
```

## 10. 开发指南

### 快速启动
```bash
# 安装所有依赖
npm run install:all

# 启动所有服务（开发模式）
npm run dev

# 分别启动各服务
npm run dev:workorder-backend   # 工单系统后端 (3001)
npm run dev:workorder-frontend  # 工单系统前端 (3000)
npm run dev:ai-assistant       # AI助手服务 (3002)
```

### 🧪 测试驱动开发 (TDD)

#### 测试命令体系
```bash
# 按优先级运行测试
npm run test:critical    # P0关键测试 (LangGraph初始化等)
npm run test:core       # P1核心测试 (多Agent系统等)
npm run test:enhanced   # P2增强测试 (性能优化等)

# 按类型运行测试  
npm run test:unit       # 单元测试 (基础组件)
npm run test:integration # 集成测试 (组件协作)
npm run test:e2e        # 端到端测试 (完整流程)

# 开发辅助
npm run test:watch      # 监控测试状态
npm run test:all        # 运行所有测试
```

#### TDD开发流程
1. **🔴 Red**: 先写失败测试，明确需求
2. **🟢 Green**: 写最小代码让测试通过
3. **⭐ Refactor**: 重构优化代码质量

#### 测试覆盖目标
- **单元测试**: ≥90% 覆盖率
- **集成测试**: ≥80% 覆盖率
- **E2E测试**: 覆盖所有关键业务流程

### 调试技巧
1. **浏览器调试**: 设置 `BROWSER_HEADLESS=false` 观察操作
2. **Agent日志**: 查看AI助手控制台输出
3. **状态检查**: 查询 `workflow_states` 表
4. **单元测试**: 使用 `test-*.js` 文件进行针对性测试
5. **测试报告**: 查看 `TEST_ROADMAP.md` 了解当前测试状态
