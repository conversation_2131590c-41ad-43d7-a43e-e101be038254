#!/usr/bin/env node

/**
 * 数据库种子数据脚本
 */

require('dotenv').config();
const db = require('../src/shared/database/connection');

// 生成随机工单数据
function generateTicketData(index) {
    const titles = [
        '下架门店外卖商品',
        '更新门店信息',
        '批量商品上架',
        '修改商品价格',
        '处理客户投诉',
        '更新库存信息',
        '设置促销活动',
        '门店营业时间调整',
        '商品分类整理',
        '配送范围更新',
        '会员等级调整',
        '优惠券批量发放',
        '商品图片更新',
        '门店公告发布',
        '评价回复处理',
        '退款申请处理',
        '商品下架通知',
        '新品上架推广',
        '库存预警处理',
        '数据统计报表'
    ];

    const contents = [
        '请下架指定门店的相关商品，确保商品状态正确更新',
        '更新门店基本信息，包括地址、电话、营业时间等',
        '将新商品批量上架到指定门店，设置合适的价格和库存',
        '根据市场情况调整商品价格，确保竞争力',
        '及时处理客户投诉，维护良好的客户关系',
        '更新商品库存信息，确保数据准确性',
        '设置限时促销活动，提升销售业绩',
        '调整门店营业时间，适应客户需求',
        '整理商品分类，优化用户购物体验',
        '更新配送范围，扩大服务覆盖面',
        '调整会员等级规则，提升用户粘性',
        '批量发放优惠券，促进用户消费',
        '更新商品展示图片，提升商品吸引力',
        '发布门店重要公告，及时通知用户',
        '回复用户评价，维护品牌形象',
        '处理用户退款申请，确保服务质量',
        '通知商品下架信息，避免用户困扰',
        '推广新上架商品，提升知名度',
        '处理库存预警，避免缺货情况',
        '生成数据统计报表，支持决策分析'
    ];

    const statuses = ['待开始', '排队中', '处理中', '待补充信息', '已完成', '处理失败', '已挂起'];
    const priorities = [0, 1, 2, 3]; // 低、中、高、紧急

    const now = new Date();
    const createdAt = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000); // 过去7天内随机时间
    
    return {
        ticket_number: `WO-${createdAt.toISOString().slice(0, 10).replace(/-/g, '')}-${String(index).padStart(4, '0')}`,
        title: titles[index % titles.length],
        content: contents[index % contents.length],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        notes: Math.random() > 0.5 ? `备注信息 ${index}` : null,
        created_at: createdAt.toISOString()
    };
}

async function seedDatabase() {
    try {
        console.log('开始添加种子数据...');

        // 连接数据库
        await db.connect();
        console.log('数据库连接成功');

        // 清空现有数据（可选）
        console.log('清空现有工单数据...');
        await db.run('DELETE FROM tickets');
        await db.run('DELETE FROM tasks');

        // 插入50条测试工单数据
        console.log('插入测试工单数据...');
        for (let i = 1; i <= 50; i++) {
            const ticketData = generateTicketData(i);
            
            const sql = `
                INSERT INTO tickets (ticket_number, title, content, status, priority, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `;
            
            await db.run(sql, [
                ticketData.ticket_number,
                ticketData.title,
                ticketData.content,
                ticketData.status,
                ticketData.priority,
                ticketData.notes,
                ticketData.created_at
            ]);
            
            if (i % 10 === 0) {
                console.log(`已插入 ${i} 条工单数据...`);
            }
        }

        console.log('种子数据添加完成！');
        console.log('总共添加了 50 条工单数据');

        // 验证数据
        const count = await db.get('SELECT COUNT(*) as count FROM tickets');
        console.log(`数据库中现有工单数量: ${count.count}`);

    } catch (error) {
        console.error('添加种子数据失败:', error);
        process.exit(1);
    } finally {
        // 关闭数据库连接
        await db.close();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    seedDatabase();
}

module.exports = { seedDatabase };
