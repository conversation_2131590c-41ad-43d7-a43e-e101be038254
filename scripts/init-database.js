#!/usr/bin/env node

/**
 * 数据库初始化脚本
 */

require('dotenv').config();
const path = require('path');
const fs = require('fs');
const db = require('../src/shared/database/connection');

async function initDatabase() {
    try {
        console.log('开始初始化数据库...');

        // 连接数据库
        await db.connect();
        console.log('数据库连接成功');

        // 初始化表结构
        await db.initializeSchema();
        console.log('数据库表结构初始化完成');

        // 检查数据库健康状态
        const isHealthy = await db.healthCheck();
        if (isHealthy) {
            console.log('数据库健康检查通过');
        } else {
            throw new Error('数据库健康检查失败');
        }

        console.log('数据库初始化完成！');
        console.log(`数据库文件位置: ${db.dbPath}`);

    } catch (error) {
        console.error('数据库初始化失败:', error);
        process.exit(1);
    } finally {
        // 关闭数据库连接
        await db.close();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    initDatabase();
}

module.exports = initDatabase;