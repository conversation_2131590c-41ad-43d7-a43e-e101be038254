#!/usr/bin/env node

/**
 * 数据库清理脚本
 * 清理错误的任务记录，重置系统状态
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const DB_PATH = path.join(__dirname, '../data/rpa_platform.db');

async function cleanDatabase() {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(DB_PATH, (err) => {
            if (err) {
                console.error('❌ 数据库连接失败:', err.message);
                reject(err);
                return;
            }
            console.log('✅ 数据库连接成功');
        });

        db.serialize(() => {
            console.log('🧹 开始清理数据库...');

            // 1. 删除错误的任务记录
            db.run(`DELETE FROM tasks WHERE description LIKE '%任务执行结果:%' AND description LIKE '%Cannot read properties of undefined%'`, function(err) {
                if (err) {
                    console.error('❌ 删除错误任务失败:', err.message);
                } else {
                    console.log(`✅ 删除了 ${this.changes} 条错误任务记录`);
                }
            });

            // 2. 删除异常长度的任务描述
            db.run(`DELETE FROM tasks WHERE LENGTH(description) > 1000`, function(err) {
                if (err) {
                    console.error('❌ 删除异常任务失败:', err.message);
                } else {
                    console.log(`✅ 删除了 ${this.changes} 条异常任务记录`);
                }
            });

            // 3. 重置所有工单状态为待开始（除了真正完成的）
            db.run(`UPDATE tickets SET status = '待开始' WHERE status IN ('处理中', '排队中') OR (status = '已完成' AND (report IS NULL OR report = ''))`, function(err) {
                if (err) {
                    console.error('❌ 重置工单状态失败:', err.message);
                } else {
                    console.log(`✅ 重置了 ${this.changes} 个工单状态`);
                }
            });

            // 4. 清理操作日志中的错误记录
            db.run(`DELETE FROM operation_logs WHERE operation_type = 'error' AND details LIKE '%Cannot read properties of undefined%'`, function(err) {
                if (err) {
                    console.error('❌ 清理操作日志失败:', err.message);
                } else {
                    console.log(`✅ 清理了 ${this.changes} 条错误日志`);
                }
            });

            // 5. 查看清理后的统计
            db.get(`SELECT 
                COUNT(*) as total_tickets,
                COUNT(CASE WHEN status = '已完成' THEN 1 END) as completed,
                COUNT(CASE WHEN status = '待开始' THEN 1 END) as pending,
                COUNT(CASE WHEN status = '处理中' THEN 1 END) as processing
                FROM tickets`, (err, row) => {
                if (err) {
                    console.error('❌ 查询统计失败:', err.message);
                } else {
                    console.log('\n📊 清理后的数据库统计:');
                    console.log(`   总工单数: ${row.total_tickets}`);
                    console.log(`   已完成: ${row.completed}`);
                    console.log(`   待开始: ${row.pending}`);
                    console.log(`   处理中: ${row.processing}`);
                }
            });

            db.get(`SELECT COUNT(*) as task_count FROM tasks`, (err, row) => {
                if (err) {
                    console.error('❌ 查询任务统计失败:', err.message);
                } else {
                    console.log(`   剩余任务数: ${row.task_count}`);
                }
                
                db.close((err) => {
                    if (err) {
                        console.error('❌ 关闭数据库失败:', err.message);
                        reject(err);
                    } else {
                        console.log('\n🎉 数据库清理完成!');
                        resolve();
                    }
                });
            });
        });
    });
}

// 运行清理
if (require.main === module) {
    cleanDatabase().catch(console.error);
}

module.exports = { cleanDatabase };
