const fs = require('fs');
const path = require('path');
const dbConnection = require('../src/shared/database/connection');

/**
 * 数据库迁移工具
 */
class DatabaseMigrator {
    constructor() {
        this.db = dbConnection;
        this.migrationsPath = path.join(__dirname, '../config/migrations');
    }

    /**
     * 执行数据库迁移
     */
    async migrate() {
        try {
            console.log('开始数据库迁移...');
            
            // 确保数据库连接
            if (!this.db.isConnected) {
                await this.db.connect();
            }

            // 创建迁移记录表
            await this.createMigrationTable();

            // 获取所有迁移文件
            const migrationFiles = this.getMigrationFiles();
            
            if (migrationFiles.length === 0) {
                console.log('没有找到迁移文件');
                return;
            }

            // 执行迁移
            for (const file of migrationFiles) {
                await this.executeMigration(file);
            }

            console.log('数据库迁移完成');
            
        } catch (error) {
            console.error('数据库迁移失败:', error);
            throw error;
        }
    }

    /**
     * 创建迁移记录表
     */
    async createMigrationTable() {
        try {
            await this.db.run(`
                CREATE TABLE IF NOT EXISTS migrations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    filename VARCHAR(255) NOT NULL UNIQUE,
                    executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);
            console.log('迁移记录表已创建');
        } catch (error) {
            console.error('创建迁移记录表失败:', error);
            throw error;
        }
    }

    /**
     * 获取迁移文件列表
     */
    getMigrationFiles() {
        try {
            if (!fs.existsSync(this.migrationsPath)) {
                console.log('迁移目录不存在:', this.migrationsPath);
                return [];
            }

            const files = fs.readdirSync(this.migrationsPath)
                .filter(file => file.endsWith('.sql'))
                .sort(); // 按文件名排序

            console.log(`找到 ${files.length} 个迁移文件:`, files);
            return files;
        } catch (error) {
            console.error('读取迁移文件失败:', error);
            return [];
        }
    }

    /**
     * 执行单个迁移
     */
    async executeMigration(filename) {
        try {
            // 检查是否已经执行过
            const existing = await this.db.get(
                'SELECT * FROM migrations WHERE filename = ?',
                [filename]
            );

            if (existing) {
                console.log(`迁移已执行，跳过: ${filename}`);
                return;
            }

            console.log(`执行迁移: ${filename}`);

            // 读取迁移文件
            const filePath = path.join(this.migrationsPath, filename);
            const sql = fs.readFileSync(filePath, 'utf8');

            // 分割SQL语句并执行
            const statements = sql.split(';').filter(stmt => stmt.trim());

            const operations = [];

            // 添加SQL语句操作
            for (const statement of statements) {
                if (statement.trim()) {
                    operations.push({
                        sql: statement.trim(),
                        params: []
                    });
                }
            }

            // 添加迁移记录操作
            operations.push({
                sql: 'INSERT INTO migrations (filename) VALUES (?)',
                params: [filename]
            });

            await this.db.transaction(operations);

            console.log(`迁移执行成功: ${filename}`);

        } catch (error) {
            console.error(`迁移执行失败: ${filename}`, error);
            throw error;
        }
    }

    /**
     * 回滚迁移（简单实现）
     */
    async rollback(filename) {
        try {
            console.log(`回滚迁移: ${filename}`);
            
            // 删除迁移记录
            await this.db.run(
                'DELETE FROM migrations WHERE filename = ?',
                [filename]
            );

            console.log(`迁移回滚成功: ${filename}`);
            console.log('注意: 数据库结构更改需要手动回滚');

        } catch (error) {
            console.error(`迁移回滚失败: ${filename}`, error);
            throw error;
        }
    }

    /**
     * 获取迁移状态
     */
    async getStatus() {
        try {
            const executed = await this.db.all(
                'SELECT filename, executed_at FROM migrations ORDER BY executed_at'
            );

            const allFiles = this.getMigrationFiles();
            const executedFiles = executed.map(m => m.filename);
            const pendingFiles = allFiles.filter(f => !executedFiles.includes(f));

            return {
                executed: executed,
                pending: pendingFiles,
                total: allFiles.length
            };
        } catch (error) {
            console.error('获取迁移状态失败:', error);
            return {
                executed: [],
                pending: [],
                total: 0
            };
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const migrator = new DatabaseMigrator();
    
    const command = process.argv[2];
    
    switch (command) {
        case 'migrate':
            migrator.migrate()
                .then(() => {
                    console.log('迁移完成');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('迁移失败:', error);
                    process.exit(1);
                });
            break;
            
        case 'status':
            migrator.getStatus()
                .then(status => {
                    console.log('迁移状态:');
                    console.log(`总计: ${status.total}`);
                    console.log(`已执行: ${status.executed.length}`);
                    console.log(`待执行: ${status.pending.length}`);
                    
                    if (status.pending.length > 0) {
                        console.log('待执行的迁移:');
                        status.pending.forEach(file => console.log(`  - ${file}`));
                    }
                    
                    process.exit(0);
                })
                .catch(error => {
                    console.error('获取状态失败:', error);
                    process.exit(1);
                });
            break;
            
        case 'rollback':
            const filename = process.argv[3];
            if (!filename) {
                console.error('请指定要回滚的迁移文件名');
                process.exit(1);
            }
            
            migrator.rollback(filename)
                .then(() => {
                    console.log('回滚完成');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('回滚失败:', error);
                    process.exit(1);
                });
            break;
            
        default:
            console.log('用法:');
            console.log('  node migrate-database.js migrate   # 执行迁移');
            console.log('  node migrate-database.js status    # 查看状态');
            console.log('  node migrate-database.js rollback <filename>  # 回滚迁移');
            process.exit(1);
    }
}

module.exports = DatabaseMigrator;
