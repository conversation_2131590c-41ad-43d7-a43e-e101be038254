/**
 * 验证LLM API密钥配置
 * 测试API连接是否正常
 */

require('dotenv').config();
const axios = require('axios');

async function testAPIKey() {
    console.log('====================================');
    console.log('LLM API密钥验证测试');
    console.log('====================================\n');
    
    // 检查环境变量
    const apiKey = process.env.DASHSCOPE_API_KEY;
    const baseUrl = process.env.DASHSCOPE_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1';
    const model = process.env.MAIN_MODEL || 'qwen-plus';
    
    console.log('环境变量检查:');
    console.log(`- API密钥: ${apiKey ? '已配置 (前8位: ' + apiKey.substring(0, 8) + '...)' : '❌ 未配置'}`);
    console.log(`- Base URL: ${baseUrl}`);
    console.log(`- 模型: ${model}\n`);
    
    if (!apiKey) {
        console.error('❌ 错误: DASHSCOPE_API_KEY 环境变量未设置');
        console.log('\n请确保在 .env 文件中设置了有效的 API 密钥:');
        console.log('DASHSCOPE_API_KEY=your-actual-api-key-here\n');
        return false;
    }
    
    // 测试API调用
    console.log('测试API调用...\n');
    
    try {
        const response = await axios.post(
            `${baseUrl}/chat/completions`,
            {
                model: model,
                messages: [
                    {
                        role: 'user',
                        content: '请回复"测试成功"'
                    }
                ],
                temperature: 0.1
            },
            {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        console.log('✅ API调用成功!');
        console.log('响应内容:', response.data.choices[0].message.content);
        console.log('\nAPI密钥配置正确，可以正常使用。');
        return true;
        
    } catch (error) {
        console.error('❌ API调用失败!');
        
        if (error.response) {
            console.error(`状态码: ${error.response.status}`);
            console.error(`错误信息: ${error.response.data.error?.message || JSON.stringify(error.response.data)}`);
            
            if (error.response.status === 401) {
                console.log('\n401错误通常表示:');
                console.log('1. API密钥无效或过期');
                console.log('2. API密钥格式不正确');
                console.log('3. 账户余额不足\n');
                console.log('请检查您的API密钥是否正确，并确保账户有足够的余额。');
            }
        } else {
            console.error('网络错误:', error.message);
        }
        
        return false;
    }
}

// 执行测试
testAPIKey().then(success => {
    process.exit(success ? 0 : 1);
});