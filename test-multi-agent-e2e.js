const { MultiAgentLangGraphProcessor } = require('./src/ai-assistant/src/langgraph/multi-agent-langgraph-processor');
const { getTicket } = require('./src/shared/core/ticket-manager');

async function runTest() {
  console.log('🚀 Starting Multi-Agent E2E Test...');

  const ticketId = 'TEST-MULTI-AGENT-001';
  const ticket = await getTicket(ticketId);

  if (!ticket) {
    console.error(`Ticket ${ticketId} not found. Please create it first.`);
    return;
  }

  console.log(`📄 Ticket Found: ${ticket.title}`);
  console.log(`📝 Ticket Content: ${ticket.content}`);

  const processor = new MultiAgentLangGraphProcessor();

  try {
    const finalState = await processor.process(ticket);

    console.log('✅ E2E Test Completed Successfully!');
    console.log('--- FINAL WORKFLOW STATE ---');
    console.log(JSON.stringify(finalState, null, 2));

    if (finalState.result && finalState.result.summary) {
        console.log('🎉🎉🎉');
        console.log('Final Summary:', finalState.result.summary);
        console.log('🎉🎉🎉');
    } else {
        console.warn('🤔 No final summary was generated.');
    }

  } catch (error) {
    console.error('🔥 E2E Test Failed:', error);
  }
}

runTest();
