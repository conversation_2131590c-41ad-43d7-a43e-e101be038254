# 数据库设计文档

## 1. 数据库选择
- **数据库类型**: SQLite
- **原因**: 轻量级，适合演示环境，无需额外配置，支持并发读取

## 2. 数据表设计

### 2.1 工单表 (tickets)
```sql
CREATE TABLE tickets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_number VARCHAR(50) UNIQUE NOT NULL,     -- 工单号
    title VARCHAR(200) NOT NULL,                   -- 工单标题
    content TEXT NOT NULL,                         -- 工单内容(支持富文本/markdown)
    status VARCHAR(20) NOT NULL DEFAULT '待开始',   -- 工单状态
    notes TEXT,                                    -- 备注(AI填写)
    summary TEXT,                                  -- 完单总结(AI填写)
    priority INTEGER DEFAULT 0,                   -- 优先级(0-低,1-中,2-高)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,                         -- 完成时间
    processing_time INTEGER,                       -- 处理时长(小时)
    assigned_worker_id VARCHAR(50),               -- 分配的AI工作线程ID
    guide_version VARCHAR(20)                      -- 使用的操作指引版本
);
```

### 2.2 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_id INTEGER NOT NULL,                   -- 关联工单ID
    task_order INTEGER NOT NULL,                  -- 任务顺序
    title VARCHAR(200) NOT NULL,                  -- 任务标题
    description TEXT,                             -- 任务描述
    status VARCHAR(20) NOT NULL DEFAULT '待开始', -- 任务状态
    action_type VARCHAR(50),                      -- 操作类型(click,input,navigate等)
    target_selector TEXT,                         -- 目标元素选择器
    action_data TEXT,                             -- 操作数据(JSON格式)
    screenshot_path VARCHAR(500),                 -- 截图路径
    result_summary TEXT,                          -- 执行结果摘要
    error_message TEXT,                           -- 错误信息
    retry_count INTEGER DEFAULT 0,               -- 重试次数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id)
);
```

### 2.3 AI工作线程表 (ai_workers)
```sql
CREATE TABLE ai_workers (
    id VARCHAR(50) PRIMARY KEY,                   -- 工作线程ID
    status VARCHAR(20) NOT NULL DEFAULT 'idle',  -- 状态(idle,busy,error)
    current_ticket_id INTEGER,                   -- 当前处理的工单ID
    browser_session_id VARCHAR(100),             -- 浏览器会话ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_heartbeat DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (current_ticket_id) REFERENCES tickets(id)
);
```

### 2.4 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    key VARCHAR(100) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2.5 操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_id INTEGER,
    task_id INTEGER,
    worker_id VARCHAR(50),
    operation_type VARCHAR(50) NOT NULL,         -- 操作类型
    operation_data TEXT,                         -- 操作数据(JSON)
    result VARCHAR(20),                          -- 结果(success,error,retry)
    message TEXT,                                -- 日志消息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id),
    FOREIGN KEY (task_id) REFERENCES tasks(id)
);
```

## 3. 索引设计
```sql
-- 工单表索引
CREATE INDEX idx_tickets_status ON tickets(status);
CREATE INDEX idx_tickets_created_at ON tickets(created_at);
CREATE INDEX idx_tickets_number ON tickets(ticket_number);

-- 任务表索引
CREATE INDEX idx_tasks_ticket_id ON tasks(ticket_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_order ON tasks(ticket_id, task_order);

-- 工作线程表索引
CREATE INDEX idx_workers_status ON ai_workers(status);
CREATE INDEX idx_workers_ticket ON ai_workers(current_ticket_id);

-- 日志表索引
CREATE INDEX idx_logs_ticket_id ON operation_logs(ticket_id);
CREATE INDEX idx_logs_created_at ON operation_logs(created_at);
```

## 4. 工单状态枚举
- `待开始`: 新建或用户补充信息后
- `排队中`: 已进入处理队列，等待分配工作线程
- `处理中`: AI助手正在处理
- `待补充信息`: AI助手需要更多信息
- `已完成`: 所有任务完成
- `已挂起`: 用户暂停
- `处理失败`: 处理过程中出现无法恢复的错误

## 5. 任务状态枚举
- `待开始`: 等待执行
- `执行中`: 正在执行
- `已完成`: 执行成功
- `失败`: 执行失败
- `跳过`: 被跳过执行

## 6. 并发处理设计要点

### 6.1 工作线程管理
- 最大并发数：根据系统资源动态调整(建议3-5个)
- 线程池：预创建工作线程，避免频繁创建销毁
- 负载均衡：优先分配给空闲时间最长的工作线程

### 6.2 资源隔离
- 每个工作线程使用独立的浏览器实例
- 独立的临时文件目录
- 独立的日志文件

### 6.3 状态同步
- 使用WebSocket实时推送状态变更
- 数据库乐观锁防止并发冲突
- 定期心跳检测工作线程健康状态