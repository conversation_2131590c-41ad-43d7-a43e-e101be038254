# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log
src/ai-assistant/data/logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3
src/ai-assistant/data/database.db

# 截图和临时文件
src/ai-assistant/data/screenshots/
src/ai-assistant/data/temp/
screenshots/
temp/

# 构建输出
dist/
build/
.nuxt/
.next/
.vuepress/dist/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 测试覆盖率
coverage/
.nyc_output/

# 缓存
.cache/
.parcel-cache/

# 浏览器相关
playwright-report/
test-results/

# PM2
.pm2/

# 其他
*.tgz
*.tar.gz