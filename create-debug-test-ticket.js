const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, 'data', 'rpa_platform.db');

// 创建测试工单
function createTestTicket() {
    const db = new sqlite3.Database(dbPath);
    
    const title = '上架榮記豆腐麵食外卖商品：菜遠牛肉飯';
    const content = `上架榮記豆腐麵食(官也街、招牌煲仔飯、嫩滑豆腐花)的外卖商品: 菜遠牛肉飯

请AI助手根据系统操作指引执行以下业务任务：
1. 分析工单内容，理解需要上架的商品信息
2. 读取system_guide.md中的上架操作流程
3. 制定具体的操作计划
4. 执行浏览器操作完成商品上架
5. 截图记录操作过程和结果
6. 生成详细的执行报告

这是一个真实的业务RPA任务，需要AI助手展现真正的业务理解和执行能力。`;
    
    const priority = '中';
    const status = '排队中';
    const createdAt = new Date().toISOString().replace('T', ' ').substring(0, 19);
    
    // 生成工单号
    const ticketNumber = `WO-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${Math.floor(Math.random() * 900000) + 100000}`;
    
    const sql = `INSERT INTO tickets (ticket_number, title, content, priority, status, created_at, updated_at) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)`;
    
    db.run(sql, [ticketNumber, title, content, priority, status, createdAt, createdAt], function(err) {
        if (err) {
            console.error('创建工单失败:', err);
        } else {
            console.log(`✅ 成功创建工单: ${ticketNumber}`);
            console.log(`📋 工单标题: ${title}`);
            console.log(`🔢 工单ID: ${this.lastID}`);
            console.log(`📅 创建时间: ${createdAt}`);
        }
        db.close();
    });
}

createTestTicket();
