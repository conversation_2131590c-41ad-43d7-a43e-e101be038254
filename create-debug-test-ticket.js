const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, 'data', 'rpa_platform.db');

// 创建测试工单
function createTestTicket() {
    const db = new sqlite3.Database(dbPath);
    
    const title = '修复状态传递机制测试v17';
    const content = `请AI助手执行一个简单的RPA测试：

1. 打开商户后台：https://uat-merchant.aomiapp.com/#/bdlogin
2. 等待页面完全加载（10秒）
3. 截图保存页面状态
4. 验证是否成功打开了商户后台

这是一个基础的浏览器导航测试，用于验证AI助手的真实RPA能力。`;
    
    const priority = '中';
    const status = '排队中';
    const createdAt = new Date().toISOString().replace('T', ' ').substring(0, 19);
    
    // 生成工单号
    const ticketNumber = `WO-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${Math.floor(Math.random() * 900000) + 100000}`;
    
    const sql = `INSERT INTO tickets (ticket_number, title, content, priority, status, created_at, updated_at) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)`;
    
    db.run(sql, [ticketNumber, title, content, priority, status, createdAt, createdAt], function(err) {
        if (err) {
            console.error('创建工单失败:', err);
        } else {
            console.log(`✅ 成功创建工单: ${ticketNumber}`);
            console.log(`📋 工单标题: ${title}`);
            console.log(`🔢 工单ID: ${this.lastID}`);
            console.log(`📅 创建时间: ${createdAt}`);
        }
        db.close();
    });
}

createTestTicket();
