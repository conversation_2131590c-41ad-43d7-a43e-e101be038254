# RPA 自动化平台

## 项目简介

RPA自动化平台是一个基于AI驱动的自动化解决方案，通过工单系统和AI助手的协作，实现对现有管理后台的自动化操作，替代人工操作，提升效率。

**v2.0**: 多Agent架构重构，实现零硬编码的AI驱动执行流程。  
**v2.1**: 系统稳定性优化，添加执行控制、错误恢复和性能监控。

## 核心特性

- 🤖 **AI驱动**: 集成阿里云百炼模型，支持文本和图像理解
- 👥 **多Agent协同**: 5个专门化Agent分工合作，智能决策执行
- 🔄 **并发处理**: 支持多工单同时处理，提升处理效率
- 🎯 **智能任务拆解**: 自动理解工单内容，构建执行任务
- 🌐 **浏览器自动化**: 基于Playwright实现精确的页面操作
- 📊 **实时监控**: WebSocket实时状态同步，可视化处理进度
- 🎨 **现代界面**: Vue.js + Element Plus构建的直观用户界面
- ⏱️ **执行控制**: 操作超时保护，防止系统挂起
- 🔧 **错误恢复**: 智能错误分类和自动恢复机制
- 📈 **性能监控**: 实时性能追踪和优化建议

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   工单系统      │    │   AI助手        │    │   管理后台      │
│                 │    │                 │    │                 │
│ • 工单管理      │◄──►│ • 任务理解      │───►│ • 目标操作      │
│ • 状态跟踪      │    │ • 浏览器操作    │    │ • 业务处理      │
│ • 用户交互      │    │ • 并发处理      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 技术栈

### 后端
- **Node.js 18+** - 运行时环境
- **Express.js** - Web框架
- **SQLite** - 轻量级数据库
- **Playwright** - 浏览器自动化
- **WebSocket** - 实时通信

### 前端
- **Vue.js 3** - 前端框架
- **Element Plus** - UI组件库
- **Vite** - 构建工具
- **Pinia** - 状态管理

### AI集成
- **阿里云百炼** - 大模型服务
- **qwen-plus** - 主模型（已升级）
- **qwen-vl-plus** - 视觉模型
- **LangGraph** - 工作流引擎

## 快速开始

### 环境要求

- Node.js 18.0+
- npm 8.0+
- Chrome/Chromium 浏览器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd project_RPA
```

2. **安装依赖**
```bash
npm run install:all
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

4. **初始化数据库**
```bash
npm run db:init
```

5. **启动开发环境**
```bash
npm run dev
```

### 访问地址

- 工单系统前端: http://localhost:3000
- 工单系统后端: http://localhost:3001
- AI助手服务: http://localhost:3002
- WebSocket服务: ws://localhost:3003

## 项目结构

```
project_RPA_langGraph/
├── src/
│   ├── workorder-system/     # 工单系统
│   │   ├── backend/         # 后端API服务
│   │   └── frontend/        # 前端Vue应用
│   ├── ai-assistant/        # AI助手系统
│   │   ├── src/            # 核心代码
│   │   │   ├── agents/     # 多Agent系统
│   │   │   ├── langgraph/  # LangGraph工作流
│   │   │   └── core/       # 核心组件
│   │   ├── prompts/        # 提示词体系
│   │   └── data/           # 数据存储
│   ├── shared/             # 共享模块
│   └── prompt/             # 通用提示词模板
├── config/                 # 配置文件
├── test-*.js              # 测试文件（已精简）
└── docs/                  # 项目文档
```

## 使用指南

### 1. 创建工单
- 在工单系统中点击"新建工单"
- 填写工单内容，支持富文本和图片
- 提交后工单进入"待开始"状态

### 2. AI自动处理
- 启动AI助手的自动处理模式
- AI助手会按队列顺序处理工单
- 支持多个工单并发处理

### 3. 监控进度
- 实时查看工单处理状态
- 查看详细的任务执行日志
- 查看操作截图和结果

### 4. 人工干预
- 可随时暂停AI处理
- 补充必要信息
- 手动调整任务优先级

## 开发指南

### 添加新的操作指引

1. 编辑 `system_guide.md` 文件
2. 按照既定格式添加新的操作流程
3. 重启AI助手服务

### 自定义提示词

1. 编辑 `src/prompt/` 目录下的模板文件
2. 修改后会自动生效，无需重启

### 扩展浏览器操作

1. 在 `src/ai-assistant/src/browser/` 目录下添加新的操作模块
2. 在 `action-executor.js` 中注册新的操作类型

## 部署指南

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm run build
npm start
```

### Docker部署
```bash
docker-compose up -d
```

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查Chrome/Chromium是否正确安装
   - 确认Playwright依赖是否完整

2. **模型调用失败**
   - 检查API密钥是否正确配置
   - 确认网络连接是否正常

3. **WebSocket连接失败**
   - 检查端口是否被占用
   - 确认防火墙设置

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系我们

如有问题或建议，请提交 Issue 或联系开发团队。