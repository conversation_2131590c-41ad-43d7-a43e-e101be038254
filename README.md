# AI-First RPA 自动化平台

## 项目简介

AI-First RPA自动化平台是一个完全由AI驱动的智能自动化解决方案，通过工单系统和AI助手的协作，实现对现有管理后台的自动化操作，替代人工操作，提升效率。

**v2.0**: 多Agent架构重构，实现零硬编码的AI驱动执行流程。
**v2.1**: 系统稳定性优化，添加执行控制、错误恢复和性能监控。
**v2.2**: AI-First测试框架建立，LLM客户端现代化，智能特征验证通过率75%。
**v2.0.1**: 🔧 **AI-First状态适配器重大修复** - 解决LangGraph状态方法缺失问题，实现100%AI-First兼容。

## 🏆 最新成就 (2025-07-14)

- 🔧 **AI-First状态适配器**: 完美解决LangGraph状态管理问题，保持100%AI-First原则
- ✅ **工作流状态修复**: 从`state.addError is not a function`错误完全恢复
- 🎯 **最小侵入性设计**: 不改变核心架构，完美兼容现有代码
- 🚀 **动态方法注入**: 支持AI驱动的状态方法，可扩展性极强
- ✅ **AI智能特征验证**: 75%通过率，真正的AI驱动能力
- ✅ **LLM客户端现代化**: 标准OpenAI兼容调用，100%连接成功率
- ✅ **零硬编码原则**: 完全移除预定义响应模板和硬编码逻辑
- ✅ **动态测试场景**: 每次运行生成不同的业务场景
- ✅ **阿里云百炼API**: 稳定连接，支持qwen-plus模型

## 核心特性

### 🧬 AI-First核心能力
- 🤖 **真正的AI驱动**: 零硬编码，所有决策由AI模型实时生成
- 🧠 **智能特征验证**: 语义理解73.4%，适应能力57%，学习能力75%
- 🔄 **动态场景生成**: 每次测试生成不同业务场景，避免固化
- 📊 **概率性验证**: 验证AI推理过程而非输出格式匹配
- ⚡ **标准API调用**: OpenAI兼容客户端，1.5-3秒响应时间

### 🏗️ 系统架构特性
- 👥 **多Agent协同**: 5个专门化Agent分工合作，智能决策执行
- 🔄 **并发处理**: 支持多工单同时处理，提升处理效率
- 🎯 **智能任务拆解**: 自动理解工单内容，构建执行任务
- 🌐 **浏览器自动化**: 基于Playwright实现精确的页面操作
- 📊 **实时监控**: WebSocket实时状态同步，可视化处理进度
- 🔧 **AI-First状态适配器**: 动态方法注入，完美解决LangGraph状态管理问题

### 🛠️ 开发体验特性
- 🎨 **现代界面**: Vue.js + Element Plus构建的直观用户界面
- ⏱️ **执行控制**: 操作超时保护，防止系统挂起
- 🔧 **错误恢复**: 智能错误分类和自动恢复机制
- 📈 **性能监控**: 实时性能追踪和优化建议
- 🧪 **AI-First测试**: 真正验证AI智能特征的测试框架

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   工单系统      │    │   AI助手        │    │   管理后台      │
│                 │    │                 │    │                 │
│ • 工单管理      │◄──►│ • 任务理解      │───►│ • 目标操作      │
│ • 状态跟踪      │    │ • 浏览器操作    │    │ • 业务处理      │
│ • 用户交互      │    │ • 并发处理      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 技术栈

### 后端
- **Node.js 18+** - 运行时环境
- **Express.js** - Web框架
- **SQLite** - 轻量级数据库
- **Playwright** - 浏览器自动化
- **WebSocket** - 实时通信

### 前端
- **Vue.js 3** - 前端框架
- **Element Plus** - UI组件库
- **Vite** - 构建工具
- **Pinia** - 状态管理

### AI集成
- **阿里云百炼** - 大模型服务 (100%连接成功率)
- **qwen-plus** - 主模型，支持标准OpenAI兼容调用
- **qwen-vl-plus** - 视觉模型，图像理解能力
- **LangGraph** - 工作流引擎，多Agent协同
- **OpenAI客户端** - 标准化API调用方式
- **AI-First状态适配器** - 动态状态管理，支持AI驱动的方法注入

## 快速开始

### 环境要求

- Node.js 18.0+
- npm 8.0+
- Chrome/Chromium 浏览器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd project_RPA
```

2. **安装依赖**
```bash
npm run install:all
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

4. **初始化数据库**
```bash
npm run db:init
```

5. **启动开发环境**
```bash
npm run dev
```

### 访问地址

- 工单系统前端: http://localhost:3000
- 工单系统后端: http://localhost:3001
- AI助手服务: http://localhost:3002
- WebSocket服务: ws://localhost:3003

## 项目结构

```
project_RPA_langGraph/
├── src/
│   ├── workorder-system/     # 工单系统
│   │   ├── backend/         # 后端API服务
│   │   └── frontend/        # 前端Vue应用
│   ├── ai-assistant/        # AI助手系统
│   │   ├── src/            # 核心代码
│   │   │   ├── agents/     # 多Agent系统
│   │   │   ├── langgraph/  # LangGraph工作流
│   │   │   └── core/       # 核心组件
│   │   ├── prompts/        # 提示词体系
│   │   └── data/           # 数据存储
│   ├── shared/             # 共享模块
│   └── prompt/             # 通用提示词模板
├── config/                 # 配置文件
├── test-*.js              # 测试文件（已精简）
└── docs/                  # 项目文档
```

## 使用指南

### 1. 创建工单
- 在工单系统中点击"新建工单"
- 填写工单内容，支持富文本和图片
- 提交后工单进入"待开始"状态

### 2. AI自动处理
- 启动AI助手的自动处理模式
- AI助手会按队列顺序处理工单
- 支持多个工单并发处理

### 3. 监控进度
- 实时查看工单处理状态
- 查看详细的任务执行日志
- 查看操作截图和结果

### 4. 人工干预
- 可随时暂停AI处理
- 补充必要信息
- 手动调整任务优先级

## 开发指南

### 添加新的操作指引

1. 编辑 `system_guide.md` 文件
2. 按照既定格式添加新的操作流程
3. 重启AI助手服务

### 自定义提示词

1. 编辑 `src/prompt/` 目录下的模板文件
2. 修改后会自动生效，无需重启

### 扩展浏览器操作

1. 在 `src/ai-assistant/src/browser/` 目录下添加新的操作模块
2. 在 `action-executor.js` 中注册新的操作类型

## 部署指南

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm run build
npm start
```

### Docker部署
```bash
docker-compose up -d
```

## 🧪 测试和验证

### AI-First测试框架

本项目建立了完整的AI-First测试方法论，确保系统真正具备AI驱动能力：

#### 运行LLM连接测试
```bash
# 验证阿里云百炼API连通性
node test-llm-connection.js
```

#### 运行AI智能特征测试
```bash
# 验证AI的理解、推理、适应、学习能力
node test-true-ai-first-rpa.js
```

#### 测试结果解读
```bash
✅ LLM连接测试: 验证API连通性和配置正确性
✅ AI智能特征测试: 验证AI的四大核心能力
   🧠 语义理解能力: 73.4% (优秀)
   🎯 推理能力: 33.0% (需改进)
   🔄 适应能力: 57.0% (良好)
   📚 学习能力: 75.0% (优秀)
```

### 测试原则

- 🚫 **禁止预定义响应**: 不使用Mock模板，确保真实AI调用
- 🚫 **禁止硬编码验证**: 不验证固定输出格式，而是验证智能特征
- ✅ **动态场景生成**: 每次运行生成不同的测试场景
- ✅ **概率性验证**: 验证语义一致性而非字符串匹配

### 质量指标

| 指标 | 当前值 | 目标值 | 状态 |
|------|--------|--------|------|
| LLM连接成功率 | 100% | 99%+ | ✅ 超额完成 |
| AI智能特征验证 | 75% | 80%+ | 🔄 接近目标 |
| 测试覆盖率 | 81% | 95%+ | 🔄 进行中 |
| AI-First合规率 | 100% | 100% | ✅ 完成 |

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查Chrome/Chromium是否正确安装
   - 确认Playwright依赖是否完整

2. **模型调用失败**
   - 检查API密钥是否正确配置
   - 确认网络连接是否正常

3. **WebSocket连接失败**
   - 检查端口是否被占用
   - 确认防火墙设置

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系我们

如有问题或建议，请提交 Issue 或联系开发团队。