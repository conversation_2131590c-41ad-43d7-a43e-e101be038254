/**
 * LangGraph挂起恢复机制测试
 */

require('dotenv').config();
const LangGraphTaskProcessor = require('./src/ai-assistant/src/langgraph/langgraph-task-processor');
const TicketManager = require('./src/shared/core/ticket-manager');
const logger = require('./src/ai-assistant/src/utils/logger');

class SuspendResumeTest {
    constructor() {
        this.processor = new LangGraphTaskProcessor();
        this.ticketManager = new TicketManager();
        this.testTicket = null;
    }

    async runTest() {
        try {
            logger.info('🧪 开始LangGraph挂起恢复机制测试...');

            // 1. 初始化处理器
            await this.processor.initialize();
            logger.info('✅ LangGraph任务处理器初始化完成');

            // 2. 创建测试工单
            await this.createTestTicket();

            // 3. 开始处理工单（异步）
            this.startTicketProcessing();

            // 4. 等待3秒后挂起工单
            await this.sleep(3000);
            await this.suspendTicket();

            // 5. 等待2秒后恢复工单
            await this.sleep(2000);
            await this.resumeTicket();

            // 6. 等待处理完成
            await this.waitForCompletion();

            logger.info('🎉 LangGraph挂起恢复机制测试完成！');

        } catch (error) {
            logger.error('❌ 测试失败:', error);
        } finally {
            // 清理测试数据
            await this.cleanup();
        }
    }

    async createTestTicket() {
        logger.info('📝 创建测试工单...');
        
        this.testTicket = {
            id: `test_${Date.now()}`,
            ticket_number: `TEST-${Date.now()}`,
            title: 'LangGraph挂起恢复测试工单',
            content: '这是用于测试LangGraph挂起恢复机制的工单。请导航到https://www.baidu.com并进行简单操作。',
            status: '待开始',
            priority: 1,
            created_at: new Date().toISOString()
        };

        // 将测试工单添加到数据库
        await this.ticketManager.createTicket(this.testTicket);
        logger.info(`✅ 测试工单创建成功: ${this.testTicket.id}`);
    }

    async startTicketProcessing() {
        logger.info('🚀 开始异步处理工单...');
        
        // 异步开始处理，不等待完成
        this.processingPromise = this.processor.processTicket(this.testTicket)
            .then(result => {
                logger.info('📊 工单处理结果:', result);
                this.processingResult = result;
            })
            .catch(error => {
                logger.error('❌ 工单处理异常:', error);
                this.processingError = error;
            });
        
        logger.info('⏳ 工单处理已开始（异步）');
    }

    async suspendTicket() {
        logger.info('🛑 测试挂起工单...');
        
        // 更新工单状态为挂起
        await this.ticketManager.updateTicketStatus(this.testTicket.id, '已挂起', {
            notes: '测试挂起机制',
            suspended_at: new Date().toISOString()
        });

        // 调用处理器的挂起方法
        const suspendResult = await this.processor.suspendTicket(this.testTicket.id);
        
        logger.info('✅ 工单挂起操作完成:', suspendResult);
    }

    async resumeTicket() {
        logger.info('▶️ 测试恢复工单...');
        
        // 更新工单状态为处理中
        await this.ticketManager.updateTicketStatus(this.testTicket.id, '处理中', {
            notes: '测试恢复机制',
            resumed_at: new Date().toISOString()
        });

        // 重新开始处理工单
        this.resumePromise = this.processor.processTicket(this.testTicket)
            .then(result => {
                logger.info('📊 恢复后工单处理结果:', result);
                this.resumeResult = result;
            })
            .catch(error => {
                logger.error('❌ 恢复后工单处理异常:', error);
                this.resumeError = error;
            });

        logger.info('✅ 工单恢复操作完成');
    }

    async waitForCompletion() {
        logger.info('⏳ 等待工单处理完成...');
        
        // 等待原始处理和恢复处理都完成
        if (this.processingPromise) {
            await this.processingPromise;
        }
        
        if (this.resumePromise) {
            await this.resumePromise;
        }

        logger.info('✅ 所有处理流程已完成');
    }

    async cleanup() {
        logger.info('🧹 清理测试数据...');
        
        try {
            // 停止处理器
            await this.processor.stop();
            
            // 删除测试工单（如果需要）
            // await this.ticketManager.deleteTicket(this.testTicket.id);
            
            logger.info('✅ 清理完成');
        } catch (error) {
            logger.error('⚠️ 清理时出现错误:', error);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 运行测试
if (require.main === module) {
    const test = new SuspendResumeTest();
    test.runTest().then(() => {
        logger.info('测试执行完成');
        process.exit(0);
    }).catch(error => {
        logger.error('测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = SuspendResumeTest;