/**
 * 测试SimpleLLMClient修复后的功能
 */

require('dotenv').config();
const SimpleLLMClient = require('./src/ai-assistant/src/pdca/simple-llm-client');
const logger = require('./src/ai-assistant/src/utils/logger');

async function testSimpleLLMClient() {
    console.log('====================================');
    console.log('SimpleLLMClient 功能测试');
    console.log('====================================\n');
    
    const llmClient = new SimpleLLMClient();
    
    try {
        console.log('测试基本对话功能...\n');
        
        const prompt = '请分析这个任务：上架榮記豆腐麵食的外卖商品。请用JSON格式返回需要执行的步骤。';
        const response = await llmClient.chat(prompt);
        
        console.log('✅ LLM客户端调用成功！');
        console.log('\n响应内容:');
        console.log('----------------------------------------');
        console.log(response);
        console.log('----------------------------------------\n');
        
        // 测试JSON解析
        try {
            const parsed = JSON.parse(response);
            console.log('✅ 响应是有效的JSON格式');
            console.log(`包含 ${Object.keys(parsed).length} 个字段`);
        } catch (e) {
            console.log('⚠️  响应不是JSON格式，但这可能是正常的');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ SimpleLLMClient测试失败！');
        console.error('错误详情:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('错误数据:', error.response.data);
        }
        return false;
    }
}

// 执行测试
testSimpleLLMClient().then(success => {
    console.log('\n测试结果:', success ? '✅ 成功' : '❌ 失败');
    process.exit(success ? 0 : 1);
});