const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'data', 'rpa_platform.db');
const db = new sqlite3.Database(dbPath);

async function cleanAndCreateTestTicket() {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            // 开启事务
            db.run('BEGIN TRANSACTION', (err) => {
                if (err) {
                    console.error('开启事务失败:', err);
                    reject(err);
                    return;
                }

                // 1. 清空现有数据
                console.log('清空现有工单数据...');
                
                // 清空所有相关表
                const tables = [
                    'operation_logs',
                    'message_queue',
                    'workflow_states',
                    'tasks',
                    'tickets',
                    'ai_workers'
                ];

                let cleanupCount = 0;
                tables.forEach(table => {
                    db.run(`DELETE FROM ${table}`, (err) => {
                        if (err) {
                            console.error(`清空表 ${table} 失败:`, err);
                            db.run('ROLLBACK');
                            reject(err);
                            return;
                        }
                        console.log(`✓ 已清空表: ${table}`);
                        cleanupCount++;

                        // 所有表清空完成后，创建新工单
                        if (cleanupCount === tables.length) {
                            // 2. 创建新的测试工单
                            const ticketNumber = `WO-${new Date().toISOString().slice(0,10).replace(/-/g,'')}-001`;
                            const ticketData = {
                                ticket_number: ticketNumber,
                                title: '上架榮記豆腐麵食外卖商品',
                                content: '上架榮記豆腐麵食(官也街、招牌煲仔飯、嫩滑豆腐花)的外卖商品: 菜遠牛肉飯',
                                status: '待开始',
                                priority: 1,
                                notes: JSON.stringify({
                                    restaurant: '榮記豆腐麵食',
                                    location: '官也街',
                                    specialties: ['招牌煲仔飯', '嫩滑豆腐花'],
                                    product_to_add: '菜遠牛肉飯',
                                    operation_type: '商品上架'
                                }),
                                guide_version: 'v2.0-multi-agent'
                            };

                            const sql = `
                                INSERT INTO tickets (
                                    ticket_number, title, content, status, priority, notes, guide_version
                                ) VALUES (?, ?, ?, ?, ?, ?, ?)
                            `;

                            db.run(sql, [
                                ticketData.ticket_number,
                                ticketData.title,
                                ticketData.content,
                                ticketData.status,
                                ticketData.priority,
                                ticketData.notes,
                                ticketData.guide_version
                            ], function(err) {
                                if (err) {
                                    console.error('创建工单失败:', err);
                                    db.run('ROLLBACK');
                                    reject(err);
                                    return;
                                }

                                console.log('\n✅ 成功创建测试工单:');
                                console.log('  工单号:', ticketData.ticket_number);
                                console.log('  标题:', ticketData.title);
                                console.log('  内容:', ticketData.content);
                                console.log('  工单ID:', this.lastID);

                                // 提交事务
                                db.run('COMMIT', (err) => {
                                    if (err) {
                                        console.error('提交事务失败:', err);
                                        reject(err);
                                        return;
                                    }
                                    console.log('\n✅ 数据库操作成功完成！');
                                    resolve({
                                        ticketId: this.lastID,
                                        ticketNumber: ticketData.ticket_number
                                    });
                                });
                            });
                        }
                    });
                });
            });
        });
    });
}

// 执行清理和创建
console.log('开始执行数据库操作...\n');
cleanAndCreateTestTicket()
    .then(result => {
        console.log('\n可以通过以下方式测试:');
        console.log('1. 启动服务: npm run dev');
        console.log('2. 访问前端: http://localhost:3000');
        console.log(`3. 查看工单: ${result.ticketNumber}`);
        db.close();
    })
    .catch(err => {
        console.error('\n操作失败:', err);
        db.close();
        process.exit(1);
    });