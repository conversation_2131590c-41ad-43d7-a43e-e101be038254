/**
 * 真正的AI-First RPA测试
 * 测试AI的智能特征，而非预设输出
 * 
 * 🎯 核心原则：验证AI的理解、推理、适应能力
 * 🚫 禁止：任何形式的预定义响应模板
 * ✅ 要求：AI必须真正理解和推理
 */

require('dotenv').config();
const SimpleLLMClient = require('./src/ai-assistant/src/pdca/simple-llm-client');
const logger = require('./src/ai-assistant/src/utils/logger');

class TrueAIFirstTester {
    constructor() {
        this.llmClient = new SimpleLLMClient();
        this.testResults = {
            understanding: [],
            reasoning: [],
            adaptation: [],
            learning: []
        };
    }

    /**
     * 验证AI的语义理解能力
     */
    async testUnderstandingCapability() {
        console.log('🧠 测试AI语义理解能力...\n');

        const scenarios = [
            "上架榮記豆腐麵食外卖商品: 菜遠牛肉飯",
            "为榮記豆腐麵食添加新的外卖产品：菜遠牛肉飯",
            "榮記豆腐麵食需要在外卖平台上新增菜遠牛肉飯这道菜",
            "帮助官也街的榮記豆腐麵食把菜遠牛肉飯放到外卖系统里"
        ];

        const responses = [];
        for (const scenario of scenarios) {
            try {
                const response = await this.llmClient.chat(`
请分析这个业务指令的核心意图和关键信息：
"${scenario}"

请用自然语言描述你的理解，包括：
1. 这是什么类型的操作？
2. 涉及哪些关键实体？
3. 操作的最终目标是什么？
                `.trim());
                
                responses.push({
                    scenario,
                    understanding: response
                });
                
                console.log(`场景: ${scenario}`);
                console.log(`AI理解: ${response.substring(0, 100)}...\n`);
                
            } catch (error) {
                console.log(`❌ 场景理解失败: ${error.message}\n`);
                return false;
            }
        }

        // 验证语义一致性：不同表达方式，AI是否理解为相同意图
        const consistencyScore = this.validateSemanticConsistency(responses);
        console.log(`语义一致性得分: ${(consistencyScore * 100).toFixed(1)}%`);
        
        return consistencyScore > 0.7; // 70%以上一致性认为通过
    }

    /**
     * 验证AI的推理能力
     */
    async testReasoningCapability() {
        console.log('🎯 测试AI推理能力...\n');

        const complexScenario = `
业务背景：榮記豆腐麵食是官也街的一家餐厅，主营招牌煲仔飯和嫩滑豆腐花。
当前状况：餐厅想要扩展外卖业务，需要在外卖平台上架新商品。
具体需求：上架菜遠牛肉飯作为外卖商品。
约束条件：
1. 必须确保商品信息准确
2. 需要设置合适的商品分类
3. 要考虑外卖配送的特殊要求
4. 价格需要考虑配送成本

请推理出完成这个任务需要考虑的关键因素和可能的操作步骤。
        `.trim();

        try {
            const response = await this.llmClient.chat(complexScenario);
            console.log(`AI推理结果: ${response}\n`);

            // 验证推理质量
            const reasoningQuality = this.validateReasoningQuality(response);
            console.log(`推理质量得分: ${(reasoningQuality * 100).toFixed(1)}%`);
            
            return reasoningQuality > 0.6;
            
        } catch (error) {
            console.log(`❌ 推理测试失败: ${error.message}\n`);
            return false;
        }
    }

    /**
     * 验证AI的适应能力
     */
    async testAdaptationCapability() {
        console.log('🔄 测试AI适应能力...\n');

        const adaptationScenarios = [
            {
                context: "正常情况",
                scenario: "上架榮記豆腐麵食外卖商品: 菜遠牛肉飯"
            },
            {
                context: "系统异常",
                scenario: "上架榮記豆腐麵食外卖商品: 菜遠牛肉飯，但是系统显示商家信息不存在"
            },
            {
                context: "权限限制",
                scenario: "上架榮記豆腐麵食外卖商品: 菜遠牛肉飯，但是当前账户没有商品管理权限"
            },
            {
                context: "信息不完整",
                scenario: "上架榮記豆腐麵食外卖商品: 菜遠牛肉飯，但是缺少商品价格和描述信息"
            }
        ];

        let adaptationScore = 0;
        for (const { context, scenario } of adaptationScenarios) {
            try {
                const response = await this.llmClient.chat(`
情况：${context}
任务：${scenario}

请分析这种情况下应该如何处理，包括：
1. 识别出的问题或挑战
2. 可能的解决方案
3. 需要采取的具体行动
                `.trim());

                console.log(`${context}: ${response.substring(0, 80)}...\n`);
                
                // 验证AI是否识别出了情况的特殊性
                const adaptationQuality = this.validateAdaptationResponse(response, context);
                adaptationScore += adaptationQuality;
                
            } catch (error) {
                console.log(`❌ ${context}适应测试失败: ${error.message}\n`);
            }
        }

        const avgAdaptationScore = adaptationScore / adaptationScenarios.length;
        console.log(`适应能力得分: ${(avgAdaptationScore * 100).toFixed(1)}%`);
        
        return avgAdaptationScore > 0.5;
    }

    /**
     * 验证语义一致性
     */
    validateSemanticConsistency(responses) {
        // 检查关键概念是否在所有响应中都被识别
        const keyTerms = ['上架', '榮記豆腐麵食', '菜遠牛肉飯', '外卖', '商品'];
        let consistencyCount = 0;
        
        for (const term of keyTerms) {
            const mentionCount = responses.filter(r => 
                r.understanding.includes(term) || 
                r.understanding.includes('商品') || 
                r.understanding.includes('产品') ||
                r.understanding.includes('添加') ||
                r.understanding.includes('新增')
            ).length;
            
            if (mentionCount >= responses.length * 0.7) { // 70%以上提及
                consistencyCount++;
            }
        }
        
        return consistencyCount / keyTerms.length;
    }

    /**
     * 验证推理质量
     */
    validateReasoningQuality(response) {
        let qualityScore = 0;
        
        // 检查是否考虑了多个维度
        const dimensions = [
            '商品信息', '分类', '价格', '配送', '准确', '设置'
        ];
        
        for (const dimension of dimensions) {
            if (response.includes(dimension) || 
                response.includes('信息') || 
                response.includes('考虑') ||
                response.includes('需要')) {
                qualityScore += 0.2;
            }
        }
        
        // 检查是否有逻辑结构
        if (response.includes('步骤') || response.includes('首先') || response.includes('然后')) {
            qualityScore += 0.2;
        }
        
        return Math.min(qualityScore, 1.0);
    }

    /**
     * 验证适应性响应
     */
    validateAdaptationResponse(response, context) {
        let adaptationScore = 0;
        
        switch (context) {
            case "系统异常":
                if (response.includes('检查') || response.includes('确认') || response.includes('验证')) {
                    adaptationScore += 0.5;
                }
                break;
            case "权限限制":
                if (response.includes('权限') || response.includes('联系') || response.includes('申请')) {
                    adaptationScore += 0.5;
                }
                break;
            case "信息不完整":
                if (response.includes('补充') || response.includes('收集') || response.includes('完善')) {
                    adaptationScore += 0.5;
                }
                break;
        }
        
        // 检查是否提出了具体的解决方案
        if (response.includes('解决') || response.includes('处理') || response.includes('方案')) {
            adaptationScore += 0.5;
        }
        
        return Math.min(adaptationScore, 1.0);
    }

    /**
     * 运行完整的AI-First测试
     */
    async runCompleteTest() {
        console.log('🤖 开始真正的AI-First RPA测试\n');
        console.log('🎯 测试目标：验证AI的智能特征，而非预设输出\n');
        
        const results = {
            understanding: false,
            reasoning: false,
            adaptation: false
        };

        try {
            // 测试理解能力
            results.understanding = await this.testUnderstandingCapability();
            
            // 测试推理能力
            results.reasoning = await this.testReasoningCapability();
            
            // 测试适应能力
            results.adaptation = await this.testAdaptationCapability();
            
        } catch (error) {
            console.error('💥 测试执行异常:', error);
        }

        // 输出最终结果
        console.log('📊 AI-First测试结果汇总:');
        console.log(`🧠 语义理解能力: ${results.understanding ? '✅ 通过' : '❌ 失败'}`);
        console.log(`🎯 推理能力: ${results.reasoning ? '✅ 通过' : '❌ 失败'}`);
        console.log(`🔄 适应能力: ${results.adaptation ? '✅ 通过' : '❌ 失败'}`);
        
        const passedCount = Object.values(results).filter(Boolean).length;
        const totalCount = Object.keys(results).length;
        const successRate = passedCount / totalCount;
        
        console.log(`\n🎯 AI智能特征验证成功率: ${(successRate * 100).toFixed(1)}%`);
        
        if (successRate >= 0.67) { // 至少2/3通过
            console.log('\n🎉 AI-First RPA系统智能特征验证通过！');
            console.log('✨ 系统展现了真正的AI驱动能力');
            return true;
        } else {
            console.log('\n⚠️ AI-First RPA系统需要进一步优化AI能力');
            return false;
        }
    }
}

// 运行测试
if (require.main === module) {
    const tester = new TrueAIFirstTester();
    tester.runCompleteTest().catch(console.error);
}

module.exports = TrueAIFirstTester;
