/**
 * 真实的端到端测试
 * 测试系统能否完成一个简单的导航任务
 */

require('dotenv').config();
const sqlite3 = require('sqlite3').verbose();
const LangGraphTaskProcessor = require('./src/ai-assistant/src/langgraph/langgraph-task-processor');
const logger = require('./src/ai-assistant/src/utils/logger');

// 数据库路径
const DB_PATH = './data/rpa_platform.db';

/**
 * 创建测试工单
 */
async function createTestTicket() {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(DB_PATH);
        
        const ticket = {
            ticket_number: `TEST-${Date.now()}`,
            title: '测试导航任务',
            content: '请导航到BD商户后台登录页面（https://uat-merchant.aomiapp.com/#/bdlogin），然后截图。',
            status: '待开始',
            priority: 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        
        db.run(`
            INSERT INTO tickets (ticket_number, title, content, status, priority, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
            ticket.ticket_number,
            ticket.title,
            ticket.content,
            ticket.status,
            ticket.priority,
            ticket.created_at,
            ticket.updated_at
        ], function(err) {
            if (err) {
                reject(err);
            } else {
                ticket.id = this.lastID;
                resolve(ticket);
            }
        });
        
        db.close();
    });
}

/**
 * 查询工单状态
 */
async function getTicketStatus(ticketId) {
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(DB_PATH);
        
        db.get(`
            SELECT status, notes, summary, processing_time
            FROM tickets
            WHERE id = ?
        `, [ticketId], (err, row) => {
            if (err) {
                reject(err);
            } else {
                resolve(row);
            }
        });
        
        db.close();
    });
}

/**
 * 运行端到端测试
 */
async function runE2ETest() {
    logger.info('🚀 开始真实的端到端测试');
    
    try {
        // 1. 创建测试工单
        logger.info('📝 创建测试工单...');
        const ticket = await createTestTicket();
        logger.info(`✅ 工单创建成功: ${ticket.ticket_number} (ID: ${ticket.id})`);
        
        // 2. 初始化任务处理器
        logger.info('🔧 初始化LangGraph任务处理器...');
        const processor = new LangGraphTaskProcessor();
        await processor.initialize();
        logger.info('✅ 任务处理器初始化完成');
        
        // 3. 处理工单
        logger.info('🎯 开始处理工单...');
        const startTime = Date.now();
        
        const result = await processor.processTicket(ticket);
        
        const duration = (Date.now() - startTime) / 1000;
        logger.info(`✅ 工单处理完成，耗时: ${duration.toFixed(2)}秒`);
        
        // 4. 验证结果
        logger.info('🔍 验证处理结果...');
        const finalStatus = await getTicketStatus(ticket.id);
        
        logger.info('📊 最终状态:', {
            status: finalStatus.status,
            notes: finalStatus.notes,
            summary: finalStatus.summary,
            processing_time: finalStatus.processing_time
        });
        
        // 5. 判断测试是否成功
        if (finalStatus.status === '已完成') {
            logger.info('🎉 端到端测试通过！系统成功完成了导航任务。');
            return true;
        } else {
            logger.error('❌ 端到端测试失败：工单未能成功完成');
            return false;
        }
        
    } catch (error) {
        logger.error('💥 端到端测试出错:', error);
        return false;
    }
}

// 主函数
(async () => {
    logger.info('=' * 50);
    logger.info('真实端到端测试 - 验证系统能否完成简单任务');
    logger.info('=' * 50);
    
    const success = await runE2ETest();
    
    if (success) {
        logger.info('\n✅ 测试成功！系统能够处理真实的RPA任务。');
        process.exit(0);
    } else {
        logger.error('\n❌ 测试失败！系统存在问题，需要进一步修复。');
        process.exit(1);
    }
})();