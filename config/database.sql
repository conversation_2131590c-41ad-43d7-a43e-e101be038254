-- RPA自动化平台数据库初始化脚本

-- 工单表
CREATE TABLE IF NOT EXISTS tickets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_number VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT '待开始',
    notes TEXT,
    summary TEXT,
    priority INTEGER DEFAULT 0,
    task_list TEXT, -- JSON格式存储任务列表
    progress INTEGER DEFAULT 0, -- 进度百分比
    report TEXT, -- 执行报告
    queue_priority INTEGER, -- 队列优先级
    processor VARCHAR(50), -- 处理器标识
    started_at DATETIME, -- 开始处理时间
    error_details TEXT, -- 错误详情
    failed_at DATETIME, -- 失败时间
    suspended_at DATETIME, -- 挂起时间
    images TEXT, -- JSON格式存储图片信息(base64)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    processing_time REAL, -- 改为REAL类型支持小数
    assigned_worker_id VARCHAR(50),
    guide_version VARCHAR(20)
);

-- 任务表
CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_id INTEGER NOT NULL,
    task_order INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT '待开始',
    action_type VARCHAR(50),
    target_selector TEXT,
    action_data TEXT,
    screenshot_path VARCHAR(500),
    result_summary TEXT,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE
);

-- AI工作线程表
CREATE TABLE IF NOT EXISTS ai_workers (
    id VARCHAR(50) PRIMARY KEY,
    status VARCHAR(20) NOT NULL DEFAULT 'idle',
    current_ticket_id INTEGER,
    browser_session_id VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_heartbeat DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (current_ticket_id) REFERENCES tickets(id) ON DELETE SET NULL
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    key VARCHAR(100) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_id INTEGER,
    task_id INTEGER,
    worker_id VARCHAR(50),
    operation_type VARCHAR(50) NOT NULL,
    operation_data TEXT,
    result VARCHAR(20),
    message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- 消息队列表
CREATE TABLE IF NOT EXISTS message_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_id INTEGER NOT NULL,
    priority_score INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'queued',
    queued_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE
);

-- LangGraph工作流状态表
CREATE TABLE IF NOT EXISTS workflow_states (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workflow_id VARCHAR(100) UNIQUE NOT NULL,
    ticket_id INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    current_node VARCHAR(50),
    node_history TEXT, -- JSON数组存储节点历史
    cycle_count INTEGER DEFAULT 0,
    pdca_phase VARCHAR(10), -- plan, do, check, act
    execution_context TEXT, -- JSON格式存储执行上下文
    checkpoint_data TEXT, -- JSON格式存储检查点数据
    error_history TEXT, -- JSON数组存储错误历史
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_active_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    suspended_at DATETIME,
    completed_at DATETIME,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tickets_status ON tickets(status);
CREATE INDEX IF NOT EXISTS idx_tickets_created_at ON tickets(created_at);
CREATE INDEX IF NOT EXISTS idx_tickets_number ON tickets(ticket_number);

CREATE INDEX IF NOT EXISTS idx_tasks_ticket_id ON tasks(ticket_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_order ON tasks(ticket_id, task_order);

CREATE INDEX IF NOT EXISTS idx_workers_status ON ai_workers(status);
CREATE INDEX IF NOT EXISTS idx_workers_ticket ON ai_workers(current_ticket_id);

CREATE INDEX IF NOT EXISTS idx_logs_ticket_id ON operation_logs(ticket_id);
CREATE INDEX IF NOT EXISTS idx_logs_created_at ON operation_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_queue_status ON message_queue(status);
CREATE INDEX IF NOT EXISTS idx_queue_priority ON message_queue(priority_score);

CREATE INDEX IF NOT EXISTS idx_workflow_ticket_id ON workflow_states(ticket_id);
CREATE INDEX IF NOT EXISTS idx_workflow_status ON workflow_states(status);
CREATE INDEX IF NOT EXISTS idx_workflow_id ON workflow_states(workflow_id);

-- 插入初始配置数据
INSERT OR REPLACE INTO system_config (key, value, description) VALUES
('guide_version', '1.0.0', '当前操作指引版本'),
('max_concurrent_workers', '5', '最大并发工作线程数'),
('min_concurrent_workers', '2', '最小并发工作线程数'),
('browser_pool_size', '8', '浏览器实例池大小'),
('worker_idle_timeout', '300000', '工作线程空闲超时时间(毫秒)'),
('heartbeat_interval', '30000', '心跳检测间隔(毫秒)'),
('max_retries', '3', '最大重试次数'),
('retry_delay', '1000', '重试延迟时间(毫秒)'),
('screenshot_enabled', 'true', '是否启用截图功能'),
('log_level', 'debug', '日志级别');

-- 插入示例数据(仅开发环境)
INSERT OR IGNORE INTO tickets (ticket_number, title, content, status, priority) VALUES
('***********-001', '下架门店外卖商品', '请下架"测试门店"的"测试商品A"和"测试商品B"', '待开始', 1),
('***********-002', '更新门店信息', '请更新"示例门店"的营业时间为9:00-22:00', '待开始', 0),
('***********-003', '批量商品上架', '请将以下商品批量上架到"新开门店"：商品1、商品2、商品3', '待开始', 2);