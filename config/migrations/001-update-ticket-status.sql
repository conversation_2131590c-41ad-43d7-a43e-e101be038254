-- 更新工单状态系统
-- 新的状态流转：待开始 → 排队中 → 处理中 → 已完成/待补充信息

-- 添加新字段到工单表
ALTER TABLE tickets ADD COLUMN task_list TEXT; -- JSON格式的任务清单
ALTER TABLE tickets ADD COLUMN progress INTEGER DEFAULT 0; -- 进度百分比
ALTER TABLE tickets ADD COLUMN report TEXT; -- 富文本报告
ALTER TABLE tickets ADD COLUMN queue_priority INTEGER; -- 队列优先级分数

-- 创建消息队列表
CREATE TABLE IF NOT EXISTS message_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_id INTEGER NOT NULL,
    priority_score INTEGER NOT NULL, -- 优先级分数 (高=2, 中=1, 低=0) * 1000 - 创建时间戳
    status VARCHAR(20) DEFAULT 'queued', -- queued, processing, completed
    queued_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE
);

-- 创建截图表
CREATE TABLE IF NOT EXISTS screenshots (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ticket_id INTEGER NOT NULL,
    file_path TEXT NOT NULL,
    description TEXT,
    step_index INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE
);

-- 创建系统指引表
CREATE TABLE IF NOT EXISTS system_guides (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    keywords TEXT NOT NULL, -- JSON数组，匹配关键词
    required_fields TEXT NOT NULL, -- JSON数组，必需字段
    template TEXT NOT NULL, -- 任务模板
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_queue_priority ON message_queue(priority_score);
CREATE INDEX IF NOT EXISTS idx_queue_status ON message_queue(status);
CREATE INDEX IF NOT EXISTS idx_screenshots_ticket ON screenshots(ticket_id);
CREATE INDEX IF NOT EXISTS idx_guides_name ON system_guides(name);

-- 插入系统指引数据
INSERT OR REPLACE INTO system_guides (name, keywords, required_fields, template) VALUES
('商品下架指引', '["下架", "商品", "门店"]', '["门店名称", "商品名称"]', '{"steps": ["登录商户后台", "搜索并进入目标门店", "进入门店管理后台", "进入商品管理页面", "搜索并下架目标商品"]}'),
('商品上架指引', '["上架", "商品", "发布"]', '["门店名称", "商品信息", "价格"]', '{"steps": ["登录商户后台", "搜索并进入目标门店", "进入门店管理后台", "进入商品管理页面", "添加新商品", "设置商品信息", "发布商品"]}'),
('门店信息更新指引', '["门店", "信息", "更新"]', '["门店名称", "更新内容"]', '{"steps": ["登录商户后台", "搜索并进入目标门店", "进入门店管理后台", "更新门店信息", "保存更改"]}'),
('通用浏览器操作指引', '["浏览器", "操作", "网站"]', '["目标网站", "操作描述"]', '{"steps": ["打开浏览器", "导航到目标网站", "执行指定操作", "截图记录结果"]}');

-- 更新现有工单状态（如果需要）
UPDATE tickets SET status = '待开始' WHERE status NOT IN ('待开始', '排队中', '处理中', '已完成', '待补充信息');
