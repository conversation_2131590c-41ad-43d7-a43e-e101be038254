/**
 * 验证Agent (Validator Agent)
 * 负责验证操作结果和任务完成度
 */

const AgentBase = require('./agent-base');
const logger = require('../utils/logger');

class ValidatorAgent extends AgentBase {
    constructor() {
        const systemPrompt = `你是一个严格的结果验证专家。你负责验证每个操作的结果以及整体任务的完成度。

## 重要：验证循环控制
- 同一个操作最多验证3次
- 如果连续3次验证失败，必须停止验证并建议调整策略
- 避免陷入验证循环

## 验证层次：

### 1. 操作级验证
- 单个操作是否成功执行
- 操作是否产生了预期效果
- 是否有错误或异常发生
- 操作的副作用

### 2. 步骤级验证
- 业务步骤是否完成
- 多个相关操作的综合效果
- 数据的一致性
- 流程的连贯性

### 3. 任务级验证
- 整体任务目标是否达成
- 所有必要步骤是否完成
- 最终状态是否符合预期
- 是否有遗漏或未完成项

## 验证方法：

### 1. 状态对比
- 操作前后的页面变化
- URL变化（页面跳转）
- 标题变化
- 内容变化
- 元素增减

### 2. 内容验证
- 成功消息的出现
- 错误提示的检测
- 数据的正确显示
- 状态标记的改变

### 3. 结构验证
- 页面结构的改变
- 新元素的出现
- 旧元素的消失
- 表单的清空或填充

### 4. 业务验证
- 业务ID的生成
- 状态的转换
- 数据的保存确认
- 操作日志的产生

## 验证标准：

### 成功标志
- 明确的成功提示（如"保存成功"、"已上架"）
- 页面跳转到预期位置
- 数据显示在列表中
- 状态标签的改变
- 操作按钮的变化（如"上架"变为"下架"）

### 失败标志
- 错误提示信息
- 页面无变化
- 验证失败消息
- 异常或超时
- 数据未保存

### 需要人工确认
- 模糊的提示信息
- 部分成功的情况
- 需要额外确认的操作
- 系统行为异常

## 特殊情况处理：

### 1. 异步操作
- 等待加载完成
- 检查进度指示
- 轮询状态更新
- 设置合理超时

### 2. 批量操作
- 验证所有项目
- 统计成功/失败数
- 识别失败原因
- 生成详细报告

### 3. 多步骤验证
- 中间步骤的验证
- 最终结果的确认
- 回滚情况的检测
- 数据一致性检查

## 输出格式：
{
    "validation": {
        "level": "操作级/步骤级/任务级",
        "passed": true/false,
        "confidence": 0.0-1.0
    },
    "evidence": {
        "stateChanges": [
            {
                "type": "变化类型",
                "before": "变化前",
                "after": "变化后",
                "significance": "重要性"
            }
        ],
        "successIndicators": ["成功标志列表"],
        "errorIndicators": ["错误标志列表"],
        "screenshots": ["相关截图"]
    },
    "analysis": {
        "whatHappened": "发生了什么",
        "asExpected": true/false,
        "deviations": ["偏差列表"],
        "implications": "影响分析"
    },
    "taskProgress": {
        "currentStage": "当前阶段",
        "completedSteps": ["已完成步骤"],
        "remainingSteps": ["剩余步骤"],
        "overallProgress": "0-100%",
        "blockers": ["阻碍因素"]
    },
    "recommendation": {
        "nextAction": "建议的下一步行动",
        "reasoning": "建议理由",
        "alternatives": ["备选方案"],
        "risks": ["潜在风险"]
    },
    "taskCompleted": false,
    "completionCriteriaMet": {
        "criteria1": true/false,
        "criteria2": true/false
    }
}

## 验证决策树：
1. 如果有明确的成功标志 → 验证通过
2. 如果有明确的错误标志 → 验证失败
3. 如果页面无变化 → 可能失败，需要进一步检查
4. 如果部分成功 → 详细分析，提供建议
5. 如果无法判断 → 请求人工确认

## 重要提醒：
- 不要被表面现象迷惑
- 深入分析业务含义
- 考虑操作的最终目的
- 保持客观和严格
- 提供可行的建议

记住：验证的目的是确保任务真正完成，而不是假装完成！`;

        super('ValidatorAgent', systemPrompt);
        
        // 验证次数追踪器 - 防止无限递归
        this.validationTracker = new Map(); // key: 操作标识, value: 验证次数
        this.maxValidationAttempts = 3;
    }

    /**
     * 验证操作结果
     */
    async validateOperation(operation, beforeState, afterState) {
        // 创建操作标识
        const operationId = `${operation.type}_${operation.target || 'unknown'}_${beforeState.url || 'nourl'}`;
        
        // 检查验证次数
        const attempts = this.validationTracker.get(operationId) || 0;
        if (attempts >= this.maxValidationAttempts) {
            logger.warn(`⚠️ 操作验证达到最大次数限制`, { operationId, attempts });
            return {
                validation: {
                    level: '操作级',
                    passed: false,
                    confidence: 0.1
                },
                error: 'VALIDATION_LIMIT_EXCEEDED',
                recommendation: {
                    nextAction: 'SKIP_OR_ALTERNATIVE',
                    reasoning: `此操作已验证${attempts}次但仍未通过，建议跳过或尝试替代方案`,
                    alternatives: ['使用不同的定位策略', '检查页面是否正确', '尝试其他操作路径']
                }
            };
        }
        
        // 增加验证次数
        this.validationTracker.set(operationId, attempts + 1);
        
        const input = {
            action: 'validate_operation',
            operation: operation,
            stateBefore: beforeState,
            stateAfter: afterState,
            expectedResult: operation.expectedResult,
            validationAttempt: attempts + 1
        };
        
        return await this.execute(input);
    }

    /**
     * 验证任务完成度
     */
    async validateTaskCompletion(taskGoal, executionSummary, currentState) {
        // 创建任务标识
        const taskId = `task_${taskGoal?.substring(0, 50)}_${currentState.url || 'nourl'}`;
        
        // 检查验证次数
        const attempts = this.validationTracker.get(taskId) || 0;
        if (attempts >= this.maxValidationAttempts) {
            logger.warn(`⚠️ 任务验证达到最大次数限制`, { taskId, attempts });
            return {
                validation: {
                    level: '任务级',
                    passed: false,
                    confidence: 0.1
                },
                taskCompleted: false,
                error: 'TASK_VALIDATION_LIMIT_EXCEEDED',
                recommendation: {
                    nextAction: 'NEED_HELP',
                    reasoning: `任务已验证${attempts}次但仍未完成，需要人工介入`,
                    alternatives: ['检查任务目标是否正确', '确认当前页面状态', '重新评估执行策略']
                }
            };
        }
        
        // 增加验证次数
        this.validationTracker.set(taskId, attempts + 1);
        
        const input = {
            action: 'validate_task_completion',
            originalGoal: taskGoal,
            executionHistory: executionSummary,
            finalState: currentState,
            startTime: this.context.startTime,
            evidenceCollected: this.context.evidence || [],
            validationAttempt: attempts + 1
        };
        
        return await this.execute(input);
    }

    /**
     * 增量验证（验证单个步骤）
     */
    async validateStep(stepDescription, evidence) {
        const input = {
            action: 'validate_step',
            step: stepDescription,
            evidence: evidence,
            previousValidations: this.getRecentHistory(2)
        };
        
        return await this.execute(input);
    }

    /**
     * 对比验证（用于验证数据变化）
     */
    async compareStates(beforeState, afterState, expectedChanges) {
        const input = {
            action: 'compare_states',
            before: beforeState,
            after: afterState,
            expected: expectedChanges
        };
        
        return await this.execute(input);
    }

    /**
     * 生成执行报告（增强功能）
     * 整合自reporting-agent的核心功能
     */
    async generateExecutionReport(taskInfo, executionSteps, validationResults) {
        const input = {
            action: 'generate_report',
            task: taskInfo,
            steps: executionSteps,
            validations: validationResults,
            timestamp: new Date().toISOString()
        };
        
        const promptAddition = `

## 报告生成功能（增强）：
当action为"generate_report"时，生成详细的执行报告，包括：
1. 执行摘要（任务状态、完成度、耗时等）
2. 关键操作记录（每个步骤的执行情况）
3. 验证结果汇总（成功/失败的验证点）
4. 业务影响分析（完成了什么业务目标）
5. 问题与建议（遇到的问题和改进建议）

返回格式：
{
    "summary": {
        "status": "成功|部分成功|失败",
        "completionRate": 0-100,
        "duration": "执行耗时",
        "keyResult": "关键结果描述"
    },
    "executionDetails": {
        "totalSteps": 总步骤数,
        "completedSteps": 完成步骤数,
        "failedSteps": 失败步骤数,
        "keyOperations": ["关键操作列表"]
    },
    "businessImpact": {
        "achieved": ["达成的业务目标"],
        "pending": ["未完成的目标"]
    },
    "recommendations": ["建议列表"]
}`;
        
        // 临时修改提示词以包含报告生成功能
        const originalPrompt = this.systemPrompt;
        this.systemPrompt += promptAddition;
        
        try {
            const result = await this.execute(input);
            return result;
        } finally {
            // 恢复原始提示词
            this.systemPrompt = originalPrompt;
        }
    }
    
    /**
     * 清空历史记录（重写父类方法）
     */
    clearHistory() {
        super.clearHistory();
        // 清空验证追踪器
        this.validationTracker.clear();
        logger.info('✅ ValidatorAgent验证追踪器已重置');
    }
}

module.exports = ValidatorAgent;