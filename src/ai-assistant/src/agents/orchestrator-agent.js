/**
 * 总控Agent (Orchestrator Agent)
 * 负责理解工单、协调其他Agent、管理执行流程
 * 增强功能：包含任务规划能力、模式识别、智能策略调整
 */

const AgentBase = require('./agent-base');
const logger = require('../utils/logger');

class OrchestratorAgent extends AgentBase {
    constructor() {
        const systemPrompt = `你是一个智能RPA系统的总控制器。你的核心职责是理解用户需求、规划执行方案并协调其他Agent完成任务。

## 你的职责：
1. 深入理解用户的工单需求，提取关键信息
2. 将复杂任务分解为可执行的步骤（但不要硬编码步骤）
3. 制定灵活的执行计划
4. 协调其他Agent完成任务
5. 监控执行进度并根据实际情况调整策略
6. 判断任务是否完成或需要人工介入
7. 【新增】识别失败模式，避免重复错误
8. 【新增】基于历史成功率动态调整策略

## 你可以调用的Agent：
- **ObserverAgent**: 观察和理解页面状态
- **DecisionAgent**: 基于观察结果做出下一步决策
- **ExecutorAgent**: 执行具体的页面操作
- **ValidatorAgent**: 验证操作结果和任务完成度

## 工作原则：
1. **不做任何假设**：不要假设页面结构、流程顺序或具体步骤
2. **基于观察决策**：所有决策都应该基于实时的页面观察结果
3. **灵活适应**：根据实际情况调整执行策略
4. **循环验证**：每个操作后都要验证结果，确保达到预期效果
5. **智能判断**：基于验证结果决定是继续、重试还是调整策略
6. **【新增】模式学习**：记住哪些操作序列有效，哪些无效
7. **【新增】失败预防**：如果某个操作连续失败3次，必须换方法

## 执行流程：
1. 分析工单，理解核心目标
2. 制定初步执行计划（可动态调整）
3. 让ObserverAgent观察当前状态
4. 基于观察结果，让DecisionAgent决定下一步
5. 让ExecutorAgent执行操作
6. 让ValidatorAgent验证结果
7. 根据验证结果决定下一步（继续/调整/完成/请求帮助）
8. 重复3-7直到任务完成

## 任务规划功能（增强）：
当分析工单时，你需要：
1. 识别任务类型（商品管理、信息更新、查询等）
2. 提取关键实体（门店、商品、操作类型等）
3. 制定成功标准
4. 识别潜在风险点
5. 准备备用策略
6. 【新增】检查历史执行记录，了解类似任务的成功模式

## 策略调整规则：
1. 如果连续3次执行相同操作失败，必须改变策略
2. 如果页面状态长时间不变，说明操作无效，需要新方法
3. 如果验证一直失败，可能是目标理解有误，需要重新分析
4. 优先使用历史上成功率高的操作序列
5. 避免进入"操作-验证-失败"的死循环

## 输出格式：
当分析工单时，返回：
{
    "understanding": "对工单的理解",
    "goal": "核心目标",
    "keyEntities": ["关键实体列表"],
    "strategy": "执行策略（灵活的，非硬编码的）",
    "firstStep": "建议的第一步行动",
    "taskPlan": {
        "taskType": "任务类型",
        "targetEntities": {
            "store": "目标门店（如果有）",
            "product": "目标商品（如果有）"
        },
        "successCriteria": ["成功标准列表"],
        "riskPoints": ["潜在风险点"],
        "fallbackStrategy": "备用策略"
    }
}

当协调执行时，返回：
{
    "currentStatus": "当前状态总结",
    "nextAgent": "下一个要调用的Agent",
    "instruction": "给下一个Agent的指令",
    "reasoning": "这样做的原因",
    "strategyAdjustment": "策略调整建议（如果需要）",
    "avoidActions": ["应该避免的操作列表"]
}

当判断任务完成时，返回：
{
    "taskCompleted": true/false,
    "completionEvidence": ["完成的证据"],
    "summary": "执行总结",
    "nextAction": "COMPLETE/CONTINUE/NEED_HELP",
    "learnedPatterns": ["本次执行学到的有效模式"]
}

记住：保持灵活，不要硬编码任何流程！从失败中学习，不断优化策略！`;

        super('OrchestratorAgent', systemPrompt);
        
        // 操作模式记录（记录成功和失败的操作序列）
        this.operationPatterns = {
            successful: [],
            failed: [],
            maxPatternSize: 50
        };
        
        // 策略调整记录
        this.strategyAdjustments = [];
        
        // 失败操作黑名单（临时的，每个任务重置）
        this.failedOperations = new Map();
    }

    /**
     * 分析工单（增强版）
     */
    async analyzeWorkOrder(workOrder) {
        // 查找相似任务的成功模式
        const similarPatterns = this.findSimilarSuccessPatterns(workOrder);
        
        const input = {
            action: 'analyze_workorder',
            workOrder: workOrder,
            historicalPatterns: similarPatterns,
            previousFailures: this.getRecentFailures()
        };
        
        const result = await this.execute(input);
        
        // 重置失败操作记录（新任务开始）
        this.failedOperations.clear();
        
        return result;
    }

    /**
     * 协调下一步行动（增强版）
     */
    async coordinateNextStep(currentState, validationResult, executionHistory = []) {
        // 分析当前执行模式
        const currentPattern = this.analyzeCurrentPattern(executionHistory);
        
        // 检查是否重复失败
        const repeatedFailures = this.checkRepeatedFailures(executionHistory);
        
        // 获取应该避免的操作
        const avoidOperations = this.getOperationsToAvoid();
        
        const input = {
            action: 'coordinate_next_step',
            currentState: currentState,
            validationResult: validationResult,
            executionHistory: this.getRecentHistory(5),
            currentPattern: currentPattern,
            repeatedFailures: repeatedFailures,
            avoidOperations: Array.from(avoidOperations),
            strategyAdjustments: this.strategyAdjustments.slice(-3)
        };
        
        const result = await this.execute(input);
        
        // 如果建议了策略调整，记录下来
        if (result.strategyAdjustment) {
            this.recordStrategyAdjustment(result.strategyAdjustment);
        }
        
        return result;
    }

    /**
     * 判断任务是否完成（增强版）
     */
    async evaluateCompletion(workOrder, executionSummary) {
        const input = {
            action: 'evaluate_completion',
            originalWorkOrder: workOrder,
            executionSummary: executionSummary,
            finalState: this.context.currentState,
            operationPatterns: this.getCurrentOperationPattern()
        };
        
        const result = await this.execute(input);
        
        // 如果任务完成，记录成功模式
        if (result.taskCompleted) {
            this.recordSuccessPattern(workOrder, executionSummary);
        }
        
        return result;
    }

    /**
     * 查找相似任务的成功模式
     */
    findSimilarSuccessPatterns(workOrder) {
        return this.operationPatterns.successful
            .filter(pattern => {
                // 简单的相似度检查（可以根据需要改进）
                return pattern.taskType === workOrder.type ||
                       pattern.keywords.some(kw => workOrder.content.includes(kw));
            })
            .slice(0, 3); // 返回最相关的3个模式
    }

    /**
     * 分析当前执行模式
     */
    analyzeCurrentPattern(executionHistory) {
        if (executionHistory.length < 2) return null;
        
        const recentOps = executionHistory.slice(-5);
        const pattern = {
            operations: recentOps.map(h => ({
                agent: h.coordination?.nextAgent,
                action: h.decision?.action?.type,
                result: h.validation?.validation?.passed ? 'success' : 'failed'
            })),
            repeatingCycle: this.detectRepeatingCycle(recentOps),
            failureRate: this.calculateFailureRate(recentOps)
        };
        
        return pattern;
    }

    /**
     * 检查重复失败
     */
    checkRepeatedFailures(executionHistory) {
        const failures = {};
        
        executionHistory.slice(-10).forEach(entry => {
            if (entry.validation && !entry.validation.validation?.passed) {
                const key = `${entry.decision?.action?.type}_${entry.decision?.action?.target}`;
                failures[key] = (failures[key] || 0) + 1;
                
                // 如果某个操作失败3次以上，加入黑名单
                if (failures[key] >= 3) {
                    this.failedOperations.set(key, {
                        count: failures[key],
                        lastFailure: entry.timestamp
                    });
                }
            }
        });
        
        return failures;
    }

    /**
     * 获取应该避免的操作
     */
    getOperationsToAvoid() {
        const avoidList = [];
        
        // 从失败操作黑名单中获取
        for (const [operation, info] of this.failedOperations) {
            if (info.count >= 3) {
                avoidList.push(operation);
            }
        }
        
        return avoidList;
    }

    /**
     * 记录策略调整
     */
    recordStrategyAdjustment(adjustment) {
        this.strategyAdjustments.push({
            adjustment: adjustment,
            timestamp: new Date().toISOString()
        });
        
        // 限制记录数量
        if (this.strategyAdjustments.length > 20) {
            this.strategyAdjustments.shift();
        }
    }

    /**
     * 记录成功模式
     */
    recordSuccessPattern(workOrder, executionSummary) {
        const pattern = {
            taskType: workOrder.type,
            keywords: this.extractKeywords(workOrder.content),
            operationSequence: this.extractOperationSequence(executionSummary),
            totalSteps: executionSummary.totalSteps,
            timestamp: new Date().toISOString()
        };
        
        this.operationPatterns.successful.push(pattern);
        
        // 限制存储的模式数量
        if (this.operationPatterns.successful.length > this.operationPatterns.maxPatternSize) {
            this.operationPatterns.successful.shift();
        }
    }

    /**
     * 获取最近的失败记录
     */
    getRecentFailures() {
        return Array.from(this.failedOperations.entries())
            .map(([op, info]) => ({ operation: op, ...info }))
            .slice(-5);
    }

    /**
     * 检测重复循环
     */
    detectRepeatingCycle(operations) {
        if (operations.length < 4) return false;
        
        // 简单的循环检测：查看最后的操作是否在重复
        const lastOp = operations[operations.length - 1];
        const secondLastOp = operations[operations.length - 2];
        
        let repeatCount = 0;
        for (let i = operations.length - 3; i >= 0; i--) {
            if (this.isSimilarOperation(operations[i], lastOp) ||
                this.isSimilarOperation(operations[i], secondLastOp)) {
                repeatCount++;
            }
        }
        
        return repeatCount >= 2;
    }

    /**
     * 判断两个操作是否相似
     */
    isSimilarOperation(op1, op2) {
        return op1.decision?.action?.type === op2.decision?.action?.type &&
               op1.decision?.action?.target === op2.decision?.action?.target;
    }

    /**
     * 计算失败率
     */
    calculateFailureRate(operations) {
        if (operations.length === 0) return 0;
        
        const failures = operations.filter(op => 
            op.validation && !op.validation.validation?.passed
        ).length;
        
        return failures / operations.length;
    }

    /**
     * 提取关键词
     */
    extractKeywords(content) {
        // 简单的关键词提取（可以改进）
        const keywords = [];
        const patterns = ['上架', '下架', '商品', '门店', '搜索', '查询', '更新', '删除'];
        
        patterns.forEach(pattern => {
            if (content.includes(pattern)) {
                keywords.push(pattern);
            }
        });
        
        return keywords;
    }

    /**
     * 提取操作序列
     */
    extractOperationSequence(executionSummary) {
        // 从执行摘要中提取成功的操作序列
        return executionSummary.recentSteps
            ?.filter(step => step.result === 'success')
            .map(step => step.action) || [];
    }

    /**
     * 获取当前操作模式
     */
    getCurrentOperationPattern() {
        return {
            successful: this.operationPatterns.successful.length,
            failed: this.operationPatterns.failed.length,
            recentAdjustments: this.strategyAdjustments.slice(-3)
        };
    }

    /**
     * 重置Agent状态（增强版）
     */
    reset() {
        super.clearHistory();
        this.failedOperations.clear();
        this.strategyAdjustments = [];
        // 保留成功模式用于学习
    }
}

module.exports = OrchestratorAgent;