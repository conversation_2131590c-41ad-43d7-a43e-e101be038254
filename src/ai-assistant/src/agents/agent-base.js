/**
 * Agent基类
 * 所有Agent的基础类，提供通用功能
 */

const logger = require('../utils/logger');
const SimpleLLMClient = require('../pdca/simple-llm-client');

class AgentBase {
    constructor(name, systemPrompt) {
        this.agentName = name;  // 添加agentName属性
        this.name = name;
        this.systemPrompt = systemPrompt;
        this.llmClient = new SimpleLLMClient();
        this.conversationHistory = [];
        this.context = {};
    }

    /**
     * 执行Agent任务
     * @param {Object} input - 输入参数
     * @returns {Object} Agent执行结果
     */
    async execute(input) {
        try {
            logger.info(`🤖 ${this.name} 开始执行`, { input });
            
            // 构建对话消息
            const messages = this.buildMessages(input);
            
            // 调用LLM
            const response = await this.llmClient.chatWithMessages(messages);
            
            // 解析响应
            const result = this.parseResponse(response);
            
            // 记录历史
            this.conversationHistory.push({
                input,
                response: result,
                timestamp: new Date().toISOString()
            });
            
            logger.info(`✅ ${this.name} 执行完成`, { result });
            
            return result;
            
        } catch (error) {
            logger.error(`❌ ${this.name} 执行失败`, error);
            throw error;
        }
    }

    /**
     * 构建消息列表
     */
    buildMessages(input) {
        const messages = [
            {
                role: 'system',
                content: this.systemPrompt
            }
        ];

        // 添加上下文信息
        if (this.context && Object.keys(this.context).length > 0) {
            messages.push({
                role: 'system',
                content: `当前上下文信息：\n${JSON.stringify(this.context, null, 2)}`
            });
        }

        // 添加用户输入
        messages.push({
            role: 'user',
            content: typeof input === 'string' ? input : JSON.stringify(input, null, 2)
        });

        return messages;
    }

    /**
     * 解析LLM响应
     * 子类应该重写此方法以实现特定的解析逻辑
     */
    parseResponse(response) {
        try {
            // 尝试解析JSON
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            // 如果不是JSON，返回原始文本
            return { response };
            
        } catch (error) {
            logger.warn(`${this.name} 响应解析失败，返回原始文本`);
            return { response };
        }
    }

    /**
     * 更新Agent上下文
     */
    updateContext(updates) {
        this.context = {
            ...this.context,
            ...updates
        };
    }

    /**
     * 清空历史记录
     */
    clearHistory() {
        this.conversationHistory = [];
    }

    /**
     * 获取最近的N条历史记录
     */
    getRecentHistory(n = 5) {
        return this.conversationHistory.slice(-n);
    }
}

module.exports = AgentBase;