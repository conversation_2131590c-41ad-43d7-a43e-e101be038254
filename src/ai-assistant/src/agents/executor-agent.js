/**
 * 执行Agent (Executor Agent)
 * 负责将决策转化为具体的浏览器操作
 */

const AgentBase = require('./agent-base');
const logger = require('../utils/logger');

class ExecutorAgent extends AgentBase {
    constructor() {
        const systemPrompt = `你是一个精确的操作执行专家。你负责将决策转化为具体的浏览器操作指令。

## 执行原则：

### 1. 精确性
- 准确定位目标元素
- 使用最可靠的定位策略
- 验证元素状态（可见、可用）
- 处理动态加载的元素

### 2. 稳定性
- 合理的操作节奏（不要太快）
- 等待页面响应
- 处理异步操作
- 避免竞态条件

### 3. 容错性
- 处理元素不存在的情况
- 处理操作超时
- 处理页面错误
- 提供详细的错误信息

## 元素定位策略（按优先级）：

### 1. 唯一标识符
- ID属性（如果唯一）
- 特定的data属性
- aria-label等辅助属性

### 2. 文本内容
- 按钮文本：button:has-text("确定")
- 链接文本：a:has-text("下一步")
- 标签文本：label:has-text("用户名")

### 3. 属性匹配
- placeholder：input[placeholder*="请输入"]
- type：input[type="email"]
- class（谨慎使用）：.submit-button

### 4. 结构关系
- 父子关系：form > input:first-child
- 兄弟关系：label + input
- 包含关系：div:has(span:text("标题"))

### 5. 位置索引
- 第N个元素：input >> nth=0
- 可见元素：button:visible
- 特定区域：.modal button

## 操作类型详解：

### 1. 导航 (navigate)
- 直接访问URL
- 等待页面加载完成
- 处理重定向

### 2. 输入 (type)
- 清空现有内容
- 输入新内容
- 处理特殊字符
- 支持快捷键

### 3. 点击 (click)
- 确保元素可点击
- 处理遮挡情况
- 支持右键、双击
- 等待点击响应

### 4. 选择 (select)
- 下拉框选择
- 单选/复选框
- 日期选择器
- 自动完成选项

### 5. 特殊操作
- 文件上传
- 拖拽操作
- 滚动页面
- 键盘操作

## 执行步骤：
1. 解析决策指令
2. 定位目标元素
3. 验证元素状态
4. 执行操作
5. 等待响应
6. 收集执行证据

## 输出格式：
{
    "executed": true/false,
    "operation": {
        "type": "执行的操作类型",
        "target": "目标元素",
        "value": "操作值",
        "timestamp": "执行时间"
    },
    "locator": {
        "strategy": "使用的定位策略",
        "selector": "实际使用的选择器",
        "alternatives": ["备选选择器"]
    },
    "result": {
        "success": true/false,
        "duration": "执行耗时(ms)",
        "elementFound": true/false,
        "elementState": {
            "visible": true/false,
            "enabled": true/false,
            "value": "元素当前值"
        }
    },
    "evidence": {
        "screenshot": "操作后截图",
        "pageUrl": "当前URL",
        "pageTitle": "页面标题"
    },
    "error": {
        "type": "错误类型",
        "message": "错误信息",
        "recoverable": true/false
    }
}

## 特殊情况处理：

### 1. 自动完成下拉框
- 输入触发：缓慢输入，每个字符后暂停
- 等待选项：等待下拉选项出现
- 选择匹配：点击包含目标文本的选项

### 2. 动态加载
- 等待元素出现
- 处理加载指示器
- 设置合理的超时时间

### 3. 弹窗对话框
- 识别对话框类型
- 定位对话框内的元素
- 处理遮罩层

### 4. 文件上传
- 使用文件选择器
- 处理拖拽上传
- 等待上传完成

记住：
- 宁慢勿错，稳定性优先于速度
- 详细记录每个操作的结果
- 失败时提供足够的调试信息
- 考虑不同浏览器的兼容性`;

        super('ExecutorAgent', systemPrompt);
        this.mcpClient = null; // 将在初始化时注入
    }

    /**
     * 设置MCP客户端
     */
    setMCPClient(client) {
        this.mcpClient = client;
    }

    /**
     * 执行操作
     */
    async executeAction(decision) {
        const input = {
            action: 'execute_action',
            decision: decision,
            currentUrl: this.context.currentUrl,
            retryCount: this.context.retryCount || 0
        };
        
        const executionPlan = await this.execute(input);
        
        // 如果有MCP客户端，执行实际操作
        if (this.mcpClient && executionPlan.executed) {
            return await this.performBrowserAction(executionPlan);
        }
        
        return executionPlan;
    }

    /**
     * 执行浏览器操作
     */
    async performBrowserAction(plan) {
        try {
            const { operation, locator } = plan;
            let result = null;
            
            switch (operation.type) {
                case 'navigate':
                    result = await this.mcpClient.navigate(operation.value);
                    break;
                    
                case 'type':
                case 'input':
                    result = await this.mcpClient.type(
                        locator.selector || operation.target,
                        operation.value,
                        { submit: false }
                    );
                    break;
                    
                case 'click':
                    result = await this.mcpClient.click(
                        locator.selector || operation.target
                    );
                    break;
                    
                case 'select':
                    result = await this.mcpClient.select(
                        locator.selector || operation.target,
                        operation.value
                    );
                    break;
                    
                case 'wait':
                    await new Promise(resolve => 
                        setTimeout(resolve, operation.value || 2000)
                    );
                    result = { success: true };
                    break;
                    
                default:
                    logger.warn(`未知操作类型: ${operation.type}`);
                    result = { success: false, error: 'Unknown operation type' };
            }
            
            // 收集执行证据
            const evidence = await this.collectEvidence();
            
            return {
                ...plan,
                result: {
                    ...plan.result,
                    ...result
                },
                evidence
            };
            
        } catch (error) {
            logger.error('浏览器操作失败:', error);
            return {
                ...plan,
                result: {
                    success: false,
                    error: error.message
                }
            };
        }
    }

    /**
     * 收集执行证据
     */
    async collectEvidence() {
        if (!this.mcpClient) return {};
        
        try {
            const screenshot = await this.mcpClient.screenshot();
            const snapshot = await this.mcpClient.snapshot();
            
            return {
                screenshot: screenshot.screenshot,
                pageUrl: snapshot.url,
                pageTitle: snapshot.title,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            logger.warn('收集证据失败:', error);
            return {};
        }
    }

    /**
     * 处理特殊输入（如自动完成）
     */
    async handleSpecialInput(target, value, inputType) {
        const input = {
            action: 'handle_special_input',
            target: target,
            value: value,
            inputType: inputType
        };
        
        return await this.execute(input);
    }
}

module.exports = ExecutorAgent;