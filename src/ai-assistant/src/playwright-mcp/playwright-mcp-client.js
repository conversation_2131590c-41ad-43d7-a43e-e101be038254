/**
 * Playwright MCP客户端 - 基于我成功完成下架任务的经验
 * 直接使用Playwright MCP，无需本地浏览器环境
 *
 * 关键发现：页面快照是结构化文本，不是图像！
 * 无需多模态处理能力，使用字符串解析即可
 */

const axios = require('axios');

class PlaywrightMCPClient {
    constructor(logger = console) {
        this.logger = logger;
        this.isInitialized = false;
        this.currentUrl = null;
        this.currentTitle = null;

        // 快照分析器 - 处理结构化文本快照
        this.snapshotAnalyzer = null; // 暂时禁用，使用简单的文本解析
    }

    /**
     * 初始化MCP客户端
     */
    async initialize() {
        try {
            this.logger.info('🚀 初始化Playwright MCP客户端...');

            // 初始化MCP连接
            await this.initializeMCPConnection();

            this.isInitialized = true;

            this.logger.info('✅ Playwright MCP客户端初始化完成');
            return true;
        } catch (error) {
            this.logger.error('❌ Playwright MCP客户端初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化MCP连接
     */
    async initializeMCPConnection() {
        try {
            this.logger.info('🔗 初始化MCP连接...');

            // 发送初始化请求
            const initResponse = await axios.post(
                'http://127.0.0.1:8931/sse',
                {
                    jsonrpc: '2.0',
                    id: 1,
                    method: 'initialize',
                    params: {
                        protocolVersion: '2024-11-05',
                        capabilities: {
                            tools: {},
                            resources: {},
                            prompts: {}
                        },
                        clientInfo: {
                            name: 'RPA-AI-Assistant',
                            version: '1.0.0'
                        }
                    }
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/event-stream'
                    },
                    timeout: 10000
                }
            );

            if (initResponse.data.error) {
                throw new Error(`MCP初始化失败: ${initResponse.data.error.message}`);
            }

            this.logger.info('✅ MCP连接初始化成功');
            this.logger.info('📋 服务器信息:', initResponse.data.result?.serverInfo);

        } catch (error) {
            this.logger.error('❌ MCP连接初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 导航到指定URL
     */
    async navigate(url) {
        try {
            this.logger.info(`🌐 导航到: ${url}`);
            
            // 使用MCP导航 - 基于我的成功经验
            const result = await this.callMCP('browser_navigate', {
                url: url
            });
            
            this.currentUrl = url;
            this.logger.info(`✅ 导航成功: ${url}`);
            
            return {
                success: true,
                url: url,
                message: `成功导航到 ${url}`,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.error(`❌ 导航失败: ${url}`, error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 获取页面快照 - 核心感知能力
     * 返回结构化文本快照，无需多模态处理
     */
    async getPageSnapshot() {
        try {
            this.logger.info('📸 获取页面快照 (结构化文本)...');

            // 使用MCP获取页面快照 - 这是我成功的关键
            const snapshot = await this.callMCP('browser_snapshot', {});

            this.logger.info('✅ 页面快照获取成功');

            return {
                success: true,
                snapshot: snapshot,
                url: this.currentUrl,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.error('❌ 页面快照获取失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 获取并解析页面快照 - 提取可操作元素
     * 基于我成功经验：快照是结构化文本，不需要视觉AI
     */
    async getPageElements() {
        try {
            this.logger.info('🔍 获取并解析页面元素...');

            // 1. 获取原始快照
            const snapshotResult = await this.getPageSnapshot();
            if (!snapshotResult.success) {
                throw new Error(`快照获取失败: ${snapshotResult.error}`);
            }

            // 2. 解析快照文本，提取元素 (简化版本)
            const elements = this.parseSnapshotSimple(snapshotResult.snapshot);

            this.logger.info(`✅ 页面元素解析完成: ${elements.buttons.length}个按钮, ${elements.inputs.length}个输入框, ${elements.links.length}个链接`);

            return {
                success: true,
                elements: elements,
                rawSnapshot: snapshotResult.snapshot,
                url: this.currentUrl,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.error('❌ 页面元素获取失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 智能查找元素 - 基于文本描述或ref
     */
    async findElement(description) {
        try {
            this.logger.info(`🎯 查找元素: ${description}`);

            // 获取页面元素
            const elementsResult = await this.getPageElements();
            if (!elementsResult.success) {
                throw new Error(`页面元素获取失败: ${elementsResult.error}`);
            }

            const { elements } = elementsResult;

            // 尝试多种查找策略
            let foundElement = null;

            // 1. 按ref精确查找
            if (description.startsWith('e') && /^e\d+$/.test(description)) {
                foundElement = this.findByRef(elements, description);
            }

            // 2. 按文本内容查找
            if (!foundElement) {
                foundElement = this.findByText(elements, description);
            }

            // 3. 按描述模糊查找
            if (!foundElement) {
                foundElement = this.findByDescription(elements, description);
            }

            if (foundElement) {
                this.logger.info(`✅ 找到元素: ${foundElement.type} (ref: ${foundElement.ref})`);
                return {
                    success: true,
                    element: foundElement,
                    timestamp: new Date().toISOString()
                };
            } else {
                this.logger.warn(`⚠️ 未找到元素: ${description}`);
                return {
                    success: false,
                    error: `未找到匹配的元素: ${description}`,
                    timestamp: new Date().toISOString()
                };
            }
        } catch (error) {
            this.logger.error(`❌ 元素查找失败: ${description}`, error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 点击元素
     */
    async clickElement(element, ref) {
        try {
            this.logger.info(`🖱️ 点击元素: ${element}`);
            
            // 使用MCP点击 - 基于我的成功经验
            const result = await this.callMCP('browser_click', {
                element: element,
                ref: ref
            });
            
            this.logger.info(`✅ 点击成功: ${element}`);
            
            return {
                success: true,
                element: element,
                ref: ref,
                message: `成功点击 ${element}`,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.error(`❌ 点击失败: ${element}`, error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 输入文本
     */
    async typeText(element, ref, text) {
        try {
            this.logger.info(`⌨️ 输入文本到: ${element}`);
            
            // 使用MCP输入文本 - 基于我的成功经验
            const result = await this.callMCP('browser_type', {
                element: element,
                ref: ref,
                text: text
            });
            
            this.logger.info(`✅ 文本输入成功: ${text}`);
            
            return {
                success: true,
                element: element,
                text: text,
                message: `成功输入文本: ${text}`,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.error(`❌ 文本输入失败: ${element}`, error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 等待
     */
    async wait(time) {
        try {
            this.logger.info(`⏳ 等待 ${time} 秒...`);
            
            // 使用MCP等待
            const result = await this.callMCP('browser_wait_for', {
                time: time
            });
            
            this.logger.info(`✅ 等待完成`);
            
            return {
                success: true,
                time: time,
                message: `等待 ${time} 秒完成`,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.error(`❌ 等待失败`, error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 截图
     */
    async takeScreenshot(filename = null) {
        try {
            this.logger.info('📷 截图...');
            
            // 使用MCP截图
            const result = await this.callMCP('browser_take_screenshot', {
                filename: filename
            });
            
            this.logger.info('✅ 截图成功');
            
            return {
                success: true,
                filename: filename,
                message: '截图成功',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.error('❌ 截图失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 执行通用操作 - 基于我的成功经验
     */
    async executeAction(action) {
        try {
            this.logger.info(`🎬 执行操作: ${action.action_type}`);
            
            let result;
            
            switch (action.action_type) {
                case 'navigate':
                    result = await this.navigate(action.action_data.url);
                    break;
                    
                case 'click':
                    result = await this.clickElement(
                        action.target_element.description,
                        action.target_element.ref
                    );
                    break;
                    
                case 'type':
                case 'fill':
                    result = await this.typeText(
                        action.target_element.description,
                        action.target_element.ref,
                        action.action_data.text
                    );
                    break;
                    
                case 'wait':
                    result = await this.wait(action.action_data.timeout || 3);
                    break;
                    
                case 'screenshot':
                    result = await this.takeScreenshot(action.action_data.filename);
                    break;
                    
                default:
                    throw new Error(`不支持的操作类型: ${action.action_type}`);
            }
            
            return result;
        } catch (error) {
            this.logger.error(`❌ 操作执行失败: ${action.action_type}`, error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 按ref查找元素
     */
    findByRef(elements, ref) {
        const allElements = [
            ...elements.buttons,
            ...elements.inputs,
            ...elements.links,
            ...elements.headings
        ];

        return allElements.find(el => el.ref === ref);
    }

    /**
     * 按文本内容查找元素
     */
    findByText(elements, text) {
        const allElements = [
            ...elements.buttons,
            ...elements.inputs,
            ...elements.links,
            ...elements.headings
        ];

        return allElements.find(el =>
            el.text?.includes(text) ||
            el.placeholder?.includes(text)
        );
    }

    /**
     * 按描述模糊查找元素
     */
    findByDescription(elements, description) {
        const allElements = [
            ...elements.buttons,
            ...elements.inputs,
            ...elements.links,
            ...elements.headings
        ];

        return allElements.find(el =>
            el.description?.includes(description) ||
            el.text?.toLowerCase().includes(description.toLowerCase()) ||
            el.placeholder?.toLowerCase().includes(description.toLowerCase())
        );
    }

    /**
     * 检测页面状态变化 - 验证操作是否成功
     */
    async detectPageChanges(beforeSnapshot, afterSnapshot) {
        try {
            this.logger.info('🔄 检测页面状态变化...');

            const changes = this.detectChangesSimple(beforeSnapshot, afterSnapshot);

            this.logger.info(`✅ 变化检测完成: ${changes.changedElements.length}个元素变化, ${changes.alerts.length}个提示`);

            return {
                success: true,
                changes: changes,
                hasSuccess: changes.alerts.some(alert => alert.isSuccess),
                hasError: changes.alerts.some(alert => alert.isError),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.error('❌ 页面变化检测失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 调用MCP方法 - 连接到真正的MCP服务
     */
    async callMCP(method, params) {
        this.logger.info(`📡 调用真实MCP: ${method}`);
        this.logger.info(`📋 参数:`, params);

        try {
            const response = await axios.post(
                'http://127.0.0.1:8931', // 修正后的URL
                {
                    jsonrpc: '2.0',
                    id: Date.now(), // 使用时间戳作为唯一ID
                    method: method,
                    params: params
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/event-stream'
                    },
                    timeout: 60000 // 设置60秒超时
                }
            );

            if (response.data.error) {
                throw new Error(`MCP服务返回错误: ${response.data.error.message}`);
            }

            this.logger.info(`✅ MCP响应成功: ${method}`);
            return response.data.result; // 返回结果

        } catch (error) {
            this.logger.error(`❌ MCP调用失败: ${method}`, error.message);
            // 如果是网络错误，可以检查MCP服务是否已启动
            if (error.code === 'ECONNREFUSED') {
                this.logger.error('   ➡️ 错误提示: 无法连接到MCP服务。请确认您已在命令行成功运行 npx @playwright/mcp ...');
            }
            throw error; // 抛出异常，让上层逻辑处理
        }
    }

    /**
     * 简化的快照解析 (临时实现)
     */
    parseSnapshotSimple(snapshot) {
        return {
            buttons: [],
            inputs: [],
            links: [],
            headings: []
        };
    }

    /**
     * 简化的变化检测 (临时实现)
     */
    detectChangesSimple(beforeSnapshot, afterSnapshot) {
        return {
            changedElements: [],
            alerts: [],
            hasSuccess: false,
            hasError: false
        };
    }

    /**
     * 清理资源
     */
    async cleanup() {
        try {
            this.logger.info('🧹 清理MCP客户端资源...');
            // MCP客户端无需特殊清理
            this.logger.info('✅ MCP客户端资源清理完成');
        } catch (error) {
            this.logger.error('❌ MCP客户端资源清理失败:', error);
        }
    }
}

module.exports = PlaywrightMCPClient;
