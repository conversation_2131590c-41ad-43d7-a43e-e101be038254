/**
 * 增强版SimpleMCPClient - 支持更强大的元素定位和操作
 * 直接使用Playwright库，提供更灵活的浏览器控制
 */

const { chromium } = require('playwright');

class EnhancedSimpleMCPClient {
    constructor(logger = console) {
        this.logger = logger;
        this.browser = null;
        this.page = null;
        this.isInitialized = false;
    }

    /**
     * 初始化浏览器
     */
    async initialize() {
        try {
            this.logger.info('🚀 启动增强版浏览器客户端...');
            
            this.browser = await chromium.launch({
                headless: process.env.BROWSER_HEADLESS === 'true',
                args: ['--no-sandbox', '--disable-setuid-sandbox'],
                channel: 'chrome' // 使用系统Chrome浏览器
            });
            
            this.page = await this.browser.newPage();
            
            // 设置默认超时
            this.page.setDefaultTimeout(30000);
            
            // 设置视口大小
            await this.page.setViewportSize({ width: 1366, height: 768 });
            
            this.isInitialized = true;
            
            this.logger.info('✅ 增强版浏览器客户端初始化完成');
            return true;
        } catch (error) {
            this.logger.error('❌ 增强版浏览器客户端初始化失败:', error.message);
            return false;
        }
    }

    /**
     * 导航到URL
     */
    async navigate(url) {
        try {
            this.logger.info(`🌐 导航到: ${url}`);
            const response = await this.page.goto(url, { 
                waitUntil: 'domcontentloaded',
                timeout: 30000 
            });
            
            const title = await this.page.title();
            const currentUrl = this.page.url();
            
            this.logger.info(`✅ 导航成功: ${title}`);
            
            return {
                success: true,
                url: currentUrl,
                title: title,
                status: response?.status(),
                message: `成功导航到 ${url}`
            };
        } catch (error) {
            this.logger.error(`❌ 导航失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 获取页面快照
     */
    async snapshot() {
        try {
            this.logger.info('📸 获取页面快照...');
            
            const title = await this.page.title();
            const url = this.page.url();
            
            // 获取所有可交互元素
            const elements = await this.page.evaluate(() => {
                const result = {
                    buttons: [],
                    inputs: [],
                    links: [],
                    selects: []
                };
                
                // 获取按钮
                document.querySelectorAll('button, input[type="button"], input[type="submit"], [role="button"]').forEach((el, i) => {
                    result.buttons.push({
                        type: 'button',
                        text: el.textContent?.trim() || el.value || '',
                        ref: `button[${i}]`,
                        selector: `button:nth-child(${i+1})`,
                        id: el.id,
                        class: el.className
                    });
                });
                
                // 获取输入框
                document.querySelectorAll('input[type="text"], input[type="search"], input[type="email"], input[type="password"], textarea').forEach((el, i) => {
                    result.inputs.push({
                        type: 'input',
                        placeholder: el.placeholder || '',
                        ref: `input[${i}]`,
                        selector: `input:nth-child(${i+1})`,
                        id: el.id,
                        name: el.name,
                        class: el.className
                    });
                });
                
                // 获取链接
                document.querySelectorAll('a[href]').forEach((el, i) => {
                    if (i < 20) { // 限制数量
                        result.links.push({
                            type: 'link',
                            text: el.textContent?.trim() || '',
                            href: el.href,
                            ref: `link[${i}]`,
                            selector: `a:nth-child(${i+1})`,
                            id: el.id
                        });
                    }
                });
                
                // 获取下拉框
                document.querySelectorAll('select').forEach((el, i) => {
                    result.selects.push({
                        type: 'select',
                        ref: `select[${i}]`,
                        selector: `select:nth-child(${i+1})`,
                        id: el.id,
                        name: el.name,
                        options: Array.from(el.options).map(opt => ({
                            value: opt.value,
                            text: opt.text
                        }))
                    });
                });
                
                return result;
            });
            
            this.logger.info('✅ 页面快照获取成功');
            
            return {
                success: true,
                url: url,
                title: title,
                elements: elements,
                message: '页面快照获取成功'
            };
        } catch (error) {
            this.logger.error(`❌ 页面快照失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 点击元素 - 支持多种定位方式
     */
    async click(elementOrSelector, options = {}) {
        try {
            this.logger.info(`🖱️ 点击元素: ${elementOrSelector}`);
            
            let selector = elementOrSelector;
            
            // 如果是文本描述，尝试多种定位策略
            if (!elementOrSelector.includes('[') && !elementOrSelector.includes('#') && !elementOrSelector.includes('.')) {
                // 尝试通过文本查找
                const possibleSelectors = [
                    `text="${elementOrSelector}"`,
                    `button:has-text("${elementOrSelector}")`,
                    `a:has-text("${elementOrSelector}")`,
                    `[role="button"]:has-text("${elementOrSelector}")`,
                    `span:has-text("${elementOrSelector}")`,
                    `div:has-text("${elementOrSelector}")`
                ];
                
                // 尝试每个选择器
                for (const sel of possibleSelectors) {
                    const count = await this.page.locator(sel).count();
                    if (count > 0) {
                        selector = sel;
                        if (count > 1 && options.index !== undefined) {
                            selector = `${sel} >> nth=${options.index}`;
                        }
                        break;
                    }
                }
            }
            
            // 等待元素可见和可点击
            await this.page.waitForSelector(selector, { 
                state: 'visible',
                timeout: options.timeout || 10000 
            });
            
            // 滚动到元素
            await this.page.locator(selector).scrollIntoViewIfNeeded();
            
            // 点击元素
            await this.page.click(selector, {
                timeout: options.timeout || 10000,
                force: options.force || false
            });
            
            // 等待导航或网络空闲
            if (options.waitForNavigation) {
                await this.page.waitForLoadState('networkidle', { timeout: 5000 }).catch(() => {});
            }
            
            this.logger.info(`✅ 点击成功: ${elementOrSelector}`);
            
            return {
                success: true,
                element: elementOrSelector,
                selector: selector,
                message: `成功点击 ${elementOrSelector}`
            };
        } catch (error) {
            this.logger.error(`❌ 点击失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 输入文本 - 支持多种定位方式
     */
    async type(elementOrSelector, text, options = {}) {
        try {
            this.logger.info(`⌨️ 输入文本: "${text}" 到 ${elementOrSelector}`);
            
            let selector = elementOrSelector;
            
            // 如果是纯数字（ref值），直接作为索引使用
            if (/^\d+$/.test(elementOrSelector)) {
                // 使用nth选择器找到对应的输入框
                selector = `input >> nth=${elementOrSelector}`;
                const count = await this.page.locator(selector).count();
                if (count === 0) {
                    // 如果没找到input，尝试textarea
                    selector = `textarea >> nth=${elementOrSelector}`;
                }
            }
            // 如果是描述性文本，尝试通过placeholder或label查找
            else if (!elementOrSelector.includes('[') && !elementOrSelector.includes('#') && !elementOrSelector.includes('.') && !elementOrSelector.includes('>')) {
                const possibleSelectors = [
                    `input[placeholder*="${elementOrSelector}"]`,
                    `textarea[placeholder*="${elementOrSelector}"]`,
                    `input[name*="${elementOrSelector}"]`,
                    `[aria-label*="${elementOrSelector}"]`,
                    `label:has-text("${elementOrSelector}") >> input`,
                    `label:has-text("${elementOrSelector}") >> textarea`
                ];
                
                for (const sel of possibleSelectors) {
                    const count = await this.page.locator(sel).count();
                    if (count > 0) {
                        selector = sel;
                        break;
                    }
                }
            }
            
            // 等待元素可见
            await this.page.waitForSelector(selector, { 
                state: 'visible',
                timeout: options.timeout || 10000 
            });
            
            // 清空现有内容
            if (options.clear !== false) {
                await this.page.fill(selector, '');
            }
            
            // 输入文本
            await this.page.fill(selector, text);
            
            // 如果需要，按Enter键
            if (options.pressEnter) {
                await this.page.press(selector, 'Enter');
            }
            
            this.logger.info(`✅ 输入成功: ${text}`);
            
            return {
                success: true,
                element: elementOrSelector,
                text: text,
                message: `成功输入文本到 ${elementOrSelector}`
            };
        } catch (error) {
            this.logger.error(`❌ 输入失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 选择下拉框选项
     */
    async select(elementOrSelector, value, options = {}) {
        try {
            this.logger.info(`📋 选择下拉框: ${elementOrSelector} -> ${value}`);
            
            let selector = elementOrSelector;
            
            // 定位select元素
            if (!elementOrSelector.includes('select') && !elementOrSelector.includes('#') && !elementOrSelector.includes('.')) {
                selector = `select:has-text("${elementOrSelector}")`;
            }
            
            // 选择选项
            await this.page.selectOption(selector, value, {
                timeout: options.timeout || 10000
            });
            
            this.logger.info(`✅ 选择成功: ${value}`);
            
            return {
                success: true,
                element: elementOrSelector,
                value: value,
                message: `成功选择 ${value}`
            };
        } catch (error) {
            this.logger.error(`❌ 选择失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 等待元素出现
     */
    async waitForElement(selector, options = {}) {
        try {
            this.logger.info(`⏳ 等待元素: ${selector}`);
            
            await this.page.waitForSelector(selector, {
                state: options.state || 'visible',
                timeout: options.timeout || 30000
            });
            
            this.logger.info(`✅ 元素已出现: ${selector}`);
            
            return {
                success: true,
                selector: selector,
                message: `元素已出现`
            };
        } catch (error) {
            this.logger.error(`❌ 等待超时: ${error.message}`);
            throw error;
        }
    }

    /**
     * 截图
     */
    async screenshot(options = {}) {
        try {
            this.logger.info(`📸 截图操作`);
            
            const screenshot = await this.page.screenshot({
                fullPage: options.fullPage || false,
                type: 'png'
            });
            
            // 转换为base64
            const base64 = screenshot.toString('base64');
            
            this.logger.info('✅ 截图成功');
            
            return {
                success: true,
                screenshot: `data:image/png;base64,${base64}`,
                message: '截图成功'
            };
        } catch (error) {
            this.logger.error(`❌ 截图失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 关闭浏览器
     */
    async close() {
        try {
            if (this.browser) {
                await this.browser.close();
                this.logger.info('✅ 浏览器已关闭');
            }
        } catch (error) {
            this.logger.error('❌ 关闭浏览器失败:', error.message);
        }
    }
}

module.exports = { EnhancedSimpleMCPClient };