/**
 * 最简化的MCP客户端 - 解决连接层复杂度问题
 * 直接使用Playwright库，绕过复杂的MCP协议
 */

const { chromium } = require('playwright');

class SimpleMCPClient {
    constructor(logger = console) {
        this.logger = logger;
        this.browser = null;
        this.page = null;
        this.isInitialized = false;
    }

    /**
     * 初始化浏览器
     */
    async initialize() {
        try {
            this.logger.info('🚀 启动简化版浏览器客户端...');
            
            this.browser = await chromium.launch({
                headless: false, // 显示浏览器窗口
                args: ['--no-sandbox', '--disable-setuid-sandbox']
            });
            
            this.page = await this.browser.newPage();
            this.isInitialized = true;
            
            this.logger.info('✅ 简化版浏览器客户端初始化完成');
            return true;
        } catch (error) {
            this.logger.error('❌ 简化版浏览器客户端初始化失败:', error.message);
            return false;
        }
    }

    /**
     * 导航到URL
     */
    async navigate(url) {
        try {
            this.logger.info(`🌐 导航到: ${url}`);
            await this.page.goto(url, { waitUntil: 'networkidle' });
            
            const title = await this.page.title();
            const currentUrl = this.page.url();
            
            this.logger.info(`✅ 导航成功: ${title}`);
            
            return {
                success: true,
                url: currentUrl,
                title: title,
                message: `成功导航到 ${url}`
            };
        } catch (error) {
            this.logger.error(`❌ 导航失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 获取页面快照
     */
    async snapshot() {
        try {
            this.logger.info('📸 获取页面快照...');
            
            const title = await this.page.title();
            const url = this.page.url();
            
            // 获取页面的基本元素信息
            const elements = await this.page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"]'))
                    .map((el, i) => ({ type: 'button', text: el.textContent?.trim() || el.value, ref: `btn_${i}` }));
                
                const inputs = Array.from(document.querySelectorAll('input[type="text"], input[type="search"], textarea'))
                    .map((el, i) => ({ type: 'input', placeholder: el.placeholder, ref: `input_${i}` }));
                
                const links = Array.from(document.querySelectorAll('a[href]'))
                    .slice(0, 10) // 只取前10个链接
                    .map((el, i) => ({ type: 'link', text: el.textContent?.trim(), href: el.href, ref: `link_${i}` }));
                
                return { buttons, inputs, links };
            });
            
            this.logger.info('✅ 页面快照获取成功');
            
            return {
                success: true,
                url: url,
                title: title,
                elements: elements,
                message: '页面快照获取成功'
            };
        } catch (error) {
            this.logger.error(`❌ 页面快照失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 点击元素
     */
    async click(element, ref) {
        try {
            this.logger.info(`🖱️ 点击元素: ${element} (${ref})`);
            
            // 根据ref类型选择不同的选择器策略
            let selector;
            if (ref.startsWith('btn_')) {
                const index = parseInt(ref.split('_')[1]);
                selector = `button:nth-of-type(${index + 1}), input[type="button"]:nth-of-type(${index + 1}), input[type="submit"]:nth-of-type(${index + 1})`;
            } else if (ref.startsWith('link_')) {
                const index = parseInt(ref.split('_')[1]);
                selector = `a[href]:nth-of-type(${index + 1})`;
            } else {
                // 尝试通过文本内容查找
                selector = `text="${element}"`;
            }
            
            await this.page.click(selector, { timeout: 10000 });
            
            this.logger.info(`✅ 点击成功: ${element}`);
            
            return {
                success: true,
                element: element,
                message: `成功点击 ${element}`
            };
        } catch (error) {
            this.logger.error(`❌ 点击失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 输入文本
     */
    async type(element, ref, text) {
        try {
            this.logger.info(`⌨️ 输入文本: ${text} 到 ${element}`);
            
            // 根据ref类型选择不同的选择器策略
            let selector;
            if (ref.startsWith('input_')) {
                const index = parseInt(ref.split('_')[1]);
                selector = `input[type="text"]:nth-of-type(${index + 1}), input[type="search"]:nth-of-type(${index + 1}), textarea:nth-of-type(${index + 1})`;
            } else {
                // 尝试通过placeholder查找
                selector = `input[placeholder*="${element}"], textarea[placeholder*="${element}"]`;
            }
            
            await this.page.fill(selector, text, { timeout: 10000 });
            
            this.logger.info(`✅ 输入成功: ${text}`);
            
            return {
                success: true,
                element: element,
                text: text,
                message: `成功输入 ${text}`
            };
        } catch (error) {
            this.logger.error(`❌ 输入失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 等待
     */
    async wait(seconds) {
        try {
            this.logger.info(`⏳ 等待 ${seconds} 秒...`);
            await this.page.waitForTimeout(seconds * 1000);
            
            this.logger.info(`✅ 等待完成`);
            
            return {
                success: true,
                seconds: seconds,
                message: `等待 ${seconds} 秒完成`
            };
        } catch (error) {
            this.logger.error(`❌ 等待失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 截图
     */
    async takeScreenshot(filename) {
        try {
            const screenshotPath = filename || `screenshot-${Date.now()}.png`;
            await this.page.screenshot({ path: screenshotPath, fullPage: true });
            
            this.logger.info(`✅ 截图保存: ${screenshotPath}`);
            
            return {
                success: true,
                path: screenshotPath,
                message: `截图已保存到 ${screenshotPath}`
            };
        } catch (error) {
            this.logger.error(`❌ 截图失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 关闭浏览器
     */
    async close() {
        try {
            if (this.browser) {
                await this.browser.close();
                this.logger.info('✅ 浏览器已关闭');
            }
        } catch (error) {
            this.logger.error('❌ 关闭浏览器失败:', error.message);
        }
    }
}

module.exports = { SimpleMCPClient };
