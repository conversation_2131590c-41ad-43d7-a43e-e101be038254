/**
 * 基于成功测试的正确MCP客户端实现
 * 使用持久化SSE连接 + HTTP POST的混合模式
 */

const axios = require('axios');
const EventSource = require('eventsource').EventSource || require('eventsource');

class MCPClientV2 {
    constructor(logger = console) {
        this.logger = logger;
        this.mcpEndpoint = 'http://127.0.0.1:8931/sse';
        this.postEndpoint = null;
        this.sessionId = null;
        this.eventSource = null;
        this.isInitialized = false;
        this.requestId = 1;
        this.protocolVersion = '2024-11-05';
        this.pendingRequests = new Map(); // 存储待处理的请求
    }

    /**
     * 初始化MCP连接 - 基于成功的持久化SSE实现
     */
    async initialize() {
        return new Promise((resolve, reject) => {
            try {
                this.logger.info('🔌 建立持久化SSE连接...');

                this.eventSource = new EventSource(this.mcpEndpoint);

                this.eventSource.onopen = () => {
                    this.logger.info('✅ SSE连接已建立');
                };

                // 监听endpoint事件
                this.eventSource.addEventListener('endpoint', (event) => {
                    this.logger.info('📨 收到endpoint事件:', event.data);
                    this.handleEndpointEvent(event.data, resolve, reject);
                });

                // 监听所有消息
                this.eventSource.onmessage = (event) => {
                    this.logger.info('📨 收到SSE消息:', event.type, event.data);
                    this.handleSSEMessage(event);
                };

                this.eventSource.onerror = (error) => {
                    this.logger.error('❌ SSE连接错误:', error);
                    reject(new Error('SSE连接失败'));
                };

                // 超时处理
                setTimeout(() => {
                    if (!this.sessionId) {
                        reject(new Error('获取sessionId超时'));
                    }
                }, 10000);

            } catch (error) {
                this.logger.error('❌ MCP初始化失败:', error.message);
                reject(error);
            }
        });
    }

    /**
     * 处理endpoint事件
     */
    handleEndpointEvent(data, resolve, reject) {
        this.logger.info('🔍 处理endpoint事件:', data);

        // data格式: /sse?sessionId=xxx
        const sessionMatch = data.match(/sessionId=([a-f0-9-]+)/);

        if (sessionMatch) {
            this.sessionId = sessionMatch[1];
            this.postEndpoint = data; // 使用完整的端点路径
            this.logger.info('📋 获得sessionId:', this.sessionId);
            this.logger.info('📋 获得POST端点:', this.postEndpoint);

            // 保存resolve函数，等待初始化完成
            this.initializeResolve = resolve;
            this.initializeReject = reject;

            // 立即发送初始化请求
            this.sendInitialize().catch(reject);
        }
    }

    /**
     * 处理SSE消息
     */
    handleSSEMessage(event) {
        try {
            const response = JSON.parse(event.data);
            if (response.jsonrpc === '2.0') {
                // 处理JSON-RPC响应
                if (response.id && this.pendingRequests.has(response.id)) {
                    const { resolve, reject } = this.pendingRequests.get(response.id);
                    this.pendingRequests.delete(response.id);

                    if (response.error) {
                        reject(new Error(response.error.message));
                    } else {
                        resolve(response.result);
                    }
                }

                // 特殊处理初始化响应
                if (response.id === 1 && response.result) {
                    this.logger.info('✅ MCP初始化成功');
                    this.logger.info('📋 服务器信息:', response.result.serverInfo);
                    this.isInitialized = true;

                    // 发送initialized通知
                    this.sendNotification('notifications/initialized', {});

                    // 如果有等待初始化的Promise，解决它
                    if (this.initializeResolve) {
                        this.initializeResolve(true);
                        this.initializeResolve = null;
                    }
                }
            }
        } catch (e) {
            // 不是JSON，忽略
        }
    }

    /**
     * 发送初始化请求
     */
    async sendInitialize() {
        this.logger.info('🚀 发送初始化请求...');

        const initRequest = {
            jsonrpc: '2.0',
            id: 1, // 固定ID，用于匹配SSE响应
            method: 'initialize',
            params: {
                protocolVersion: this.protocolVersion,
                capabilities: {
                    tools: {},
                    resources: {},
                    prompts: {}
                },
                clientInfo: {
                    name: 'RPA-AI-Assistant',
                    version: '2.0.0'
                }
            }
        };

        const postUrl = this.postEndpoint.startsWith('http') ?
            this.postEndpoint :
            `http://127.0.0.1:8931${this.postEndpoint}`;

        const response = await axios.post(postUrl, initRequest, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: 10000
        });

        this.logger.info('📋 POST响应:', response.data);

        // 对于SSE协议，真正的响应会通过SSE返回
        if (response.status === 200 || response.data === 'Accepted') {
            this.logger.info('✅ 初始化请求已发送，等待SSE响应...');
            return true;
        } else {
            throw new Error('初始化请求失败');
        }
    }



    /**
     * 发送HTTP请求到MCP端点 - 支持异步响应
     */
    async sendRequest(method, params = {}) {
        if (!this.isInitialized || !this.postEndpoint) {
            throw new Error('MCP客户端未初始化');
        }

        const requestId = this.requestId++;
        const request = {
            jsonrpc: '2.0',
            id: requestId,
            method: method,
            params: params
        };

        const postUrl = this.postEndpoint.startsWith('http') ?
            this.postEndpoint :
            `http://127.0.0.1:8931${this.postEndpoint}`;

        // 创建Promise来等待SSE响应
        const responsePromise = new Promise((resolve, reject) => {
            this.pendingRequests.set(requestId, { resolve, reject });

            // 设置超时
            setTimeout(() => {
                if (this.pendingRequests.has(requestId)) {
                    this.pendingRequests.delete(requestId);
                    reject(new Error('请求超时'));
                }
            }, 30000);
        });

        // 发送POST请求
        const response = await axios.post(postUrl, request, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: 10000
        });

        // 等待SSE响应
        return await responsePromise;
    }

    /**
     * 发送通知（不期望响应）
     */
    async sendNotification(method, params = {}) {
        const notification = {
            jsonrpc: '2.0',
            method: method,
            params: params
        };

        const postUrl = this.postEndpoint.startsWith('http') ?
            this.postEndpoint :
            `http://127.0.0.1:8931${this.postEndpoint}`;

        try {
            await axios.post(postUrl, notification, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            });
            this.logger.info(`✅ 通知发送成功: ${method}`);
            return true;
        } catch (error) {
            this.logger.error(`❌ 通知发送失败: ${method}`, error.message);
            return false;
        }
    }

    /**
     * 调用MCP工具
     */
    async callTool(toolName, parameters = {}) {
        if (!this.isInitialized) {
            throw new Error('MCP客户端未初始化');
        }

        // 健康检查和自动重连
        await this.ensureConnection();

        try {
            this.logger.info(`🛠️ 调用工具: ${toolName}`);
            this.logger.info(`📋 参数:`, parameters);

            const result = await this.sendRequest('tools/call', {
                name: toolName,
                arguments: parameters
            });

            this.logger.info(`✅ 工具调用成功: ${toolName}`);
            return result;
        } catch (error) {
            this.logger.error(`❌ 工具调用失败: ${toolName}`, error.message);

            // 如果是连接错误，尝试重连
            if (this.isConnectionError(error)) {
                this.logger.info('🔄 检测到连接错误，尝试重连...');
                await this.reconnect();

                // 重试一次
                try {
                    const result = await this.sendRequest('tools/call', {
                        name: toolName,
                        arguments: parameters
                    });
                    this.logger.info(`✅ 重连后工具调用成功: ${toolName}`);
                    return result;
                } catch (retryError) {
                    this.logger.error(`❌ 重连后仍然失败: ${toolName}`, retryError.message);
                    throw retryError;
                }
            }

            throw error;
        }
    }

    /**
     * 列出可用工具
     */
    async listTools() {
        if (!this.isInitialized) {
            throw new Error('MCP客户端未初始化');
        }

        try {
            const result = await this.sendRequest('tools/list');
            return result.tools || [];
        } catch (error) {
            this.logger.error('❌ 列出工具失败:', error.message);
            throw error;
        }
    }

    /**
     * 导航到URL
     */
    async navigate(url) {
        return await this.callTool('browser_navigate', { url });
    }

    /**
     * 获取页面快照 - 增强版，支持重试和等待
     */
    async snapshot(options = {}) {
        const {
            maxRetries = 3,
            waitTime = 2000,
            validateContent = true
        } = options;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                this.logger.info(`📸 获取页面快照 (尝试 ${attempt}/${maxRetries})`);

                // 等待页面稳定
                if (waitTime > 0) {
                    await this.wait(waitTime / 1000);
                }

                const result = await this.callTool('browser_snapshot', {});

                // 验证快照内容
                if (validateContent && this.isSnapshotEmpty(result)) {
                    this.logger.warn(`⚠️ 页面快照为空，尝试 ${attempt}/${maxRetries}`);
                    if (attempt < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                        continue;
                    }
                }

                this.logger.info(`✅ 页面快照获取成功 (尝试 ${attempt})`);
                return result;

            } catch (error) {
                this.logger.error(`❌ 页面快照获取失败 (尝试 ${attempt}/${maxRetries}):`, error.message);
                if (attempt === maxRetries) {
                    throw error;
                }
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }

    /**
     * 检查快照是否为空
     */
    isSnapshotEmpty(result) {
        if (!result || !result.content) return true;

        const content = Array.isArray(result.content) ? result.content[0] : result.content;
        if (!content || !content.text) return true;

        // 检查是否包含有效的页面内容
        const text = content.text;
        const hasSnapshot = text.includes('Page Snapshot') && text.includes('```yaml');
        const hasContent = text.split('```yaml')[1]?.trim().length > 0;

        return !hasSnapshot || !hasContent;
    }

    /**
     * 点击元素
     */
    async click(element, ref) {
        return await this.callTool('browser_click', { element, ref });
    }

    /**
     * 输入文本
     */
    async type(element, ref, text, options = {}) {
        return await this.callTool('browser_type', {
            element,
            ref,
            text,
            ...options
        });
    }

    /**
     * 获取页面快照 (兼容方法名)
     */
    async getPageSnapshot() {
        return await this.snapshot();
    }

    /**
     * 点击元素 (兼容方法名)
     */
    async clickElement(element, ref) {
        return await this.click(element, ref);
    }

    /**
     * 输入文本 (兼容方法名)
     */
    async typeText(element, ref, text) {
        return await this.type(element, ref, text);
    }

    /**
     * 截图
     */
    async takeScreenshot(filename) {
        return await this.callTool('browser_take_screenshot', { filename });
    }

    /**
     * 等待
     */
    async wait(seconds) {
        return await this.callTool('browser_wait_for', { time: seconds });
    }

    /**
     * 确保连接健康
     */
    async ensureConnection() {
        if (!await this.healthCheck()) {
            this.logger.warn('🔄 连接不健康，尝试重连...');
            await this.reconnect();
        }
    }

    /**
     * 健康检查
     */
    async healthCheck() {
        try {
            // 发送一个简单的ping请求
            await this.sendRequest('ping', {}, 5000); // 5秒超时
            return true;
        } catch (error) {
            this.logger.warn('❌ 健康检查失败:', error.message);
            return false;
        }
    }

    /**
     * 重新连接
     */
    async reconnect() {
        try {
            this.logger.info('🔄 开始重新连接MCP服务器...');

            // 关闭现有连接
            if (this.eventSource) {
                this.eventSource.close();
            }

            // 清理状态
            this.isInitialized = false;
            this.pendingRequests.clear();

            // 重新初始化
            await this.initialize();

            this.logger.info('✅ MCP服务器重连成功');
        } catch (error) {
            this.logger.error('❌ MCP服务器重连失败:', error);
            throw error;
        }
    }

    /**
     * 判断是否为连接错误
     */
    isConnectionError(error) {
        const connectionErrors = [
            'ECONNREFUSED',
            'ENOTFOUND',
            'ETIMEDOUT',
            'ECONNRESET',
            'Connection closed',
            'Network Error'
        ];

        return connectionErrors.some(errorType =>
            error.message.includes(errorType) ||
            error.code === errorType
        );
    }

    /**
     * 关闭连接
     */
    async close() {
        if (this.eventSource) {
            this.eventSource.close();
            this.logger.info('🧹 SSE连接已关闭');
        }

        // 清理待处理的请求
        for (const [id, { reject }] of this.pendingRequests) {
            reject(new Error('连接已关闭'));
        }
        this.pendingRequests.clear();

        this.isInitialized = false;
        this.sessionId = null;
        this.postEndpoint = null;
        this.eventSource = null;
    }
}

module.exports = { MCPClientV2 };
