/**
 * 智能任务分析器 - 增强版单Agent系统核心组件
 * 专门解决任务理解和验证标准制定问题
 */

class TaskAnalyzer {
    constructor(modelClient, logger) {
        this.modelClient = modelClient;
        this.logger = logger;
        this.componentName = '智能任务分析器';
        this.systemGuide = null;
    }

    /**
     * 加载系统指南
     */
    async loadSystemGuide() {
        try {
            const fs = require('fs').promises;
            const path = require('path');
            const guidePath = path.join(__dirname, '../../../../system_guide.md');
            this.systemGuide = await fs.readFile(guidePath, 'utf-8');
            this.logger.info('✅ 任务分析器系统指南加载成功');
        } catch (error) {
            this.logger.error('❌ 任务分析器系统指南加载失败:', error);
            this.systemGuide = '';
        }
    }

    /**
     * 分析任务并提取关键信息
     */
    async analyzeTask(task) {
        try {
            this.logger.info(`🔍 ${this.componentName}开始分析任务...`);

            // 确保系统指南已加载
            if (!this.systemGuide) {
                await this.loadSystemGuide();
            }

            const prompt = `
你是专业的RPA任务分析专家。请仔细分析以下任务，提取关键信息并制定验证标准。

任务信息：
- 标题: ${task.title || task.description}
- 内容: ${task.originalContent || task.content || ''}
- 目标: ${task.objective || '完成指定的RPA任务'}

系统操作指南：
${this.systemGuide}

重要分析指导：
1. 对于商品下架/上架等业务任务，起始URL应该是 https://uat-merchant.aomiapp.com/#/bdlogin
2. 对于测试任务（如验证模型工作、回复指定信息），重点是验证AI理解能力，不需要复杂的业务操作
3. 如果任务要求"回复指定信息"，应该设置为验证类任务，主要检查AI的响应能力

请分析任务并返回JSON格式结果：
{
    "taskType": "商品下架|商品上架|信息查询|搜索操作|其他",
    "businessInfo": {
        "storeName": "目标门店名称（如：小泉居(威翠店)）",
        "productName": "目标商品名称（如：咖哩焗魚飯）",
        "operation": "具体操作（下架/上架/查询/搜索等）",
        "targetUrl": "目标网址（如果有）"
    },
    "verificationCriteria": {
        "primaryCheck": "主要验证标准（如：商品状态为已下架）",
        "secondaryChecks": [
            "次要验证标准1",
            "次要验证标准2"
        ],
        "evidenceRequired": [
            "需要的证据1（如：页面显示下架状态）",
            "需要的证据2（如：找到指定门店和商品）"
        ]
    },
    "executionPlan": {
        "startUrl": "具体的起始URL（必须是完整的https://开头的URL，如：https://uat-merchant.aomiapp.com/#/bdlogin）",
        "keySteps": [
            "步骤1：导航到具体的管理页面URL",
            "步骤2：搜索目标门店",
            "步骤3：找到目标商品",
            "步骤4：执行操作",
            "步骤5：验证结果"
        ],
        "expectedElements": [
            "预期会遇到的页面元素（如：搜索框、商品列表、状态按钮）"
        ]
    },
    "riskAssessment": {
        "potentialIssues": [
            "可能遇到的问题（如：需要登录、页面加载慢）"
        ],
        "fallbackStrategy": "备用策略"
    }
}

重要：请只返回纯JSON格式，不要包含markdown标记。`;

            // 任务分析暂时使用文本模型（因为初始阶段没有页面截图）
            const response = await this.modelClient.callMainModel(prompt);
            const cleanedResponse = this.cleanJsonResponse(response);
            const analysis = JSON.parse(cleanedResponse);
            
            this.logger.info(`✅ ${this.componentName}任务分析完成`);
            this.logger.info(`📊 任务类型: ${analysis.taskType}`);
            this.logger.info(`🏪 目标门店: ${analysis.businessInfo.storeName || '未指定'}`);
            this.logger.info(`📦 目标商品: ${analysis.businessInfo.productName || '未指定'}`);
            this.logger.info(`🎯 操作类型: ${analysis.businessInfo.operation}`);
            this.logger.info(`🔍 验证标准: ${analysis.verificationCriteria.primaryCheck}`);
            
            return analysis;
            
        } catch (error) {
            this.logger.error(`❌ ${this.componentName}任务分析失败:`, error);
            
            // 返回基础分析结果
            return this.generateFallbackAnalysis(task);
        }
    }

    /**
     * 根据页面状态判断下一步操作
     */
    async planNextAction(taskAnalysis, currentPageState, executionHistory) {
        try {
            this.logger.info(`🎯 ${this.componentName}规划下一步操作...`);
            
            const prompt = `
你是RPA执行规划专家。根据任务分析、当前页面状态和执行历史，规划下一步最佳操作。

重要页面状态理解：
- /bdlogin = 登录页面（如果已登录会自动跳转）
- /select = 登录后的选择页面（正常状态，不需要返回登录页面）
- 如果当前在 /select 页面，说明登录成功，应该继续后续操作

视觉分析指导：
- 请仔细观察页面截图中的实际内容
- 注意页面上的按钮、输入框、文本等元素
- 结合URL和截图内容做出准确判断
- 如果截图显示的内容与URL不符，以截图为准

任务分析：
${JSON.stringify(taskAnalysis, null, 2)}

当前页面状态：
- URL: ${currentPageState.url}
- 标题: ${currentPageState.title}
- 可用按钮: ${currentPageState.buttons?.map(b => b.text).join(', ') || '无'}
- 输入框: ${currentPageState.inputs?.map(i => i.placeholder || i.name || i.id).join(', ') || '无'}
- 重要链接: ${currentPageState.links?.slice(0, 5).map(l => l.text).join(', ') || '无'}

执行历史：
${executionHistory.map((step, i) => `${i+1}. ${step.action?.action_type || 'unknown'} - ${step.result?.success ? '成功' : '失败'}`).join('\n')}

特别注意：
1. 如果任务类型是"其他"且操作是"回复指定信息"，应该直接进行验证，不需要继续浏览器操作
2. 如果当前URL包含 /select，说明已经登录成功，不要再导航回登录页面
3. 对于测试任务，重点是验证AI理解能力，而不是执行具体的业务操作

请规划下一步操作（JSON格式）：
{
    "action_type": "navigate|click|fill|type|wait|screenshot|verify",
    "target_element": {
        "selector": "具体的CSS选择器或文本",
        "description": "元素描述"
    },
    "action_data": {
        "url": "导航URL（如果是navigate）",
        "text": "输入文本（如果是fill/type）",
        "timeout": "等待时间（如果是wait）"
    },
    "reasoning": "选择此操作的详细理由",
    "expected_outcome": "预期结果",
    "verification_method": "如何验证这一步是否成功",
    "business_relevance": "与业务目标的关联性"
}

重要：请只返回纯JSON格式。`;

            // 使用视觉分析+文本分析的双重保险
            let response;
            if (currentPageState.screenshot) {
                this.logger.info(`👁️ ${this.componentName}使用视觉分析+文本分析双重保险...`);
                // 优先使用视觉模型分析截图
                response = await this.modelClient.callVisionModel(prompt, currentPageState.screenshot);
            } else {
                this.logger.info(`📝 ${this.componentName}使用文本分析...`);
                // 备用：使用文本模型
                response = await this.modelClient.callMainModel(prompt);
            }

            const cleanedResponse = this.cleanJsonResponse(response);
            const nextAction = JSON.parse(cleanedResponse);
            
            this.logger.info(`✅ ${this.componentName}下一步规划完成`);
            this.logger.info(`🎬 计划操作: ${nextAction.action_type}`);
            this.logger.info(`🎯 操作目标: ${nextAction.target_element?.description || '无特定目标'}`);
            this.logger.info(`💭 操作理由: ${nextAction.reasoning}`);
            
            return nextAction;
            
        } catch (error) {
            this.logger.error(`❌ ${this.componentName}操作规划失败:`, error);
            
            // 返回基础操作
            return this.generateFallbackAction(currentPageState);
        }
    }

    /**
     * 验证任务完成度
     */
    async verifyTaskCompletion(taskAnalysis, currentPageState, executionHistory) {
        try {
            this.logger.info(`🔍 ${this.componentName}验证任务完成度...`);
            
            const prompt = `
你是RPA任务验证专家。请严格验证任务是否真正完成，不接受虚假成功。

任务分析：
${JSON.stringify(taskAnalysis, null, 2)}

当前页面状态：
- URL: ${currentPageState.url}
- 标题: ${currentPageState.title}
- 页面内容关键词: ${this.extractPageKeywords(currentPageState)}

执行历史：
${executionHistory.map((step, i) => `${i+1}. ${step.action?.action_type} - ${step.result?.message || '无消息'}`).join('\n')}

请严格验证并返回结果（JSON格式）：
{
    "isCompleted": true/false,
    "confidence": 0-100,
    "verificationResults": {
        "storeFound": {
            "found": true/false,
            "expectedStore": "预期门店名",
            "actualEvidence": "实际找到的证据",
            "confidence": 0-100
        },
        "productFound": {
            "found": true/false,
            "expectedProduct": "预期商品名",
            "actualEvidence": "实际找到的证据",
            "confidence": 0-100
        },
        "operationCompleted": {
            "completed": true/false,
            "expectedOperation": "预期操作",
            "actualEvidence": "实际操作证据",
            "confidence": 0-100
        }
    },
    "evidenceAnalysis": {
        "positiveEvidence": ["支持完成的证据"],
        "negativeEvidence": ["反对完成的证据"],
        "missingEvidence": ["缺失的关键证据"]
    },
    "finalAssessment": "最终评估结论（详细说明）",
    "recommendedActions": ["建议的后续操作"],
    "businessImpact": "对业务的实际影响"
}

重要：请严格验证，宁可保守也不要误判。只返回纯JSON格式。`;

            // 使用视觉分析+文本分析的双重保险进行验证
            let response;
            if (currentPageState.screenshot) {
                this.logger.info(`👁️ ${this.componentName}使用视觉验证+文本验证双重保险...`);
                // 优先使用视觉模型分析截图进行验证
                response = await this.modelClient.callVisionModel(prompt, currentPageState.screenshot);
            } else {
                this.logger.info(`📝 ${this.componentName}使用文本验证...`);
                // 备用：使用文本模型验证
                response = await this.modelClient.callMainModel(prompt);
            }

            const cleanedResponse = this.cleanJsonResponse(response);
            const verification = JSON.parse(cleanedResponse);
            
            this.logger.info(`✅ ${this.componentName}任务验证完成`);
            this.logger.info(`📊 完成状态: ${verification.isCompleted ? '✅ 已完成' : '❌ 未完成'}`);
            this.logger.info(`📈 置信度: ${verification.confidence}%`);
            this.logger.info(`🔍 最终评估: ${verification.finalAssessment}`);
            
            return verification;
            
        } catch (error) {
            this.logger.error(`❌ ${this.componentName}任务验证失败:`, error);
            
            return {
                isCompleted: false,
                confidence: 0,
                verificationResults: {
                    storeFound: { found: false, confidence: 0 },
                    productFound: { found: false, confidence: 0 },
                    operationCompleted: { completed: false, confidence: 0 }
                },
                evidenceAnalysis: {
                    positiveEvidence: [],
                    negativeEvidence: [`验证过程失败: ${error.message}`],
                    missingEvidence: ["所有验证数据"]
                },
                finalAssessment: `验证失败: ${error.message}`,
                recommendedActions: ["重新执行任务", "人工检查"],
                businessImpact: "无法确定业务影响"
            };
        }
    }

    /**
     * 提取页面关键词
     */
    extractPageKeywords(pageState) {
        const keywords = [];
        
        if (pageState.title) keywords.push(pageState.title);
        if (pageState.buttons) keywords.push(...pageState.buttons.map(b => b.text));
        if (pageState.links) keywords.push(...pageState.links.slice(0, 10).map(l => l.text));
        
        return keywords.filter(k => k && k.trim()).join(', ');
    }

    /**
     * 生成备用分析结果
     */
    generateFallbackAnalysis(task) {
        return {
            taskType: "其他",
            businessInfo: {
                storeName: "未识别",
                productName: "未识别", 
                operation: "基础操作",
                targetUrl: "https://www.baidu.com"
            },
            verificationCriteria: {
                primaryCheck: "完成基本操作流程",
                secondaryChecks: ["页面正常加载"],
                evidenceRequired: ["操作执行完成"]
            },
            executionPlan: {
                startUrl: "https://www.baidu.com",
                keySteps: ["导航到页面", "执行基本操作"],
                expectedElements: ["页面元素"]
            },
            riskAssessment: {
                potentialIssues: ["分析失败"],
                fallbackStrategy: "执行基础操作"
            }
        };
    }

    /**
     * 生成备用操作
     */
    generateFallbackAction(pageState) {
        return {
            action_type: "screenshot",
            target_element: {
                selector: "body",
                description: "页面主体"
            },
            action_data: {},
            reasoning: "规划失败，执行截图操作",
            expected_outcome: "获取页面截图",
            verification_method: "检查截图是否生成",
            business_relevance: "记录当前状态"
        };
    }

    /**
     * 清理JSON响应
     */
    cleanJsonResponse(response) {
        try {
            let cleaned = response.trim();
            cleaned = cleaned.replace(/^```(?:json)?\s*\n?/i, '');
            cleaned = cleaned.replace(/\n?\s*```\s*$/i, '');
            const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                cleaned = jsonMatch[0];
            }
            return cleaned.replace(/\n\s*\n/g, '\n').trim();
        } catch (error) {
            this.logger.warn('⚠️ 清理JSON响应失败，使用原始响应:', error);
            return response;
        }
    }
}

module.exports = TaskAnalyzer;
