/**
 * 增强版执行控制器 - 专注于真实验证的单Agent系统
 * 解决任务完成度验证问题，不接受虚假成功
 */

const TaskAnalyzer = require('./task-analyzer');
const AIExecutionController = require('../execution/ai-execution-controller');

class EnhancedExecutionController {
    constructor(logger) {
        this.logger = logger;
        this.controllerName = '增强版执行控制器';
        this.taskAnalyzer = null;
        this.baseController = null;
        this.isInitialized = false;
    }

    /**
     * 初始化增强版控制器
     */
    async initialize(modelClient) {
        try {
            this.logger.info(`🚀 ${this.controllerName}初始化中...`);
            
            // 初始化任务分析器
            this.taskAnalyzer = new TaskAnalyzer(modelClient, this.logger);
            
            // 初始化基础执行控制器
            this.baseController = new AIExecutionController(this.logger);
            await this.baseController.initialize(modelClient);
            
            this.isInitialized = true;
            this.logger.info(`✅ ${this.controllerName}初始化完成`);
            
        } catch (error) {
            this.logger.error(`❌ ${this.controllerName}初始化失败:`, error);
            throw error;
        }
    }

    /**
     * 执行增强版RPA任务
     */
    async executeEnhancedTask(task) {
        try {
            this.logger.info(`🎯 ${this.controllerName}开始执行增强版RPA任务...`);
            
            if (!this.isInitialized) {
                throw new Error('控制器未初始化');
            }
            
            const startTime = Date.now();
            
            // 阶段1: 智能任务分析
            this.logger.info(`📋 阶段1: 智能任务分析`);
            const taskAnalysis = await this.taskAnalyzer.analyzeTask(task);
            
            // 阶段2: 增强版执行
            this.logger.info(`🎬 阶段2: 增强版执行`);
            const executionResult = await this.executeWithVerification(task, taskAnalysis);
            
            // 阶段3: 严格验证
            this.logger.info(`🔍 阶段3: 严格任务验证`);
            const verificationResult = await this.performStrictVerification(
                taskAnalysis, executionResult
            );
            
            // 阶段4: 生成详细报告
            this.logger.info(`📝 阶段4: 生成详细报告`);
            const detailedReport = await this.generateDetailedReport(
                task, taskAnalysis, executionResult, verificationResult
            );
            
            const executionTime = Date.now() - startTime;
            
            const finalResult = {
                success: verificationResult.isCompleted,
                confidence: verificationResult.confidence,
                stepsCompleted: executionResult.stepsCompleted,
                taskAnalysis: taskAnalysis,
                executionResult: executionResult,
                verificationResult: verificationResult,
                report: detailedReport,
                executionTime: executionTime
            };
            
            this.logger.info(`✅ ${this.controllerName}任务执行完成`);
            this.logger.info(`📊 最终结果: ${finalResult.success ? '✅ 真实完成' : '❌ 未完成'}`);
            this.logger.info(`📈 验证置信度: ${finalResult.confidence}%`);
            this.logger.info(`⏱️ 执行时间: ${(executionTime/1000).toFixed(2)}秒`);
            
            return finalResult;
            
        } catch (error) {
            this.logger.error(`❌ ${this.controllerName}任务执行失败:`, error);
            
            return {
                success: false,
                confidence: 0,
                stepsCompleted: 0,
                error: error.message,
                report: {
                    summary: `增强版执行失败: ${error.message}`,
                    details: '系统异常，无法完成任务验证'
                }
            };
        }
    }

    /**
     * 带验证的执行过程
     */
    async executeWithVerification(task, taskAnalysis) {
        try {
            this.logger.info(`🎬 开始带验证的执行过程...`);
            
            const executionHistory = [];
            const maxSteps = 15;
            let currentStep = 0;
            
            // 执行初始导航
            const initialUrl = taskAnalysis.executionPlan.startUrl || 'https://www.baidu.com';
            const initialAction = {
                action_type: 'navigate',
                action_data: { url: initialUrl },
                reasoning: `根据任务分析，导航到起始页面: ${initialUrl}`,
                expected_outcome: '成功加载目标页面'
            };
            
            this.logger.info(`🌐 执行初始导航: ${initialUrl}`);
            const initialResult = await this.baseController.executeAction(initialAction);
            executionHistory.push({
                step: ++currentStep,
                action: initialAction,
                result: initialResult,
                timestamp: new Date().toISOString()
            });
            
            // 执行后续步骤
            while (currentStep < maxSteps) {
                // 获取当前页面状态
                const currentPageState = await this.baseController.perceivePage();
                
                // 检查是否需要人工干预
                if (await this.checkForIntervention(currentPageState)) {
                    this.logger.info(`⏸️ 检测到需要人工干预，暂停执行`);
                    break;
                }
                
                // 智能规划下一步
                const nextAction = await this.taskAnalyzer.planNextAction(
                    taskAnalysis, currentPageState, executionHistory
                );
                
                if (!nextAction || nextAction.action_type === 'verify') {
                    this.logger.info(`🏁 规划完成或需要验证，结束执行循环`);
                    break;
                }
                
                // 执行操作
                this.logger.info(`🎬 执行步骤 ${currentStep + 1}: ${nextAction.action_type}`);
                this.logger.info(`🎯 操作目标: ${nextAction.target_element?.description || '无'}`);
                
                const stepResult = await this.baseController.executeAction(nextAction);
                
                executionHistory.push({
                    step: ++currentStep,
                    action: nextAction,
                    result: stepResult,
                    timestamp: new Date().toISOString()
                });
                
                this.logger.info(`${stepResult.success ? '✅' : '❌'} 步骤 ${currentStep} ${stepResult.success ? '成功' : '失败'}: ${stepResult.message}`);
                
                // 如果连续失败，提前结束
                const recentFailures = executionHistory.slice(-3).filter(h => !h.result.success).length;
                if (recentFailures >= 3) {
                    this.logger.warn(`⚠️ 连续3步失败，提前结束执行`);
                    break;
                }
                
                // 短暂等待，避免操作过快
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            // 获取最终页面状态
            const finalPageState = await this.baseController.perceivePage();
            
            return {
                stepsCompleted: currentStep,
                executionHistory: executionHistory,
                finalPageState: finalPageState,
                success: executionHistory.some(h => h.result.success)
            };
            
        } catch (error) {
            this.logger.error(`❌ 带验证执行过程失败:`, error);
            throw error;
        }
    }

    /**
     * 执行严格验证
     */
    async performStrictVerification(taskAnalysis, executionResult) {
        try {
            this.logger.info(`🔍 开始严格任务验证...`);
            
            // 使用任务分析器进行严格验证
            const verification = await this.taskAnalyzer.verifyTaskCompletion(
                taskAnalysis,
                executionResult.finalPageState,
                executionResult.executionHistory
            );
            
            // 额外的业务逻辑验证
            const businessVerification = await this.performBusinessVerification(
                taskAnalysis, executionResult.finalPageState
            );
            
            // 合并验证结果
            const combinedVerification = {
                ...verification,
                businessVerification: businessVerification,
                overallConfidence: Math.min(verification.confidence, businessVerification.confidence)
            };
            
            this.logger.info(`✅ 严格验证完成`);
            this.logger.info(`📊 技术验证置信度: ${verification.confidence}%`);
            this.logger.info(`🏢 业务验证置信度: ${businessVerification.confidence}%`);
            this.logger.info(`📈 综合置信度: ${combinedVerification.overallConfidence}%`);
            
            return combinedVerification;
            
        } catch (error) {
            this.logger.error(`❌ 严格验证失败:`, error);
            throw error;
        }
    }

    /**
     * 业务逻辑验证
     */
    async performBusinessVerification(taskAnalysis, pageState) {
        try {
            this.logger.info(`🏢 执行业务逻辑验证...`);
            
            const businessInfo = taskAnalysis.businessInfo;
            let confidence = 0;
            const evidenceFound = [];
            const evidenceMissing = [];
            
            // 检查门店信息
            if (businessInfo.storeName && businessInfo.storeName !== '未识别') {
                const storeFound = this.checkForTextInPage(pageState, businessInfo.storeName);
                if (storeFound) {
                    confidence += 30;
                    evidenceFound.push(`找到目标门店: ${businessInfo.storeName}`);
                } else {
                    evidenceMissing.push(`未找到目标门店: ${businessInfo.storeName}`);
                }
            }
            
            // 检查商品信息
            if (businessInfo.productName && businessInfo.productName !== '未识别') {
                const productFound = this.checkForTextInPage(pageState, businessInfo.productName);
                if (productFound) {
                    confidence += 30;
                    evidenceFound.push(`找到目标商品: ${businessInfo.productName}`);
                } else {
                    evidenceMissing.push(`未找到目标商品: ${businessInfo.productName}`);
                }
            }
            
            // 检查操作相关的状态词
            const operationKeywords = ['下架', '上架', '已下架', '已上架', '禁用', '启用'];
            const operationFound = operationKeywords.some(keyword => 
                this.checkForTextInPage(pageState, keyword)
            );
            
            if (operationFound) {
                confidence += 40;
                evidenceFound.push('找到操作状态相关信息');
            } else {
                evidenceMissing.push('未找到操作状态信息');
            }
            
            return {
                confidence: confidence,
                evidenceFound: evidenceFound,
                evidenceMissing: evidenceMissing,
                businessLogicPassed: confidence >= 60
            };
            
        } catch (error) {
            this.logger.error(`❌ 业务逻辑验证失败:`, error);
            return {
                confidence: 0,
                evidenceFound: [],
                evidenceMissing: [`业务验证失败: ${error.message}`],
                businessLogicPassed: false
            };
        }
    }

    /**
     * 检查页面中是否包含指定文本
     */
    checkForTextInPage(pageState, searchText) {
        if (!searchText) return false;
        
        const searchLower = searchText.toLowerCase();
        
        // 检查标题
        if (pageState.title && pageState.title.toLowerCase().includes(searchLower)) {
            return true;
        }
        
        // 检查按钮文本
        if (pageState.buttons && pageState.buttons.some(btn => 
            btn.text && btn.text.toLowerCase().includes(searchLower))) {
            return true;
        }
        
        // 检查链接文本
        if (pageState.links && pageState.links.some(link => 
            link.text && link.text.toLowerCase().includes(searchLower))) {
            return true;
        }
        
        return false;
    }

    /**
     * 检查是否需要人工干预
     */
    async checkForIntervention(pageState) {
        // 检查登录页面
        if (pageState.title && pageState.title.includes('登录')) {
            return true;
        }
        
        // 检查验证码
        if (pageState.inputs && pageState.inputs.some(input => 
            input.placeholder && input.placeholder.includes('验证码'))) {
            return true;
        }
        
        return false;
    }

    /**
     * 生成详细报告
     */
    async generateDetailedReport(task, taskAnalysis, executionResult, verificationResult) {
        try {
            const report = {
                summary: this.generateReportSummary(verificationResult),
                taskAnalysis: {
                    taskType: taskAnalysis.taskType,
                    targetStore: taskAnalysis.businessInfo.storeName,
                    targetProduct: taskAnalysis.businessInfo.productName,
                    operation: taskAnalysis.businessInfo.operation
                },
                executionDetails: {
                    stepsCompleted: executionResult.stepsCompleted,
                    finalUrl: executionResult.finalPageState?.url || '未知',
                    finalTitle: executionResult.finalPageState?.title || '未知'
                },
                verificationResults: {
                    isCompleted: verificationResult.isCompleted,
                    confidence: verificationResult.confidence,
                    evidenceFound: verificationResult.evidenceAnalysis?.positiveEvidence || [],
                    evidenceMissing: verificationResult.evidenceAnalysis?.missingEvidence || []
                },
                businessImpact: verificationResult.businessImpact || '无法确定',
                timestamp: new Date().toLocaleString('zh-CN')
            };
            
            return report;
            
        } catch (error) {
            this.logger.error(`❌ 报告生成失败:`, error);
            return {
                summary: `报告生成失败: ${error.message}`,
                timestamp: new Date().toLocaleString('zh-CN')
            };
        }
    }

    /**
     * 生成报告摘要
     */
    generateReportSummary(verificationResult) {
        if (verificationResult.isCompleted && verificationResult.confidence >= 80) {
            return `✅ 任务真实完成，验证置信度: ${verificationResult.confidence}%`;
        } else if (verificationResult.confidence >= 50) {
            return `⚠️ 任务部分完成，验证置信度: ${verificationResult.confidence}%，需要人工确认`;
        } else {
            return `❌ 任务未完成，验证置信度: ${verificationResult.confidence}%`;
        }
    }
}

module.exports = EnhancedExecutionController;
