/**
 * 浏览器推送通知服务
 * 用于在需要用户干预时发送通知
 */

const axios = require('axios');

class NotificationService {
    constructor(logger = console) {
        this.logger = logger;
        this.workorderApiUrl = process.env.WORKORDER_API_URL || 'http://localhost:3001';
    }

    /**
     * 发送用户干预通知
     * @param {Object} options - 通知选项
     * @param {string} options.ticketId - 工单ID
     * @param {string} options.title - 通知标题
     * @param {string} options.message - 通知消息
     * @param {string} options.type - 通知类型 (login_required, user_action_needed, etc.)
     * @param {string} options.url - 相关URL
     */
    async sendUserInterventionNotification(options) {
        try {
            const {
                ticketId,
                title = '需要用户操作',
                message,
                type = 'user_action_needed',
                url
            } = options;

            this.logger.info(`📢 发送用户干预通知: ${title}`);

            // 1. 更新工单状态为"等待用户操作"
            await this.updateTicketStatus(ticketId, '等待用户操作', {
                message: message,
                type: type,
                url: url,
                timestamp: new Date().toISOString()
            });

            // 2. 发送WebSocket通知到前端
            await this.sendWebSocketNotification({
                type: 'user_intervention_required',
                ticketId: ticketId,
                title: title,
                message: message,
                actionType: type,
                url: url,
                timestamp: new Date().toISOString()
            });

            // 3. 发送浏览器推送通知
            await this.sendBrowserPushNotification({
                title: title,
                body: message,
                icon: '/icons/notification-icon.png',
                badge: '/icons/badge-icon.png',
                tag: `ticket-${ticketId}`,
                data: {
                    ticketId: ticketId,
                    type: type,
                    url: url
                },
                actions: [
                    {
                        action: 'view',
                        title: '查看工单',
                        icon: '/icons/view-icon.png'
                    },
                    {
                        action: 'dismiss',
                        title: '稍后处理',
                        icon: '/icons/dismiss-icon.png'
                    }
                ]
            });

            this.logger.info(`✅ 用户干预通知发送成功: ${ticketId}`);
            return true;

        } catch (error) {
            this.logger.error(`❌ 发送用户干预通知失败:`, error.message);
            return false;
        }
    }

    /**
     * 发送登录需求通知
     */
    async sendLoginRequiredNotification(ticketId, url) {
        return await this.sendUserInterventionNotification({
            ticketId: ticketId,
            title: '需要手动登录',
            message: '商户后台需要您手动登录后，AI助手才能继续执行任务',
            type: 'login_required',
            url: url
        });
    }

    /**
     * 发送任务暂停通知
     */
    async sendTaskPausedNotification(ticketId, reason) {
        return await this.sendUserInterventionNotification({
            ticketId: ticketId,
            title: '任务已暂停',
            message: `任务暂停原因: ${reason}`,
            type: 'task_paused'
        });
    }

    /**
     * 发送任务失败通知
     */
    async sendTaskFailedNotification(ticketId, error) {
        return await this.sendUserInterventionNotification({
            ticketId: ticketId,
            title: '任务执行失败',
            message: `任务失败: ${error}`,
            type: 'task_failed'
        });
    }

    /**
     * 更新工单状态
     */
    async updateTicketStatus(ticketId, status, details = null) {
        try {
            const updateData = { status };
            if (details) {
                updateData.notes = JSON.stringify(details);
            }

            await axios.patch(`${this.workorderApiUrl}/api/tickets/${ticketId}/status`, updateData);
            this.logger.info(`✅ 工单状态已更新: ${ticketId} -> ${status}`);
        } catch (error) {
            this.logger.error(`❌ 更新工单状态失败:`, error.message);
        }
    }

    /**
     * 发送WebSocket通知到前端
     */
    async sendWebSocketNotification(notification) {
        try {
            await axios.post(`${this.workorderApiUrl}/api/notifications`, notification);
            this.logger.info(`📡 WebSocket通知已发送: ${notification.type}`);
        } catch (error) {
            this.logger.error(`❌ WebSocket通知发送失败:`, error.message);
        }
    }

    /**
     * 发送浏览器推送通知
     */
    async sendBrowserPushNotification(notification) {
        try {
            // 发送到推送通知服务
            await axios.post(`${this.workorderApiUrl}/api/push-notifications`, notification);
            this.logger.info(`🔔 浏览器推送通知已发送: ${notification.title}`);
        } catch (error) {
            this.logger.error(`❌ 浏览器推送通知发送失败:`, error.message);
        }
    }

    /**
     * 检查用户是否已经处理了干预请求
     */
    async checkUserInterventionStatus(ticketId) {
        try {
            const response = await axios.get(`${this.workorderApiUrl}/api/tickets/${ticketId}`);
            const ticket = response.data.data || response.data;
            
            // 如果状态不再是"等待用户操作"，说明用户已经处理了
            return ticket.status !== '等待用户操作';
        } catch (error) {
            this.logger.error(`❌ 检查用户干预状态失败:`, error.message);
            return false;
        }
    }

    /**
     * 等待用户干预完成
     */
    async waitForUserIntervention(ticketId, maxWaitTime = 300000) { // 默认等待5分钟
        const startTime = Date.now();
        const checkInterval = 5000; // 每5秒检查一次

        this.logger.info(`⏳ 等待用户干预完成: ${ticketId}`);

        return new Promise((resolve) => {
            const checkStatus = async () => {
                const elapsed = Date.now() - startTime;
                
                if (elapsed >= maxWaitTime) {
                    this.logger.warn(`⏰ 用户干预等待超时: ${ticketId}`);
                    resolve(false);
                    return;
                }

                const isHandled = await this.checkUserInterventionStatus(ticketId);
                if (isHandled) {
                    this.logger.info(`✅ 用户干预已完成: ${ticketId}`);
                    resolve(true);
                    return;
                }

                // 继续等待
                setTimeout(checkStatus, checkInterval);
            };

            checkStatus();
        });
    }
}

module.exports = { NotificationService };
