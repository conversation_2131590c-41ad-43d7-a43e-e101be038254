const axios = require('axios');
const logger = require('../utils/logger');

/**
 * 验证特征自动抽取器
 * 从system_guide.md动态提取验证模式
 */
class ValidationFeatureExtractor {
    constructor(config) {
        this.baseUrl = config.qwen.baseUrl;
        this.apiKey = config.qwen.apiKey;
        this.model = config.qwen.model;
        this.systemGuideCache = new Map();
    }

    /**
     * 从system_guide.md动态提取验证模式
     */
    async extractValidationPatterns(taskType, systemGuide, taskContent) {
        try {
            const cacheKey = `${taskType}_${this.hashContent(taskContent)}`;
            if (this.systemGuideCache.has(cacheKey)) {
                return this.systemGuideCache.get(cacheKey);
            }

            const prompt = `
从以下系统指南中提取"${taskContent}"任务的验证特征：

系统指南内容：
${systemGuide}

任务类型：${taskType}
具体任务：${taskContent}

请分析系统指南，返回JSON格式的验证模式：
{
    "urlPatterns": [
        {
            "description": "URL变化描述",
            "pattern": "期望的URL模式或路径",
            "stage": "任务阶段(login/navigation/operation/completion)"
        }
    ],
    "requiredElements": [
        {
            "description": "元素描述",
            "selector": "元素选择器或描述",
            "stage": "出现阶段",
            "required": true/false
        }
    ],
    "stateTransitions": [
        {
            "from": "起始状态",
            "to": "目标状态", 
            "trigger": "触发条件",
            "verification": "验证方法"
        }
    ],
    "completionSignals": [
        {
            "type": "信号类型(text/element/url/status)",
            "description": "完成信号描述",
            "pattern": "匹配模式",
            "confidence": 0.0-1.0
        }
    ],
    "errorPatterns": [
        {
            "description": "错误描述",
            "pattern": "错误模式",
            "severity": "high/medium/low",
            "recovery": "恢复建议"
        }
    ],
    "businessLogic": {
        "objective": "业务目标",
        "keySteps": ["关键步骤1", "关键步骤2"],
        "successCriteria": ["成功标准1", "成功标准2"],
        "dependencies": ["依赖条件1", "依赖条件2"]
    }
}

重点分析：
1. 从系统指南中识别该任务的具体操作流程
2. 提取每个步骤的验证点
3. 识别成功完成的明确标志
4. 分析可能的失败模式和错误状态
5. 确定需要用户干预的情况
`;

            const response = await axios.post(`${this.baseUrl}/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的RPA验证模式分析专家。请仔细分析系统指南，提取准确的验证特征。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 2000
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            const extractionResult = response.data.choices[0].message.content;
            const patterns = this.parseValidationPatterns(extractionResult);
            
            // 缓存结果
            this.systemGuideCache.set(cacheKey, patterns);
            
            logger.info('🔍 验证模式提取完成:', {
                taskType,
                patternsCount: {
                    urlPatterns: patterns.urlPatterns?.length || 0,
                    requiredElements: patterns.requiredElements?.length || 0,
                    completionSignals: patterns.completionSignals?.length || 0
                }
            });

            return patterns;

        } catch (error) {
            logger.error('❌ 验证模式提取失败:', error.message);
            return this.getDefaultPatterns(taskType);
        }
    }

    /**
     * 解析验证模式结果
     */
    parseValidationPatterns(extractionResult) {
        try {
            // 尝试提取JSON
            const jsonMatch = extractionResult.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            // 如果没有找到JSON，返回默认模式
            logger.warn('⚠️ 无法解析验证模式，使用默认模式');
            return this.getDefaultPatterns('unknown');
            
        } catch (error) {
            logger.error('❌ 验证模式解析失败:', error.message);
            return this.getDefaultPatterns('unknown');
        }
    }

    /**
     * 获取默认验证模式
     */
    getDefaultPatterns(taskType) {
        const defaultPatterns = {
            web_automation: {
                urlPatterns: [
                    {
                        description: "页面导航变化",
                        pattern: "URL路径变化",
                        stage: "navigation"
                    }
                ],
                requiredElements: [
                    {
                        description: "目标页面元素",
                        selector: "关键元素",
                        stage: "completion",
                        required: true
                    }
                ],
                stateTransitions: [
                    {
                        from: "初始状态",
                        to: "完成状态",
                        trigger: "操作执行",
                        verification: "状态检查"
                    }
                ],
                completionSignals: [
                    {
                        type: "status",
                        description: "任务完成确认",
                        pattern: "成功状态",
                        confidence: 0.8
                    }
                ],
                errorPatterns: [
                    {
                        description: "通用错误",
                        pattern: "错误信息",
                        severity: "medium",
                        recovery: "重试操作"
                    }
                ],
                businessLogic: {
                    objective: "完成指定操作",
                    keySteps: ["导航", "操作", "确认"],
                    successCriteria: ["操作成功"],
                    dependencies: ["页面可访问"]
                }
            }
        };

        return defaultPatterns[taskType] || defaultPatterns.web_automation;
    }

    /**
     * 内容哈希（简单实现）
     */
    hashContent(content) {
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString();
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.systemGuideCache.clear();
        logger.info('🧹 验证模式缓存已清理');
    }
}

module.exports = ValidationFeatureExtractor;
