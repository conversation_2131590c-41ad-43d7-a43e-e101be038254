/**
 * 动态提示词生成器
 * 根据任务分析结果动态生成适合的提示词，而不是硬编码
 */

const logger = require('../utils/logger');

class DynamicPromptGenerator {
    constructor() {
        this.templateLibrary = new Map();
        this.initializeTemplates();
    }

    /**
     * 初始化提示词模板库
     */
    initializeTemplates() {
        // Web自动化任务模板
        this.templateLibrary.set('web_automation', {
            systemPrompt: `你是一个专业的Web自动化专家。你需要：
1. 分析网页结构和内容
2. 执行精确的浏览器操作
3. 验证操作结果
4. 处理异常情况
5. 智能处理登录和用户交互需求

重要规则：
- 如果页面快照为空（内容很少或只有空的yaml块），等待3-5秒后重新获取快照
- 如果重试后仍为空白，按以下顺序尝试备选URL：
  1. https://uat-merchant.aomiapp.com/#/select?noDirect=1
  2. https://uat-merchant.aomiapp.com/#/select
- 如果所有URL都显示空白页面，通知用户需要手动登录或报告给工单系统更新可用URL
- 当遇到需要用户输入的情况时，明确说明需要什么帮助
- 检测到URL重定向时，分析重定向的原因和目标页面

页面检测策略：
- 空白页面：快照内容少于50字符或只包含空的yaml块
- 登录页面：URL包含/bdlogin且有实际内容
- 已登录页面：URL包含/select，表示登录成功
- 重定向问题：从/bdlogin重定向到/#/且页面为空，应尝试备选URL

智能重试流程：
1. 访问主URL → 如果空白，等待5秒重试
2. 仍空白 → 尝试备选URL1
3. 仍空白 → 尝试备选URL2
4. 都失败 → 请求用户干预`,
            
            taskPrompt: (analysis, ticket) => `
# Web自动化任务

## 任务目标
${ticket.content}

## 任务分析
- 任务类型: ${analysis.taskType}
- 复杂度: ${analysis.complexityLevel}
- 预估步骤: ${analysis.estimatedSteps}
- 关键实体: ${analysis.keyEntities.join(', ')}
- 主要动作: ${analysis.actionVerbs.join(', ')}

## 执行策略
${this.generateExecutionStrategy(analysis)}

## 完成标准
${analysis.completionCriteria.map(criteria => `- ${criteria}`).join('\n')}

## 工具使用指南
${this.generateToolGuide(analysis)}

## 重要提醒
- 这是一个${analysis.complexityLevel}复杂度的任务
- 预计需要${analysis.estimatedSteps}个步骤
- 每步操作后都要验证结果
- 如遇问题，详细描述当前状态
${analysis.requiresLogin ? '- 可能需要处理登录流程' : ''}
${analysis.requiresUserInput ? '- 可能需要用户输入' : ''}

请开始执行任务。`
        });

        // 数据处理任务模板
        this.templateLibrary.set('data_entry', {
            systemPrompt: `你是一个数据处理专家。你需要：
1. 准确理解数据结构
2. 执行数据输入和验证
3. 确保数据完整性
4. 处理数据异常`,
            
            taskPrompt: (analysis, ticket) => `
# 数据处理任务

## 任务目标
${ticket.content}

## 数据处理策略
${this.generateDataStrategy(analysis)}

## 质量控制
- 数据验证规则
- 错误处理机制
- 完整性检查

请开始数据处理。`
        });

        // 通用任务模板
        this.templateLibrary.set('general', {
            systemPrompt: `你是一个通用RPA自动化专家。你需要：
1. 理解任务需求
2. 制定执行计划
3. 逐步执行操作
4. 验证执行结果`,
            
            taskPrompt: (analysis, ticket) => `
# RPA自动化任务

## 任务目标
${ticket.content}

## 执行要求
- 任务类型: ${analysis.taskType}
- 复杂度: ${analysis.complexityLevel}
- 预估步骤: ${analysis.estimatedSteps}

## 完成标准
${analysis.completionCriteria.map(criteria => `- ${criteria}`).join('\n')}

请分析任务并开始执行。`
        });
    }

    /**
     * 根据任务分析生成动态提示词
     * @param {Object} analysis - 任务分析结果
     * @param {Object} ticket - 工单信息
     * @param {Object} context - 上下文信息
     * @returns {Object} 生成的提示词
     */
    generatePrompt(analysis, ticket, context = {}) {
        try {
            const template = this.getTemplate(analysis.taskType);
            
            const prompt = {
                systemPrompt: template.systemPrompt,
                taskPrompt: template.taskPrompt(analysis, ticket),
                tools: this.generateToolList(analysis),
                maxIterations: this.calculateMaxIterations(analysis),
                timeout: analysis.suggestedTimeout || 300
            };

            logger.info('📝 动态提示词生成完成:', {
                taskType: analysis.taskType,
                complexity: analysis.complexityLevel,
                toolCount: prompt.tools.length
            });

            return prompt;

        } catch (error) {
            logger.error('❌ 提示词生成失败:', error.message);
            return this.getFallbackPrompt(ticket);
        }
    }

    /**
     * 获取任务模板
     */
    getTemplate(taskType) {
        return this.templateLibrary.get(taskType) || this.templateLibrary.get('general');
    }

    /**
     * 生成执行策略
     */
    generateExecutionStrategy(analysis) {
        const strategies = [];

        if (analysis.targetWebsite) {
            strategies.push(`1. 导航到目标网站: ${analysis.targetWebsite}`);
        }

        if (analysis.requiresLogin) {
            strategies.push('2. 智能页面检测和重试：');
            strategies.push('   - 获取页面快照，检查是否为空白页面');
            strategies.push('   - 如果空白，等待5秒后重新获取快照');
            strategies.push('   - 如果仍空白，尝试备选URL: https://uat-merchant.aomiapp.com/#/select?noDirect=1');
            strategies.push('   - 如果还是空白，尝试: https://uat-merchant.aomiapp.com/#/select');
            strategies.push('   - 如果所有URL都空白，请求用户登录或报告URL问题');
            strategies.push('3. 分析页面状态（登录页面/已登录页面/错误页面）');
            strategies.push('4. 如需登录，请求用户帮助');
        }

        strategies.push('5. 分析页面结构和内容');
        strategies.push('6. 执行目标操作');
        strategies.push('7. 验证操作结果');

        if (analysis.actionVerbs.length > 0) {
            strategies.push(`8. 重点关注: ${analysis.actionVerbs.join('、')}操作`);
        }

        return strategies.join('\n');
    }

    /**
     * 生成工具使用指南
     */
    generateToolGuide(analysis) {
        const guides = [
            '- 使用 browser_navigate_Playwright 导航到目标页面',
            '- 使用 browser_snapshot_Playwright 获取页面状态',
            '- 使用 browser_click_Playwright 点击元素',
            '- 使用 browser_type_Playwright 输入文本'
        ];

        if (analysis.taskType === 'web_automation') {
            guides.push('- 使用页面快照中的ref属性精确定位元素');
            guides.push('- 每次操作后获取页面快照验证结果');
        }

        return guides.join('\n');
    }

    /**
     * 生成数据处理策略
     */
    generateDataStrategy(analysis) {
        return `
1. 分析数据结构和格式
2. 验证数据完整性
3. 执行数据处理操作
4. 检查处理结果
5. 生成处理报告`;
    }

    /**
     * 生成工具列表
     */
    generateToolList(analysis) {
        const baseTools = [
            {
                type: 'function',
                function: {
                    name: 'browser_navigate_Playwright',
                    description: '导航到指定URL',
                    parameters: {
                        type: 'object',
                        properties: {
                            url: { type: 'string', description: '目标网址' }
                        },
                        required: ['url']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_snapshot_Playwright',
                    description: '获取页面快照(结构化文本，包含所有可交互元素的ref属性)',
                    parameters: {
                        type: 'object',
                        properties: {},
                        required: []
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_click_Playwright',
                    description: '点击页面元素',
                    parameters: {
                        type: 'object',
                        properties: {
                            element: { type: 'string', description: '元素描述' },
                            ref: { type: 'string', description: '元素的ref属性，如e14, e685等' }
                        },
                        required: ['element', 'ref']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'browser_type_Playwright',
                    description: '在输入框中输入文本',
                    parameters: {
                        type: 'object',
                        properties: {
                            element: { type: 'string', description: '输入框描述' },
                            ref: { type: 'string', description: '输入框的ref属性' },
                            text: { type: 'string', description: '要输入的文本' }
                        },
                        required: ['element', 'ref', 'text']
                    }
                }
            }
        ];

        // 根据任务类型添加特定工具
        if (analysis.taskType === 'web_automation') {
            baseTools.push({
                type: 'function',
                function: {
                    name: 'browser_take_screenshot_Playwright',
                    description: '截图保存',
                    parameters: {
                        type: 'object',
                        properties: {
                            filename: { type: 'string', description: '文件名(可选)' }
                        },
                        required: []
                    }
                }
            });
        }

        if (analysis.complexityLevel === 'high') {
            baseTools.push({
                type: 'function',
                function: {
                    name: 'browser_wait_for',
                    description: '等待指定时间',
                    parameters: {
                        type: 'object',
                        properties: {
                            time: { type: 'number', description: '等待秒数' }
                        },
                        required: ['time']
                    }
                }
            });
        }

        return baseTools;
    }

    /**
     * 计算最大迭代次数
     */
    calculateMaxIterations(analysis) {
        const baseIterations = 10;
        const complexityMultiplier = {
            'low': 1,
            'medium': 1.5,
            'high': 2
        };

        const multiplier = complexityMultiplier[analysis.complexityLevel] || 1;
        return Math.ceil(baseIterations * multiplier);
    }

    /**
     * 获取回退提示词
     */
    getFallbackPrompt(ticket) {
        return {
            systemPrompt: '你是一个RPA自动化专家，请分析并执行用户的任务。',
            taskPrompt: `请执行以下任务：${ticket.content}`,
            tools: this.generateToolList({ taskType: 'general', complexityLevel: 'medium' }),
            maxIterations: 15,
            timeout: 300
        };
    }

    /**
     * 生成继续执行的提示词
     */
    generateContinuePrompt(analysis, currentState) {
        return `
基于当前状态继续执行任务：

当前状态：${currentState}

请继续执行下一步操作，直到完成任务目标。
记住这是一个${analysis.complexityLevel}复杂度的任务，需要${analysis.estimatedSteps}个步骤。
`;
    }
}

module.exports = { DynamicPromptGenerator };
