/**
 * 全局执行状态管理器
 * 负责跟踪和控制整体执行流程，防止超时和无限等待
 */

const logger = require('../utils/logger');
const EventEmitter = require('events');

class ExecutionManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置选项
        this.config = {
            operationTimeout: options.operationTimeout || 30000,      // 单操作超时：30秒
            taskTimeout: options.taskTimeout || 300000,              // 任务总超时：5分钟
            maxRetries: options.maxRetries || 3,                     // 最大重试次数
            retryDelay: options.retryDelay || 2000,                  // 重试延迟
            enableTimeout: options.enableTimeout !== false            // 是否启用超时控制
        };
        
        // 执行状态
        this.executionState = {
            isExecuting: false,
            currentOperation: null,
            currentAgent: null,
            startTime: null,
            operations: [],
            timeouts: new Map(),
            abortController: null
        };
        
        // 性能统计
        this.stats = {
            totalOperations: 0,
            completedOperations: 0,
            timedOutOperations: 0,
            failedOperations: 0,
            averageOperationTime: 0,
            operationTimes: []
        };
        
        // 操作历史（用于模式识别）
        this.operationHistory = [];
        this.maxHistorySize = 100;
    }

    /**
     * 开始执行任务
     */
    startExecution(taskId) {
        if (this.executionState.isExecuting) {
            throw new Error('已有任务在执行中');
        }
        
        logger.info(`🚀 开始执行任务: ${taskId}`);
        
        this.executionState = {
            isExecuting: true,
            taskId: taskId,
            currentOperation: null,
            currentAgent: null,
            startTime: Date.now(),
            operations: [],
            timeouts: new Map(),
            abortController: new AbortController()
        };
        
        // 设置任务总超时
        if (this.config.enableTimeout) {
            this.setTaskTimeout();
        }
        
        this.emit('execution:started', { taskId, startTime: this.executionState.startTime });
        
        return this.executionState.abortController.signal;
    }

    /**
     * 执行操作（带超时控制）
     */
    async executeOperation(operationName, agentName, operationFn, options = {}) {
        const operationId = `${agentName}_${operationName}_${Date.now()}`;
        const timeout = options.timeout || this.config.operationTimeout;
        const currentRetries = options._retryCount || 0;
        
        // 检查是否已中止
        if (this.executionState.abortController?.signal.aborted) {
            throw new Error('任务已中止');
        }
        
        const operation = {
            id: operationId,
            name: operationName,
            agent: agentName,
            startTime: Date.now(),
            status: 'running',
            retries: currentRetries
        };
        
        this.executionState.currentOperation = operation;
        this.executionState.currentAgent = agentName;
        this.executionState.operations.push(operation);
        
        logger.info(`⚡ 开始操作: ${operationName} (Agent: ${agentName})`);
        this.emit('operation:started', operation);
        
        try {
            // 执行操作（带超时）
            const result = await this.executeWithTimeout(
                operationFn,
                timeout,
                operationId
            );
            
            // 记录成功
            operation.endTime = Date.now();
            operation.duration = operation.endTime - operation.startTime;
            operation.status = 'completed';
            operation.result = result;
            
            this.updateStats('completed', operation.duration);
            this.recordOperation(operation);
            
            logger.info(`✅ 操作完成: ${operationName} (耗时: ${operation.duration}ms)`);
            this.emit('operation:completed', operation);
            
            return result;
            
        } catch (error) {
            // 处理错误
            operation.endTime = Date.now();
            operation.duration = operation.endTime - operation.startTime;
            operation.error = error.message;
            
            if (error.name === 'TimeoutError') {
                operation.status = 'timeout';
                this.updateStats('timeout', operation.duration);
                logger.error(`⏱️ 操作超时: ${operationName} (${timeout}ms)`);
                this.emit('operation:timeout', operation);
            } else {
                operation.status = 'failed';
                this.updateStats('failed', operation.duration);
                logger.error(`❌ 操作失败: ${operationName}`, error);
                this.emit('operation:failed', operation);
            }
            
            this.recordOperation(operation);
            
            // 决定是否重试
            if (operation.retries < this.config.maxRetries && options.allowRetry !== false) {
                operation.retries++;
                logger.info(`🔄 重试操作 ${operationName} (第${operation.retries}次)`);
                
                await this.delay(this.config.retryDelay * operation.retries);
                
                return this.executeOperation(
                    operationName,
                    agentName,
                    operationFn,
                    { ...options, _retryCount: operation.retries }
                );
            }
            
            throw error;
        } finally {
            this.executionState.currentOperation = null;
            this.executionState.currentAgent = null;
        }
    }

    /**
     * 执行带超时的操作
     */
    async executeWithTimeout(fn, timeout, operationId) {
        if (!this.config.enableTimeout) {
            return await fn();
        }
        
        return new Promise(async (resolve, reject) => {
            // 设置超时计时器
            const timeoutId = setTimeout(() => {
                this.executionState.timeouts.delete(operationId);
                const error = new Error(`操作超时 (${timeout}ms)`);
                error.name = 'TimeoutError';
                reject(error);
            }, timeout);
            
            this.executionState.timeouts.set(operationId, timeoutId);
            
            try {
                const result = await fn();
                clearTimeout(timeoutId);
                this.executionState.timeouts.delete(operationId);
                resolve(result);
            } catch (error) {
                clearTimeout(timeoutId);
                this.executionState.timeouts.delete(operationId);
                reject(error);
            }
        });
    }

    /**
     * 设置任务总超时
     */
    setTaskTimeout() {
        const taskTimeoutId = setTimeout(() => {
            logger.error(`⏰ 任务执行超时 (${this.config.taskTimeout}ms)`);
            this.abortExecution('TASK_TIMEOUT');
        }, this.config.taskTimeout);
        
        this.executionState.timeouts.set('_task_timeout', taskTimeoutId);
    }

    /**
     * 中止执行
     */
    abortExecution(reason = 'USER_ABORT') {
        if (!this.executionState.isExecuting) {
            return;
        }
        
        logger.warn(`🛑 中止任务执行: ${reason}`);
        
        // 触发中止信号
        if (this.executionState.abortController) {
            this.executionState.abortController.abort();
        }
        
        // 清理所有超时计时器
        for (const [id, timeoutId] of this.executionState.timeouts) {
            clearTimeout(timeoutId);
        }
        this.executionState.timeouts.clear();
        
        // 更新状态
        this.executionState.isExecuting = false;
        this.executionState.abortReason = reason;
        
        this.emit('execution:aborted', {
            taskId: this.executionState.taskId,
            reason: reason,
            duration: Date.now() - this.executionState.startTime
        });
    }

    /**
     * 完成执行
     */
    endExecution(result = {}) {
        if (!this.executionState.isExecuting) {
            return;
        }
        
        const duration = Date.now() - this.executionState.startTime;
        
        logger.info(`🏁 任务执行完成 (总耗时: ${duration}ms)`);
        
        // 清理超时计时器
        for (const [id, timeoutId] of this.executionState.timeouts) {
            clearTimeout(timeoutId);
        }
        this.executionState.timeouts.clear();
        
        // 生成执行摘要
        const summary = {
            taskId: this.executionState.taskId,
            success: result.success || false,
            duration: duration,
            totalOperations: this.executionState.operations.length,
            completedOperations: this.executionState.operations.filter(op => op.status === 'completed').length,
            failedOperations: this.executionState.operations.filter(op => op.status === 'failed').length,
            timedOutOperations: this.executionState.operations.filter(op => op.status === 'timeout').length,
            operations: this.executionState.operations,
            result: result
        };
        
        this.executionState.isExecuting = false;
        
        this.emit('execution:completed', summary);
        
        return summary;
    }

    /**
     * 检查操作是否重复失败
     */
    isRepeatedFailure(operationName, agentName, threshold = 3) {
        const recentOps = this.operationHistory.slice(-10);
        const failures = recentOps.filter(op => 
            op.name === operationName && 
            op.agent === agentName && 
            op.status === 'failed'
        );
        
        return failures.length >= threshold;
    }

    /**
     * 获取操作成功率
     */
    getOperationSuccessRate(operationName, agentName) {
        const relevantOps = this.operationHistory.filter(op => 
            op.name === operationName && 
            op.agent === agentName
        );
        
        if (relevantOps.length === 0) return 1; // 默认100%成功率
        
        const successCount = relevantOps.filter(op => op.status === 'completed').length;
        return successCount / relevantOps.length;
    }

    /**
     * 记录操作到历史
     */
    recordOperation(operation) {
        this.operationHistory.push({
            name: operation.name,
            agent: operation.agent,
            status: operation.status,
            duration: operation.duration,
            timestamp: operation.startTime
        });
        
        // 限制历史大小
        if (this.operationHistory.length > this.maxHistorySize) {
            this.operationHistory.shift();
        }
    }

    /**
     * 更新统计信息
     */
    updateStats(status, duration) {
        this.stats.totalOperations++;
        
        switch (status) {
            case 'completed':
                this.stats.completedOperations++;
                break;
            case 'timeout':
                this.stats.timedOutOperations++;
                break;
            case 'failed':
                this.stats.failedOperations++;
                break;
        }
        
        // 更新平均操作时间
        this.stats.operationTimes.push(duration);
        if (this.stats.operationTimes.length > 100) {
            this.stats.operationTimes.shift();
        }
        
        const sum = this.stats.operationTimes.reduce((a, b) => a + b, 0);
        this.stats.averageOperationTime = sum / this.stats.operationTimes.length;
    }

    /**
     * 获取当前执行状态
     */
    getExecutionState() {
        return {
            isExecuting: this.executionState.isExecuting,
            currentAgent: this.executionState.currentAgent,
            currentOperation: this.executionState.currentOperation?.name,
            duration: this.executionState.isExecuting ? 
                Date.now() - this.executionState.startTime : 0,
            operationCount: this.executionState.operations.length
        };
    }

    /**
     * 获取性能统计
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalOperations > 0 ? 
                this.stats.completedOperations / this.stats.totalOperations : 0
        };
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 重置管理器
     */
    reset() {
        this.abortExecution('RESET');
        this.operationHistory = [];
        this.stats = {
            totalOperations: 0,
            completedOperations: 0,
            timedOutOperations: 0,
            failedOperations: 0,
            averageOperationTime: 0,
            operationTimes: []
        };
    }
}

module.exports = ExecutionManager;