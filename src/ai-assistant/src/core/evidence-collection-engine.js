const logger = require('../utils/logger');

/**
 * 多维度证据收集引擎
 * 收集技术、业务、行为三个维度的验证证据
 */
class EvidenceCollectionEngine {
    constructor() {
        this.evidenceWeights = {
            technical: 0.4,
            business: 0.4,
            behavioral: 0.2
        };
    }

    /**
     * 收集完成度证据
     */
    async collectCompletionEvidence(executionHistory, validationPatterns, taskObjective) {
        try {
            const evidence = {
                // 技术证据
                technical: await this.collectTechnicalEvidence(executionHistory, validationPatterns),
                
                // 业务证据  
                business: await this.collectBusinessEvidence(executionHistory, validationPatterns, taskObjective),
                
                // 行为证据
                behavioral: await this.collectBehavioralEvidence(executionHistory, validationPatterns),
                
                // 元数据
                metadata: {
                    collectionTime: new Date().toISOString(),
                    executionSteps: executionHistory.length,
                    patternsUsed: Object.keys(validationPatterns).length
                }
            };

            logger.info('📊 证据收集完成:', {
                technicalScore: evidence.technical.score,
                businessScore: evidence.business.score,
                behavioralScore: evidence.behavioral.score
            });

            return evidence;

        } catch (error) {
            logger.error('❌ 证据收集失败:', error.message);
            return this.getEmptyEvidence();
        }
    }

    /**
     * 收集技术证据
     */
    async collectTechnicalEvidence(executionHistory, validationPatterns) {
        const technical = {
            urlMatches: this.checkUrlPatterns(executionHistory, validationPatterns.urlPatterns || []),
            elementsFound: this.verifyRequiredElements(executionHistory, validationPatterns.requiredElements || []),
            stateProgression: this.validateStateTransitions(executionHistory, validationPatterns.stateTransitions || []),
            errorDetection: this.checkForErrors(executionHistory, validationPatterns.errorPatterns || []),
            toolExecutions: this.analyzeToolExecutions(executionHistory)
        };

        // 计算技术证据分数
        technical.score = this.calculateTechnicalScore(technical);
        technical.confidence = this.calculateTechnicalConfidence(technical);
        
        return technical;
    }

    /**
     * 收集业务证据
     */
    async collectBusinessEvidence(executionHistory, validationPatterns, taskObjective) {
        const business = {
            objectiveAchieved: this.assessObjectiveCompletion(executionHistory, taskObjective),
            expectedOutcome: this.verifyExpectedResults(executionHistory, validationPatterns.completionSignals || []),
            businessLogicFollowed: this.validateBusinessLogic(executionHistory, validationPatterns.businessLogic || {}),
            keyStepsCompleted: this.checkKeySteps(executionHistory, validationPatterns.businessLogic?.keySteps || []),
            successCriteriaMet: this.evaluateSuccessCriteria(executionHistory, validationPatterns.businessLogic?.successCriteria || [])
        };

        // 计算业务证据分数
        business.score = this.calculateBusinessScore(business);
        business.confidence = this.calculateBusinessConfidence(business);
        
        return business;
    }

    /**
     * 收集行为证据
     */
    async collectBehavioralEvidence(executionHistory, validationPatterns) {
        const behavioral = {
            actionSequence: this.validateActionSequence(executionHistory),
            userInterventions: this.analyzeUserInterventions(executionHistory),
            retryPatterns: this.assessRetryPatterns(executionHistory),
            progressConsistency: this.checkProgressConsistency(executionHistory),
            timelineAnalysis: this.analyzeExecutionTimeline(executionHistory)
        };

        // 计算行为证据分数
        behavioral.score = this.calculateBehavioralScore(behavioral);
        behavioral.confidence = this.calculateBehavioralConfidence(behavioral);
        
        return behavioral;
    }

    /**
     * 检查URL模式匹配
     */
    checkUrlPatterns(executionHistory, urlPatterns) {
        const urlChanges = executionHistory
            .filter(step => step.action === 'browser_navigate' || step.result?.includes('Page URL:'))
            .map(step => this.extractUrlFromStep(step));

        const matches = urlPatterns.map(pattern => {
            const matchCount = urlChanges.filter(url => 
                url && this.matchesPattern(url, pattern.pattern)
            ).length;
            
            return {
                pattern: pattern.pattern,
                stage: pattern.stage,
                matched: matchCount > 0,
                matchCount: matchCount
            };
        });

        return {
            patterns: matches,
            totalMatches: matches.filter(m => m.matched).length,
            matchRate: matches.length > 0 ? matches.filter(m => m.matched).length / matches.length : 0
        };
    }

    /**
     * 验证必需元素
     */
    verifyRequiredElements(executionHistory, requiredElements) {
        const snapshots = executionHistory
            .filter(step => step.action === 'browser_snapshot')
            .map(step => step.result);

        const elementChecks = requiredElements.map(element => {
            const found = snapshots.some(snapshot => 
                snapshot && this.elementExistsInSnapshot(snapshot, element.selector)
            );
            
            return {
                element: element.description,
                selector: element.selector,
                required: element.required,
                found: found,
                stage: element.stage
            };
        });

        return {
            elements: elementChecks,
            requiredFound: elementChecks.filter(e => e.required && e.found).length,
            requiredTotal: elementChecks.filter(e => e.required).length,
            completionRate: elementChecks.filter(e => e.required).length > 0 ? 
                elementChecks.filter(e => e.required && e.found).length / elementChecks.filter(e => e.required).length : 1
        };
    }

    /**
     * 验证状态转换
     */
    validateStateTransitions(executionHistory, stateTransitions) {
        // 简化实现：检查是否有状态变化的证据
        const hasStateChanges = executionHistory.some(step => 
            step.result && (
                step.result.includes('success') || 
                step.result.includes('completed') ||
                step.result.includes('Page URL:')
            )
        );

        return {
            transitionsExpected: stateTransitions.length,
            transitionsDetected: hasStateChanges ? 1 : 0,
            transitionRate: stateTransitions.length > 0 ? (hasStateChanges ? 1 : 0) / stateTransitions.length : 1
        };
    }

    /**
     * 检查错误
     */
    checkForErrors(executionHistory, errorPatterns) {
        const errors = executionHistory
            .filter(step => step.result && (
                step.result.includes('error') || 
                step.result.includes('failed') ||
                step.result.includes('Error')
            ))
            .map(step => ({
                step: step.action,
                error: step.result,
                severity: this.assessErrorSeverity(step.result, errorPatterns)
            }));

        return {
            errorCount: errors.length,
            errors: errors,
            hasBlockingErrors: errors.some(e => e.severity === 'high'),
            errorRate: executionHistory.length > 0 ? errors.length / executionHistory.length : 0
        };
    }

    /**
     * 分析工具执行
     */
    analyzeToolExecutions(executionHistory) {
        const toolStats = {};
        let successCount = 0;
        let totalCount = 0;

        executionHistory.forEach(step => {
            const tool = step.action;
            if (!toolStats[tool]) {
                toolStats[tool] = { total: 0, success: 0 };
            }
            
            toolStats[tool].total++;
            totalCount++;
            
            if (step.result && !step.result.includes('error') && !step.result.includes('failed')) {
                toolStats[tool].success++;
                successCount++;
            }
        });

        return {
            toolStats: toolStats,
            overallSuccessRate: totalCount > 0 ? successCount / totalCount : 0,
            toolsUsed: Object.keys(toolStats).length,
            totalExecutions: totalCount
        };
    }

    /**
     * 计算技术证据分数
     */
    calculateTechnicalScore(technical) {
        const weights = {
            urlMatches: 0.25,
            elementsFound: 0.30,
            stateProgression: 0.20,
            errorDetection: 0.15,
            toolExecutions: 0.10
        };

        let score = 0;
        score += (technical.urlMatches?.matchRate || 0) * weights.urlMatches;
        score += (technical.elementsFound?.completionRate || 0) * weights.elementsFound;
        score += (technical.stateProgression?.transitionRate || 0) * weights.stateProgression;
        score += (1 - (technical.errorDetection?.errorRate || 0)) * weights.errorDetection;
        score += (technical.toolExecutions?.overallSuccessRate || 0) * weights.toolExecutions;

        return Math.min(Math.max(score, 0), 1);
    }

    /**
     * 计算业务证据分数
     */
    calculateBusinessScore(business) {
        // 简化实现
        let score = 0;
        let factors = 0;

        if (business.objectiveAchieved !== undefined) {
            score += business.objectiveAchieved ? 1 : 0;
            factors++;
        }
        
        if (business.expectedOutcome !== undefined) {
            score += business.expectedOutcome ? 1 : 0;
            factors++;
        }

        return factors > 0 ? score / factors : 0.5;
    }

    /**
     * 计算行为证据分数
     */
    calculateBehavioralScore(behavioral) {
        // 简化实现
        let score = 0.7; // 基础分数
        
        if (behavioral.retryPatterns?.excessiveRetries) {
            score -= 0.2;
        }
        
        if (behavioral.userInterventions?.required) {
            score -= 0.1;
        }

        return Math.min(Math.max(score, 0), 1);
    }

    // 辅助方法
    extractUrlFromStep(step) {
        if (step.result && step.result.includes('Page URL:')) {
            const match = step.result.match(/Page URL:\s*([^\n]+)/);
            return match ? match[1].trim() : null;
        }
        return null;
    }

    matchesPattern(url, pattern) {
        // 简单的模式匹配
        return url.includes(pattern) || new RegExp(pattern).test(url);
    }

    elementExistsInSnapshot(snapshot, selector) {
        // 简单的元素存在检查
        return snapshot.includes(selector) || snapshot.toLowerCase().includes(selector.toLowerCase());
    }

    assessErrorSeverity(errorMessage, errorPatterns) {
        for (const pattern of errorPatterns) {
            if (errorMessage.includes(pattern.pattern)) {
                return pattern.severity;
            }
        }
        return 'medium';
    }

    calculateTechnicalConfidence(technical) {
        return Math.min(technical.score + 0.1, 1);
    }

    calculateBusinessConfidence(business) {
        return Math.min(business.score + 0.1, 1);
    }

    calculateBehavioralConfidence(behavioral) {
        return Math.min(behavioral.score + 0.1, 1);
    }

    // 其他简化的辅助方法
    assessObjectiveCompletion(executionHistory, taskObjective) {
        return executionHistory.length > 3; // 简化判断
    }

    verifyExpectedResults(executionHistory, completionSignals) {
        return completionSignals.length === 0 || executionHistory.some(step => 
            step.result && step.result.includes('success')
        );
    }

    validateBusinessLogic(executionHistory, businessLogic) {
        return true; // 简化实现
    }

    checkKeySteps(executionHistory, keySteps) {
        return keySteps.length === 0 || executionHistory.length >= keySteps.length;
    }

    evaluateSuccessCriteria(executionHistory, successCriteria) {
        return true; // 简化实现
    }

    validateActionSequence(executionHistory) {
        return { valid: true, score: 0.8 };
    }

    analyzeUserInterventions(executionHistory) {
        return { required: false, count: 0 };
    }

    assessRetryPatterns(executionHistory) {
        return { excessiveRetries: false, retryCount: 0 };
    }

    checkProgressConsistency(executionHistory) {
        return { consistent: true, score: 0.8 };
    }

    analyzeExecutionTimeline(executionHistory) {
        return { reasonable: true, duration: executionHistory.length };
    }

    getEmptyEvidence() {
        return {
            technical: { score: 0, confidence: 0 },
            business: { score: 0, confidence: 0 },
            behavioral: { score: 0, confidence: 0 },
            metadata: { collectionTime: new Date().toISOString() }
        };
    }
}

module.exports = EvidenceCollectionEngine;
