const logger = require('../utils/logger');

/**
 * 智能置信度计算框架
 * 基于多维度证据计算任务完成置信度
 */
class IntelligentConfidenceCalculator {
    constructor() {
        this.defaultWeights = {
            technical: 0.4,
            business: 0.4,
            behavioral: 0.2
        };
        
        this.taskComplexityWeights = {
            low: { technical: 0.3, business: 0.5, behavioral: 0.2 },
            medium: { technical: 0.4, business: 0.4, behavioral: 0.2 },
            high: { technical: 0.5, business: 0.3, behavioral: 0.2 },
            complex: { technical: 0.6, business: 0.25, behavioral: 0.15 }
        };

        this.historicalData = new Map(); // 存储历史校准数据
    }

    /**
     * 计算完成置信度
     */
    calculateCompletionConfidence(evidence, taskComplexity = 'medium', taskType = 'web_automation', historicalData = null) {
        try {
            // 基础分数计算
            const technicalScore = this.scoreTechnicalEvidence(evidence.technical);
            const businessScore = this.scoreBusinessEvidence(evidence.business);
            const behavioralScore = this.scoreBehavioralEvidence(evidence.behavioral);

            // 权重分配（基于任务类型动态调整）
            const weights = this.getTaskSpecificWeights(taskComplexity, taskType);

            // 加权计算
            const weightedScore = 
                technicalScore * weights.technical +
                businessScore * weights.business +
                behavioralScore * weights.behavioral;

            // 历史校准
            const calibratedScore = this.calibrateWithHistory(weightedScore, taskType, historicalData);

            // 不确定性评估
            const uncertainty = this.assessUncertainty(evidence);

            // 证据强度评估
            const evidenceStrength = this.calculateEvidenceStrength(evidence);

            const result = {
                confidence: Math.min(Math.max(calibratedScore, 0), 1),
                uncertainty: uncertainty,
                evidenceStrength: evidenceStrength,
                breakdown: {
                    technical: technicalScore,
                    business: businessScore,
                    behavioral: behavioralScore,
                    weights: weights
                },
                metadata: {
                    taskComplexity: taskComplexity,
                    taskType: taskType,
                    calculationTime: new Date().toISOString()
                }
            };

            logger.info('🧮 置信度计算完成:', {
                confidence: result.confidence.toFixed(3),
                uncertainty: result.uncertainty.toFixed(3),
                evidenceStrength: result.evidenceStrength.toFixed(3)
            });

            return result;

        } catch (error) {
            logger.error('❌ 置信度计算失败:', error.message);
            return this.getDefaultConfidence();
        }
    }

    /**
     * 评估技术证据分数
     */
    scoreTechnicalEvidence(technicalEvidence) {
        if (!technicalEvidence || typeof technicalEvidence.score !== 'number') {
            return 0.5; // 默认中等分数
        }

        let score = technicalEvidence.score;

        // 调整因子
        const adjustments = {
            // URL匹配调整
            urlMatch: technicalEvidence.urlMatches?.matchRate || 0.5,
            
            // 元素发现调整
            elementMatch: technicalEvidence.elementsFound?.completionRate || 0.5,
            
            // 错误检测调整
            errorPenalty: technicalEvidence.errorDetection?.hasBlockingErrors ? -0.3 : 0,
            
            // 工具执行成功率调整
            toolSuccess: (technicalEvidence.toolExecutions?.overallSuccessRate || 0.5) * 0.2
        };

        // 应用调整
        score = score * 0.6 + // 基础分数权重60%
                adjustments.urlMatch * 0.15 +
                adjustments.elementMatch * 0.15 +
                adjustments.toolSuccess +
                adjustments.errorPenalty;

        return Math.min(Math.max(score, 0), 1);
    }

    /**
     * 评估业务证据分数
     */
    scoreBusinessEvidence(businessEvidence) {
        if (!businessEvidence || typeof businessEvidence.score !== 'number') {
            return 0.5;
        }

        let score = businessEvidence.score;

        // 业务逻辑特定调整
        const businessAdjustments = {
            objectiveClarity: businessEvidence.objectiveAchieved ? 0.2 : -0.2,
            outcomeVerification: businessEvidence.expectedOutcome ? 0.15 : -0.1,
            logicConsistency: businessEvidence.businessLogicFollowed ? 0.1 : -0.05
        };

        // 应用业务调整
        Object.values(businessAdjustments).forEach(adjustment => {
            score += adjustment;
        });

        return Math.min(Math.max(score, 0), 1);
    }

    /**
     * 评估行为证据分数
     */
    scoreBehavioralEvidence(behavioralEvidence) {
        if (!behavioralEvidence || typeof behavioralEvidence.score !== 'number') {
            return 0.7; // 行为证据默认较高分数
        }

        let score = behavioralEvidence.score;

        // 行为模式调整
        const behavioralAdjustments = {
            sequenceValidity: behavioralEvidence.actionSequence?.valid ? 0.1 : -0.1,
            interventionPenalty: behavioralEvidence.userInterventions?.required ? -0.05 : 0.05,
            retryPenalty: behavioralEvidence.retryPatterns?.excessiveRetries ? -0.15 : 0,
            progressConsistency: behavioralEvidence.progressConsistency?.consistent ? 0.05 : -0.05
        };

        // 应用行为调整
        Object.values(behavioralAdjustments).forEach(adjustment => {
            score += adjustment;
        });

        return Math.min(Math.max(score, 0), 1);
    }

    /**
     * 获取任务特定权重
     */
    getTaskSpecificWeights(taskComplexity, taskType) {
        // 基于复杂度的基础权重
        let weights = { ...this.taskComplexityWeights[taskComplexity] } || { ...this.defaultWeights };

        // 基于任务类型的调整
        switch (taskType) {
            case 'web_automation':
                weights.technical += 0.1;
                weights.business -= 0.05;
                weights.behavioral -= 0.05;
                break;
            case 'data_processing':
                weights.business += 0.1;
                weights.technical -= 0.05;
                weights.behavioral -= 0.05;
                break;
            case 'user_interaction':
                weights.behavioral += 0.1;
                weights.technical -= 0.05;
                weights.business -= 0.05;
                break;
        }

        // 确保权重总和为1
        const total = weights.technical + weights.business + weights.behavioral;
        if (total !== 1) {
            weights.technical /= total;
            weights.business /= total;
            weights.behavioral /= total;
        }

        return weights;
    }

    /**
     * 历史数据校准
     */
    calibrateWithHistory(rawScore, taskType, historicalData) {
        if (!historicalData || historicalData.length === 0) {
            return rawScore; // 没有历史数据，返回原始分数
        }

        // 简化的历史校准：基于历史成功率调整
        const historicalSuccessRate = historicalData.filter(h => h.success).length / historicalData.length;
        
        // 如果历史成功率很高，稍微提升置信度
        // 如果历史成功率很低，稍微降低置信度
        const calibrationFactor = 0.9 + (historicalSuccessRate - 0.5) * 0.2;
        
        return rawScore * calibrationFactor;
    }

    /**
     * 评估不确定性
     */
    assessUncertainty(evidence) {
        let uncertainty = 0;

        // 技术证据不确定性
        if (evidence.technical) {
            const techConfidence = evidence.technical.confidence || 0.5;
            uncertainty += (1 - techConfidence) * 0.4;
        }

        // 业务证据不确定性
        if (evidence.business) {
            const bizConfidence = evidence.business.confidence || 0.5;
            uncertainty += (1 - bizConfidence) * 0.4;
        }

        // 行为证据不确定性
        if (evidence.behavioral) {
            const behavConfidence = evidence.behavioral.confidence || 0.7;
            uncertainty += (1 - behavConfidence) * 0.2;
        }

        // 数据完整性不确定性
        const dataCompleteness = this.assessDataCompleteness(evidence);
        uncertainty += (1 - dataCompleteness) * 0.3;

        return Math.min(Math.max(uncertainty, 0), 1);
    }

    /**
     * 计算证据强度
     */
    calculateEvidenceStrength(evidence) {
        let strength = 0;
        let factors = 0;

        // 技术证据强度
        if (evidence.technical && evidence.technical.score !== undefined) {
            strength += evidence.technical.score;
            factors++;
        }

        // 业务证据强度
        if (evidence.business && evidence.business.score !== undefined) {
            strength += evidence.business.score;
            factors++;
        }

        // 行为证据强度
        if (evidence.behavioral && evidence.behavioral.score !== undefined) {
            strength += evidence.behavioral.score;
            factors++;
        }

        // 数据量调整
        const dataVolume = this.assessDataVolume(evidence);
        strength *= (0.7 + dataVolume * 0.3); // 数据量越多，证据强度越高

        return factors > 0 ? strength / factors : 0;
    }

    /**
     * 评估数据完整性
     */
    assessDataCompleteness(evidence) {
        let completeness = 0;
        let expectedFields = 0;

        // 检查技术证据完整性
        if (evidence.technical) {
            expectedFields += 5; // 预期5个技术指标
            if (evidence.technical.urlMatches) completeness++;
            if (evidence.technical.elementsFound) completeness++;
            if (evidence.technical.stateProgression) completeness++;
            if (evidence.technical.errorDetection) completeness++;
            if (evidence.technical.toolExecutions) completeness++;
        }

        // 检查业务证据完整性
        if (evidence.business) {
            expectedFields += 3; // 预期3个业务指标
            if (evidence.business.objectiveAchieved !== undefined) completeness++;
            if (evidence.business.expectedOutcome !== undefined) completeness++;
            if (evidence.business.businessLogicFollowed !== undefined) completeness++;
        }

        // 检查行为证据完整性
        if (evidence.behavioral) {
            expectedFields += 2; // 预期2个行为指标
            if (evidence.behavioral.actionSequence) completeness++;
            if (evidence.behavioral.userInterventions) completeness++;
        }

        return expectedFields > 0 ? completeness / expectedFields : 0;
    }

    /**
     * 评估数据量
     */
    assessDataVolume(evidence) {
        let dataPoints = 0;

        // 计算技术数据点
        if (evidence.technical?.toolExecutions?.totalExecutions) {
            dataPoints += evidence.technical.toolExecutions.totalExecutions;
        }

        // 计算元数据点
        if (evidence.metadata?.executionSteps) {
            dataPoints += evidence.metadata.executionSteps;
        }

        // 归一化到0-1范围
        return Math.min(dataPoints / 20, 1); // 假设20个数据点为满分
    }

    /**
     * 获取默认置信度
     */
    getDefaultConfidence() {
        return {
            confidence: 0.5,
            uncertainty: 0.5,
            evidenceStrength: 0.3,
            breakdown: {
                technical: 0.5,
                business: 0.5,
                behavioral: 0.5,
                weights: this.defaultWeights
            },
            metadata: {
                taskComplexity: 'unknown',
                taskType: 'unknown',
                calculationTime: new Date().toISOString()
            }
        };
    }

    /**
     * 更新历史数据
     */
    updateHistoricalData(taskType, result) {
        if (!this.historicalData.has(taskType)) {
            this.historicalData.set(taskType, []);
        }
        
        const history = this.historicalData.get(taskType);
        history.push({
            timestamp: new Date().toISOString(),
            confidence: result.confidence,
            success: result.success,
            evidence: result.evidence
        });

        // 保持最近100条记录
        if (history.length > 100) {
            history.splice(0, history.length - 100);
        }
    }

    /**
     * 获取历史数据
     */
    getHistoricalData(taskType) {
        return this.historicalData.get(taskType) || [];
    }
}

module.exports = IntelligentConfidenceCalculator;
