const ValidationFeatureExtractor = require('./validation-feature-extractor');
const EvidenceCollectionEngine = require('./evidence-collection-engine');
const IntelligentConfidenceCalculator = require('./intelligent-confidence-calculator');
const logger = require('../utils/logger');
const fs = require('fs').promises;
const path = require('path');

/**
 * 增强的验证引擎
 * 集成多层次智能验证架构
 */
class EnhancedValidationEngine {
    constructor(config) {
        this.config = config;
        this.featureExtractor = new ValidationFeatureExtractor(config);
        this.evidenceEngine = new EvidenceCollectionEngine();
        this.confidenceCalculator = new IntelligentConfidenceCalculator();
        
        this.systemGuide = null;
        this.validationThresholds = {
            low: 0.6,
            medium: 0.75,
            high: 0.85,
            complex: 0.9
        };
    }

    /**
     * 初始化验证引擎
     */
    async initialize() {
        try {
            // 加载系统指南
            await this.loadSystemGuide();
            logger.info('✅ 增强验证引擎初始化完成');
        } catch (error) {
            logger.error('❌ 增强验证引擎初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 加载系统指南
     */
    async loadSystemGuide() {
        try {
            const guidePath = path.join(process.cwd(), 'system_guide.md');
            this.systemGuide = await fs.readFile(guidePath, 'utf-8');
            logger.info('📋 系统指南加载成功');
        } catch (error) {
            logger.warn('⚠️ 系统指南加载失败，使用默认验证模式:', error.message);
            this.systemGuide = '默认系统指南内容';
        }
    }

    /**
     * 渐进式验证 - 步骤级验证
     */
    async validateStep(stepResult, expectedOutcome, context = {}) {
        try {
            const validation = {
                stepSuccess: stepResult.success !== false,
                progressMade: this.assessStepProgress(stepResult),
                blockers: this.identifyStepBlockers(stepResult),
                nextStepValid: this.validateNextStep(stepResult, context),
                timestamp: new Date().toISOString()
            };

            logger.debug('🔍 步骤验证:', {
                action: stepResult.action,
                success: validation.stepSuccess,
                progressMade: validation.progressMade
            });

            return validation;

        } catch (error) {
            logger.error('❌ 步骤验证失败:', error.message);
            return {
                stepSuccess: false,
                progressMade: false,
                blockers: ['验证系统错误'],
                nextStepValid: false,
                error: error.message
            };
        }
    }

    /**
     * 渐进式验证 - 阶段级验证
     */
    async validatePhase(phaseResults, phaseObjective, context = {}) {
        try {
            const validation = {
                phaseComplete: this.assessPhaseCompletion(phaseResults),
                objectiveProgress: this.measureObjectiveProgress(phaseResults, phaseObjective),
                qualityScore: this.assessPhaseQuality(phaseResults),
                nextPhaseReady: this.checkNextPhasePrerequisites(phaseResults),
                timestamp: new Date().toISOString()
            };

            logger.debug('🔍 阶段验证:', {
                objective: phaseObjective,
                complete: validation.phaseComplete,
                quality: validation.qualityScore
            });

            return validation;

        } catch (error) {
            logger.error('❌ 阶段验证失败:', error.message);
            return {
                phaseComplete: false,
                objectiveProgress: 0,
                qualityScore: 0,
                nextPhaseReady: false,
                error: error.message
            };
        }
    }

    /**
     * 最终验证 - 任务完成验证
     */
    async validateCompletion(aiResponse, originalTask, executionHistory = []) {
        try {
            logger.info('🔍 开始增强验证流程...');

            // 1. 提取验证模式
            const validationPatterns = await this.featureExtractor.extractValidationPatterns(
                originalTask.taskType || 'web_automation',
                this.systemGuide,
                originalTask.content
            );

            // 2. 收集多维度证据
            const evidence = await this.evidenceEngine.collectCompletionEvidence(
                executionHistory,
                validationPatterns,
                originalTask.content
            );

            // 3. 计算智能置信度
            const confidenceResult = this.confidenceCalculator.calculateCompletionConfidence(
                evidence,
                originalTask.complexityLevel || 'medium',
                originalTask.taskType || 'web_automation',
                this.getHistoricalData(originalTask.taskType)
            );

            // 4. 综合判断
            const threshold = this.getValidationThreshold(originalTask.complexityLevel || 'medium');
            const isCompleted = confidenceResult.confidence >= threshold && 
                              confidenceResult.uncertainty < 0.5;

            // 5. 生成验证报告
            const validationResult = {
                isCompleted: isCompleted,
                confidence: confidenceResult.confidence,
                uncertainty: confidenceResult.uncertainty,
                evidenceStrength: confidenceResult.evidenceStrength,
                
                reasoning: this.generateReasoningReport(evidence, confidenceResult, validationPatterns),
                evidence: this.summarizeEvidence(evidence),
                gaps: this.identifyCompletionGaps(evidence, validationPatterns),
                
                requiresUserIntervention: this.determineUserIntervention(evidence, confidenceResult),
                interventionType: this.determineInterventionType(evidence, confidenceResult),
                
                nextAction: this.suggestNextAction(isCompleted, evidence, confidenceResult),
                
                // 详细分析
                breakdown: confidenceResult.breakdown,
                validationPatterns: validationPatterns,
                
                // 元数据
                metadata: {
                    validationTime: new Date().toISOString(),
                    threshold: threshold,
                    engineVersion: '2.0',
                    validationMethod: 'enhanced-multi-dimensional'
                }
            };

            // 6. 更新历史数据
            this.confidenceCalculator.updateHistoricalData(
                originalTask.taskType || 'web_automation',
                {
                    confidence: confidenceResult.confidence,
                    success: isCompleted,
                    evidence: evidence
                }
            );

            logger.info('✅ 增强验证完成:', {
                isCompleted: validationResult.isCompleted,
                confidence: validationResult.confidence.toFixed(3),
                uncertainty: validationResult.uncertainty.toFixed(3),
                evidenceStrength: validationResult.evidenceStrength.toFixed(3)
            });

            return validationResult;

        } catch (error) {
            logger.error('❌ 增强验证失败:', error.message);
            return this.getFallbackValidation(aiResponse, originalTask, executionHistory);
        }
    }

    /**
     * 生成推理报告
     */
    generateReasoningReport(evidence, confidenceResult, validationPatterns) {
        const reasons = [];

        // 技术证据分析
        if (evidence.technical) {
            if (evidence.technical.score > 0.7) {
                reasons.push(`技术执行良好 (${(evidence.technical.score * 100).toFixed(1)}%)`);
            } else {
                reasons.push(`技术执行存在问题 (${(evidence.technical.score * 100).toFixed(1)}%)`);
            }
        }

        // 业务证据分析
        if (evidence.business) {
            if (evidence.business.score > 0.7) {
                reasons.push(`业务目标达成 (${(evidence.business.score * 100).toFixed(1)}%)`);
            } else {
                reasons.push(`业务目标未完全达成 (${(evidence.business.score * 100).toFixed(1)}%)`);
            }
        }

        // 行为证据分析
        if (evidence.behavioral) {
            if (evidence.behavioral.score > 0.7) {
                reasons.push(`执行行为正常 (${(evidence.behavioral.score * 100).toFixed(1)}%)`);
            } else {
                reasons.push(`执行行为异常 (${(evidence.behavioral.score * 100).toFixed(1)}%)`);
            }
        }

        // 不确定性分析
        if (confidenceResult.uncertainty > 0.5) {
            reasons.push(`验证存在较高不确定性 (${(confidenceResult.uncertainty * 100).toFixed(1)}%)`);
        }

        return reasons.join('; ');
    }

    /**
     * 总结证据
     */
    summarizeEvidence(evidence) {
        const summary = [];

        if (evidence.technical?.toolExecutions?.totalExecutions) {
            summary.push(`执行了${evidence.technical.toolExecutions.totalExecutions}个操作`);
        }

        if (evidence.technical?.urlMatches?.totalMatches) {
            summary.push(`匹配了${evidence.technical.urlMatches.totalMatches}个URL模式`);
        }

        if (evidence.technical?.errorDetection?.errorCount > 0) {
            summary.push(`检测到${evidence.technical.errorDetection.errorCount}个错误`);
        }

        return summary;
    }

    /**
     * 识别完成度缺口
     */
    identifyCompletionGaps(evidence, validationPatterns) {
        const gaps = [];

        // 检查URL模式缺口
        if (evidence.technical?.urlMatches?.matchRate < 0.5) {
            gaps.push('URL导航模式不匹配');
        }

        // 检查元素缺口
        if (evidence.technical?.elementsFound?.completionRate < 0.5) {
            gaps.push('关键页面元素未找到');
        }

        // 检查业务逻辑缺口
        if (evidence.business?.score < 0.5) {
            gaps.push('业务目标未达成');
        }

        return gaps;
    }

    /**
     * 确定是否需要用户干预
     */
    determineUserIntervention(evidence, confidenceResult) {
        // 置信度过低需要干预
        if (confidenceResult.confidence < 0.5) {
            return true;
        }

        // 不确定性过高需要干预
        if (confidenceResult.uncertainty > 0.7) {
            return true;
        }

        // 检测到阻塞性错误需要干预
        if (evidence.technical?.errorDetection?.hasBlockingErrors) {
            return true;
        }

        return false;
    }

    /**
     * 确定干预类型
     */
    determineInterventionType(evidence, confidenceResult) {
        if (evidence.technical?.errorDetection?.hasBlockingErrors) {
            return 'error_resolution';
        }

        if (confidenceResult.confidence < 0.3) {
            return 'manual_completion';
        }

        if (confidenceResult.uncertainty > 0.7) {
            return 'verification';
        }

        return 'none';
    }

    /**
     * 建议下一步行动
     */
    suggestNextAction(isCompleted, evidence, confidenceResult) {
        if (isCompleted) {
            return '任务已完成，无需进一步操作';
        }

        if (confidenceResult.confidence < 0.3) {
            return '建议人工接管完成任务';
        }

        if (evidence.technical?.errorDetection?.hasBlockingErrors) {
            return '需要解决阻塞性错误后继续';
        }

        return '继续执行剩余步骤';
    }

    /**
     * 获取验证阈值
     */
    getValidationThreshold(complexityLevel) {
        return this.validationThresholds[complexityLevel] || this.validationThresholds.medium;
    }

    /**
     * 获取历史数据
     */
    getHistoricalData(taskType) {
        return this.confidenceCalculator.getHistoricalData(taskType);
    }

    /**
     * 获取后备验证结果
     */
    getFallbackValidation(aiResponse, originalTask, executionHistory) {
        return {
            isCompleted: false,
            confidence: 0.3,
            uncertainty: 0.8,
            evidenceStrength: 0.2,
            reasoning: '验证系统故障，使用后备验证',
            evidence: [],
            gaps: ['验证系统不可用'],
            requiresUserIntervention: true,
            interventionType: 'manual_verification',
            nextAction: '请人工验证任务完成状态',
            metadata: {
                validationTime: new Date().toISOString(),
                validationMethod: 'fallback'
            }
        };
    }

    // 辅助方法的简化实现
    assessStepProgress(stepResult) {
        return stepResult.success !== false && !stepResult.result?.includes('error');
    }

    identifyStepBlockers(stepResult) {
        const blockers = [];
        if (stepResult.result?.includes('error')) {
            blockers.push('执行错误');
        }
        return blockers;
    }

    validateNextStep(stepResult, context) {
        return stepResult.success !== false;
    }

    assessPhaseCompletion(phaseResults) {
        return phaseResults.length > 0 && phaseResults.every(r => r.success !== false);
    }

    measureObjectiveProgress(phaseResults, phaseObjective) {
        return phaseResults.length > 0 ? 0.8 : 0.2;
    }

    assessPhaseQuality(phaseResults) {
        const successRate = phaseResults.filter(r => r.success !== false).length / phaseResults.length;
        return successRate || 0;
    }

    checkNextPhasePrerequisites(phaseResults) {
        return this.assessPhaseCompletion(phaseResults);
    }
}

module.exports = EnhancedValidationEngine;
