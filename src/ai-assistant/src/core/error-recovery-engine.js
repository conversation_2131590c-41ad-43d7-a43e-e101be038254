/**
 * 智能错误恢复引擎
 * 负责错误分类、恢复策略选择和自动恢复执行
 */

const logger = require('../utils/logger');

class ErrorRecoveryEngine {
    constructor() {
        // 错误分类定义
        this.errorCategories = {
            NETWORK: {
                patterns: ['network', 'timeout', 'ECONNREFUSED', 'ETIMEDOUT', 'fetch failed'],
                priority: 1,
                recoverable: true
            },
            PAGE_NOT_FOUND: {
                patterns: ['404', 'not found', 'page not found', 'about:blank'],
                priority: 2,
                recoverable: true
            },
            ELEMENT_NOT_FOUND: {
                patterns: ['element not found', 'selector not found', 'no element matching'],
                priority: 3,
                recoverable: true
            },
            PERMISSION_DENIED: {
                patterns: ['403', 'forbidden', 'permission denied', 'unauthorized'],
                priority: 4,
                recoverable: false
            },
            VALIDATION_FAILED: {
                patterns: ['validation failed', 'VALIDATION_LIMIT_EXCEEDED', 'verification failed'],
                priority: 5,
                recoverable: true
            },
            EXECUTION_TIMEOUT: {
                patterns: ['TimeoutError', 'execution timeout', 'operation timeout'],
                priority: 6,
                recoverable: true
            },
            UNKNOWN_ERROR: {
                patterns: [],
                priority: 10,
                recoverable: false
            }
        };

        // 恢复策略库
        this.recoveryStrategies = {
            NETWORK: [
                {
                    name: 'retry_with_backoff',
                    description: '使用指数退避重试',
                    action: async (context) => {
                        const delay = Math.min(1000 * Math.pow(2, context.retryCount || 0), 10000);
                        logger.info(`等待 ${delay}ms 后重试...`);
                        await this.delay(delay);
                        return { retry: true, adjustments: { timeout: 60000 } };
                    }
                },
                {
                    name: 'refresh_page',
                    description: '刷新页面重试',
                    action: async (context) => {
                        if (context.mcpClient) {
                            await context.mcpClient.navigate(context.currentUrl);
                            await this.delay(3000);
                        }
                        return { retry: true };
                    }
                }
            ],
            PAGE_NOT_FOUND: [
                {
                    name: 'navigate_to_home',
                    description: '导航到主页',
                    action: async (context) => {
                        const baseUrl = this.extractBaseUrl(context.targetUrl);
                        if (context.mcpClient && baseUrl) {
                            await context.mcpClient.navigate(baseUrl);
                            await this.delay(3000);
                        }
                        return { retry: true, adjustments: { startFromHome: true } };
                    }
                },
                {
                    name: 'check_url_validity',
                    description: '检查并修正URL',
                    action: async (context) => {
                        const correctedUrl = this.correctUrl(context.targetUrl);
                        if (correctedUrl !== context.targetUrl) {
                            if (context.mcpClient) {
                                await context.mcpClient.navigate(correctedUrl);
                                await this.delay(3000);
                            }
                            return { retry: true, adjustments: { targetUrl: correctedUrl } };
                        }
                        return { retry: false, reason: '无法修正URL' };
                    }
                }
            ],
            ELEMENT_NOT_FOUND: [
                {
                    name: 'wait_and_retry',
                    description: '等待元素加载',
                    action: async (context) => {
                        logger.info('等待页面元素加载...');
                        await this.delay(5000);
                        return { retry: true };
                    }
                },
                {
                    name: 'alternative_selector',
                    description: '尝试替代选择器',
                    action: async (context) => {
                        return { 
                            retry: true, 
                            adjustments: { 
                                useAlternativeSelector: true,
                                selectorStrategy: 'text_based' 
                            } 
                        };
                    }
                },
                {
                    name: 'scroll_and_search',
                    description: '滚动页面查找元素',
                    action: async (context) => {
                        if (context.mcpClient) {
                            // 向下滚动
                            await context.mcpClient.executeScript('window.scrollBy(0, 500)');
                            await this.delay(1000);
                        }
                        return { retry: true, adjustments: { scrolled: true } };
                    }
                }
            ],
            VALIDATION_FAILED: [
                {
                    name: 'change_approach',
                    description: '改变验证方法',
                    action: async (context) => {
                        return { 
                            retry: true, 
                            adjustments: { 
                                validationStrategy: 'visual_only',
                                skipDomValidation: true 
                            } 
                        };
                    }
                },
                {
                    name: 'relax_criteria',
                    description: '放宽验证标准',
                    action: async (context) => {
                        return { 
                            retry: true, 
                            adjustments: { 
                                validationLevel: 'basic',
                                acceptPartialSuccess: true 
                            } 
                        };
                    }
                }
            ],
            EXECUTION_TIMEOUT: [
                {
                    name: 'increase_timeout',
                    description: '增加超时时间',
                    action: async (context) => {
                        return { 
                            retry: true, 
                            adjustments: { 
                                operationTimeout: 60000,
                                enableTimeout: false // 临时禁用超时
                            } 
                        };
                    }
                },
                {
                    name: 'simplify_operation',
                    description: '简化操作步骤',
                    action: async (context) => {
                        return { 
                            retry: true, 
                            adjustments: { 
                                breakDownOperation: true,
                                useSimpleSteps: true 
                            } 
                        };
                    }
                }
            ]
        };

        // 恢复历史记录
        this.recoveryHistory = [];
        this.maxHistorySize = 100;

        // 人工介入触发条件
        this.humanInterventionRules = {
            maxConsecutiveFailures: 5,
            maxTotalFailures: 10,
            criticalErrors: ['PERMISSION_DENIED', 'AUTHENTICATION_FAILED'],
            timeLimit: 300000 // 5分钟
        };
    }

    /**
     * 分析错误并返回分类
     */
    classifyError(error, context = {}) {
        const errorMessage = error.message || error.toString();
        const errorStack = error.stack || '';
        
        // 遍历错误类别，找到匹配的
        for (const [category, config] of Object.entries(this.errorCategories)) {
            if (config.patterns.some(pattern => 
                errorMessage.toLowerCase().includes(pattern.toLowerCase()) ||
                errorStack.toLowerCase().includes(pattern.toLowerCase())
            )) {
                return {
                    category,
                    recoverable: config.recoverable,
                    priority: config.priority,
                    originalError: error
                };
            }
        }
        
        // 默认为未知错误
        return {
            category: 'UNKNOWN_ERROR',
            recoverable: false,
            priority: 10,
            originalError: error
        };
    }

    /**
     * 获取恢复策略
     */
    async getRecoveryStrategy(errorInfo, context = {}) {
        const strategies = this.recoveryStrategies[errorInfo.category] || [];
        
        // 根据上下文和历史选择最佳策略
        for (const strategy of strategies) {
            // 检查这个策略是否最近失败过
            const recentFailure = this.checkRecentFailure(strategy.name, errorInfo.category);
            if (!recentFailure) {
                return strategy;
            }
        }
        
        // 如果所有策略都失败过，返回第一个
        return strategies[0] || null;
    }

    /**
     * 执行恢复策略
     */
    async executeRecovery(error, context = {}) {
        logger.info('🔧 开始错误恢复流程');
        
        // 1. 分类错误
        const errorInfo = this.classifyError(error, context);
        logger.info(`错误分类: ${errorInfo.category}, 可恢复: ${errorInfo.recoverable}`);
        
        // 2. 检查是否需要人工介入
        if (this.shouldRequestHumanIntervention(errorInfo, context)) {
            logger.warn('需要人工介入');
            return {
                success: false,
                needsHumanIntervention: true,
                reason: '错误超出自动恢复能力'
            };
        }
        
        // 3. 如果不可恢复，直接返回
        if (!errorInfo.recoverable) {
            logger.error('错误不可恢复');
            return {
                success: false,
                recoverable: false,
                reason: `不可恢复的错误: ${errorInfo.category}`
            };
        }
        
        // 4. 获取恢复策略
        const strategy = await this.getRecoveryStrategy(errorInfo, context);
        if (!strategy) {
            logger.error('没有可用的恢复策略');
            return {
                success: false,
                reason: '没有合适的恢复策略'
            };
        }
        
        logger.info(`执行恢复策略: ${strategy.name} - ${strategy.description}`);
        
        try {
            // 5. 执行恢复策略
            const result = await strategy.action(context);
            
            // 6. 记录恢复历史
            this.recordRecoveryAttempt({
                errorCategory: errorInfo.category,
                strategy: strategy.name,
                success: result.retry || false,
                timestamp: new Date().toISOString(),
                adjustments: result.adjustments
            });
            
            return {
                success: true,
                retry: result.retry,
                adjustments: result.adjustments || {},
                strategy: strategy.name
            };
            
        } catch (recoveryError) {
            logger.error('恢复策略执行失败:', recoveryError);
            
            this.recordRecoveryAttempt({
                errorCategory: errorInfo.category,
                strategy: strategy.name,
                success: false,
                timestamp: new Date().toISOString(),
                error: recoveryError.message
            });
            
            return {
                success: false,
                reason: `恢复策略执行失败: ${recoveryError.message}`
            };
        }
    }

    /**
     * 检查是否需要人工介入
     */
    shouldRequestHumanIntervention(errorInfo, context) {
        // 1. 检查是否是关键错误
        if (this.humanInterventionRules.criticalErrors.includes(errorInfo.category)) {
            return true;
        }
        
        // 2. 检查连续失败次数
        const recentFailures = this.getRecentFailures();
        if (recentFailures.length >= this.humanInterventionRules.maxConsecutiveFailures) {
            return true;
        }
        
        // 3. 检查总失败次数
        const totalFailures = this.recoveryHistory.filter(h => !h.success).length;
        if (totalFailures >= this.humanInterventionRules.maxTotalFailures) {
            return true;
        }
        
        // 4. 检查执行时间
        if (context.executionTime && context.executionTime > this.humanInterventionRules.timeLimit) {
            return true;
        }
        
        return false;
    }

    /**
     * 检查策略是否最近失败过
     */
    checkRecentFailure(strategyName, errorCategory) {
        const recentAttempts = this.recoveryHistory.slice(-10);
        return recentAttempts.some(attempt => 
            attempt.strategy === strategyName && 
            attempt.errorCategory === errorCategory && 
            !attempt.success
        );
    }

    /**
     * 获取最近的失败记录
     */
    getRecentFailures() {
        return this.recoveryHistory
            .slice(-10)
            .filter(h => !h.success);
    }

    /**
     * 记录恢复尝试
     */
    recordRecoveryAttempt(attempt) {
        this.recoveryHistory.push(attempt);
        
        // 限制历史记录大小
        if (this.recoveryHistory.length > this.maxHistorySize) {
            this.recoveryHistory.shift();
        }
    }

    /**
     * 提取基础URL
     */
    extractBaseUrl(url) {
        try {
            const urlObj = new URL(url);
            return `${urlObj.protocol}//${urlObj.host}`;
        } catch {
            return null;
        }
    }

    /**
     * 修正URL
     */
    correctUrl(url) {
        // 简单的URL修正逻辑
        if (!url.startsWith('http')) {
            return `https://${url}`;
        }
        return url;
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取恢复建议
     */
    getRecoverySuggestions(errorCategory) {
        const suggestions = {
            NETWORK: [
                '检查网络连接',
                '尝试使用VPN',
                '等待一段时间后重试'
            ],
            PAGE_NOT_FOUND: [
                '确认URL是否正确',
                '检查页面是否需要登录',
                '尝试从主页导航到目标页面'
            ],
            ELEMENT_NOT_FOUND: [
                '等待页面完全加载',
                '检查元素选择器是否正确',
                '尝试使用不同的定位方法'
            ],
            PERMISSION_DENIED: [
                '检查登录状态',
                '确认账户权限',
                '联系管理员'
            ],
            VALIDATION_FAILED: [
                '检查操作是否真的成功',
                '放宽验证条件',
                '使用替代验证方法'
            ],
            EXECUTION_TIMEOUT: [
                '检查网络速度',
                '简化操作步骤',
                '增加超时时间'
            ]
        };
        
        return suggestions[errorCategory] || ['检查错误详情', '重试操作', '寻求技术支持'];
    }

    /**
     * 重置恢复引擎
     */
    reset() {
        this.recoveryHistory = [];
    }

    /**
     * 获取恢复统计
     */
    getStatistics() {
        const total = this.recoveryHistory.length;
        const successful = this.recoveryHistory.filter(h => h.success).length;
        const byCategory = {};
        
        this.recoveryHistory.forEach(attempt => {
            if (!byCategory[attempt.errorCategory]) {
                byCategory[attempt.errorCategory] = {
                    total: 0,
                    successful: 0
                };
            }
            byCategory[attempt.errorCategory].total++;
            if (attempt.success) {
                byCategory[attempt.errorCategory].successful++;
            }
        });
        
        return {
            totalAttempts: total,
            successfulRecoveries: successful,
            successRate: total > 0 ? successful / total : 0,
            byCategory
        };
    }
}

module.exports = ErrorRecoveryEngine;