/**
 * Agent协调器
 * 负责管理和协调所有Agent的工作
 */

const logger = require('../utils/logger');
const OrchestratorAgent = require('../agents/orchestrator-agent');
const ObserverAgent = require('../agents/observer-agent');
const DecisionAgent = require('../agents/decision-agent');
const ExecutorAgent = require('../agents/executor-agent');
const ValidatorAgent = require('../agents/validator-agent');
const ExecutionManager = require('./execution-manager');

class AgentCoordinator {
    constructor(mcpClient = null) {
        // 初始化所有Agent
        this.orchestrator = new OrchestratorAgent();
        this.observer = new ObserverAgent();
        this.decision = new DecisionAgent();
        this.executor = new ExecutorAgent();
        this.validator = new ValidatorAgent();
        
        // 设置MCP客户端
        if (mcpClient) {
            this.executor.setMCPClient(mcpClient);
        }
        
        // 初始化执行管理器
        this.executionManager = new ExecutionManager({
            operationTimeout: 30000,      // 单操作30秒超时
            taskTimeout: 300000,          // 总任务5分钟超时
            maxRetries: 3,
            enableTimeout: true
        });
        
        // 监听执行管理器事件
        this.setupExecutionManagerListeners();
        
        // 执行上下文
        this.context = {
            workOrder: null,
            taskGoal: null,
            executionHistory: [],
            currentState: null,
            startTime: null
        };
        
        // 执行统计
        this.stats = {
            totalIterations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            agentCalls: {}
        };
        
        // 验证循环检测
        this.validationCycleDetector = {
            consecutiveValidations: 0,
            maxConsecutiveValidations: 3,
            lastValidationPassed: true
        };
    }
    
    /**
     * 设置执行管理器监听器
     */
    setupExecutionManagerListeners() {
        // 监听操作超时
        this.executionManager.on('operation:timeout', (operation) => {
            logger.warn(`⏱️ Agent操作超时: ${operation.name} (${operation.agent})`);
            // 记录超时信息到上下文
            this.context.executionHistory.push({
                type: 'timeout',
                operation: operation.name,
                agent: operation.agent,
                timestamp: new Date().toISOString()
            });
        });
        
        // 监听任务中止
        this.executionManager.on('execution:aborted', (info) => {
            logger.error(`🛑 任务被中止: ${info.reason}`);
        });
        
        // 监听操作失败
        this.executionManager.on('operation:failed', (operation) => {
            logger.error(`❌ Agent操作失败: ${operation.name} (${operation.agent})`);
        });
    }

    /**
     * 执行工单任务
     */
    async executeWorkOrder(workOrder) {
        logger.info('🚀 开始执行工单', { workOrderId: workOrder.id });
        
        this.context.workOrder = workOrder;
        this.context.startTime = Date.now();
        
        // 启动执行管理器
        const abortSignal = this.executionManager.startExecution(workOrder.id);
        
        try {
            // 1. 分析工单
            const analysis = await this.callAgent('orchestrator', 'analyzeWorkOrder', workOrder);
            this.context.taskGoal = analysis.goal;
            
            logger.info('📋 工单分析完成', analysis);
            
            // 2. 执行主循环
            const result = await this.executeMainLoop(analysis);
            
            // 3. 生成执行报告
            const report = await this.generateReport(result);
            
            logger.info('✅ 工单执行完成', {
                workOrderId: workOrder.id,
                success: result.success,
                duration: Date.now() - this.context.startTime
            });
            
            // 结束执行管理
            const executionSummary = this.executionManager.endExecution({
                success: result.success,
                report: report
            });
            
            // 将执行统计添加到报告
            report.executionStats = executionSummary;
            
            return report;
            
        } catch (error) {
            logger.error('❌ 工单执行失败', error);
            
            // 结束执行管理
            this.executionManager.endExecution({
                success: false,
                error: error.message
            });
            
            return {
                success: false,
                error: error.message,
                workOrderId: workOrder.id,
                executionHistory: this.context.executionHistory
            };
        }
    }

    /**
     * 执行主循环
     */
    async executeMainLoop(analysis) {
        const maxIterations = 50;
        let taskCompleted = false;
        let needsHumanHelp = false;
        
        while (!taskCompleted && !needsHumanHelp && this.stats.totalIterations < maxIterations) {
            this.stats.totalIterations++;
            
            logger.info(`\n🔄 第 ${this.stats.totalIterations} 轮执行`);
            
            try {
                // 1. 观察当前页面
                const observation = await this.observeCurrentPage();
                this.context.currentState = observation;
                
                // 2. 协调下一步
                const coordination = await this.callAgent(
                    'orchestrator', 
                    'coordinateNextStep',
                    observation,
                    this.context.executionHistory.slice(-1)[0]?.validation,
                    this.context.executionHistory // 传递执行历史
                );
                
                // 检测验证循环
                if (coordination.nextAgent === 'ValidatorAgent') {
                    this.validationCycleDetector.consecutiveValidations++;
                    
                    // 如果连续验证超过限制且上次验证失败，强制切换策略
                    if (this.validationCycleDetector.consecutiveValidations > this.validationCycleDetector.maxConsecutiveValidations 
                        && !this.validationCycleDetector.lastValidationPassed) {
                        logger.warn('⚠️ 检测到验证循环，强制切换策略');
                        coordination.nextAgent = 'DecisionAgent';
                        coordination.instruction = '当前验证陷入循环，请制定新的执行策略';
                        coordination.reasoning = '连续多次验证失败，需要调整方案';
                    }
                } else {
                    // 重置连续验证计数
                    this.validationCycleDetector.consecutiveValidations = 0;
                }
                
                // 3. 根据协调结果执行相应操作
                const cycleResult = await this.executeCycle(coordination, observation);
                
                // 4. 记录执行历史
                this.context.executionHistory.push(cycleResult);
                
                // 5. 检查是否完成或需要帮助
                if (cycleResult.validation?.taskCompleted) {
                    taskCompleted = true;
                } else if (cycleResult.validation?.recommendation?.nextAction === 'NEED_HELP') {
                    needsHumanHelp = true;
                }
                
            } catch (error) {
                logger.error('执行循环出错', error);
                
                // 尝试恢复
                const recovery = await this.attemptRecovery(error);
                if (!recovery.success) {
                    needsHumanHelp = true;
                }
            }
        }
        
        // 最终评估
        const finalEvaluation = await this.callAgent(
            'orchestrator',
            'evaluateCompletion',
            this.context.workOrder,
            this.generateExecutionSummary()
        );
        
        return {
            success: taskCompleted,
            needsHumanHelp,
            finalEvaluation,
            iterations: this.stats.totalIterations
        };
    }

    /**
     * 执行单个循环
     */
    async executeCycle(coordination, observation) {
        const cycleResult = {
            iteration: this.stats.totalIterations,
            coordination,
            timestamp: new Date().toISOString()
        };
        
        switch (coordination.nextAgent) {
            case 'DecisionAgent':
                // 做决策
                const decision = await this.callAgent(
                    'decision',
                    'makeDecision',
                    observation,
                    this.context.taskGoal,
                    this.context.executionHistory
                );
                cycleResult.decision = decision;
                
                // 如果有决策，执行它
                if (decision.action) {
                    const execution = await this.callAgent(
                        'executor',
                        'executeAction',
                        decision
                    );
                    cycleResult.execution = execution;
                    
                    // 验证执行结果
                    const beforeState = observation;
                    const afterState = await this.observeCurrentPage();
                    
                    const validation = await this.callAgent(
                        'validator',
                        'validateOperation',
                        execution,
                        beforeState,
                        afterState
                    );
                    cycleResult.validation = validation;
                    
                    // 更新验证通过状态
                    this.validationCycleDetector.lastValidationPassed = validation.validation?.passed || false;
                    
                    // 更新统计
                    if (validation.validation?.passed) {
                        this.stats.successfulOperations++;
                    } else {
                        this.stats.failedOperations++;
                    }
                }
                break;
                
            case 'ValidatorAgent':
                // 直接验证任务完成度
                const taskValidation = await this.callAgent(
                    'validator',
                    'validateTaskCompletion',
                    this.context.taskGoal,
                    this.generateExecutionSummary(),
                    observation
                );
                cycleResult.validation = taskValidation;
                
                // 更新验证通过状态
                this.validationCycleDetector.lastValidationPassed = taskValidation.validation?.passed || taskValidation.taskCompleted || false;
                break;
                
            case 'ObserverAgent':
                // 深入分析特定元素或状态
                cycleResult.observation = observation;
                break;
                
            default:
                logger.warn('未知的Agent指令:', coordination.nextAgent);
        }
        
        return cycleResult;
    }

    /**
     * 观察当前页面
     */
    async observeCurrentPage() {
        // 获取页面快照
        const snapshot = await this.getPageSnapshot();
        
        // 让Observer Agent分析
        const observation = await this.callAgent(
            'observer',
            'observePage',
            snapshot
        );
        
        return observation;
    }

    /**
     * 获取页面快照
     */
    async getPageSnapshot() {
        if (this.executor.mcpClient) {
            try {
                const snapshot = await this.executor.mcpClient.snapshot();
                const screenshot = await this.executor.mcpClient.screenshot();
                
                return {
                    ...snapshot,
                    screenshot: screenshot.screenshot,
                    capturedAt: new Date().toISOString()
                };
            } catch (error) {
                logger.error('获取页面快照失败', error);
                return {
                    error: error.message,
                    capturedAt: new Date().toISOString()
                };
            }
        }
        
        // 没有MCP客户端时的模拟数据
        return {
            url: 'mock://page',
            title: 'Mock Page',
            elements: {},
            capturedAt: new Date().toISOString()
        };
    }

    /**
     * 尝试错误恢复
     */
    async attemptRecovery(error) {
        logger.info('🔧 尝试错误恢复...');
        
        const lastAction = this.context.executionHistory.slice(-1)[0];
        const currentState = await this.observeCurrentPage();
        
        const recovery = await this.callAgent(
            'decision',
            'makeRecoveryDecision',
            error,
            lastAction,
            currentState
        );
        
        if (recovery.action) {
            try {
                const result = await this.callAgent(
                    'executor',
                    'executeAction',
                    recovery
                );
                return { success: true, result };
            } catch (recoveryError) {
                logger.error('恢复失败', recoveryError);
                return { success: false, error: recoveryError };
            }
        }
        
        return { success: false, reason: '无法恢复' };
    }

    /**
     * 调用Agent
     */
    async callAgent(agentName, method, ...args) {
        const agent = this[agentName];
        if (!agent) {
            throw new Error(`未知的Agent: ${agentName}`);
        }
        
        // 更新统计
        if (!this.stats.agentCalls[agentName]) {
            this.stats.agentCalls[agentName] = 0;
        }
        this.stats.agentCalls[agentName]++;
        
        // 检查是否应该跳过重复失败的操作
        if (this.executionManager.isRepeatedFailure(method, agentName)) {
            logger.warn(`⚠️ 跳过重复失败的操作: ${method} (${agentName})`);
            throw new Error(`操作重复失败: ${method}`);
        }
        
        // 使用执行管理器执行操作（带超时控制）
        try {
            const result = await this.executionManager.executeOperation(
                method,
                agentName,
                async () => agent[method](...args),
                {
                    timeout: this.getOperationTimeout(agentName, method),
                    allowRetry: this.shouldAllowRetry(agentName, method)
                }
            );
            
            return result;
        } catch (error) {
            // 对于超时错误，尝试快速恢复
            if (error.name === 'TimeoutError') {
                logger.warn(`处理超时错误: ${agentName}.${method}`);
                // 对于某些Agent，超时可能意味着需要调整策略
                if (agentName === 'validator' && method === 'validateOperation') {
                    return {
                        validation: { passed: false },
                        error: 'TIMEOUT',
                        recommendation: { nextAction: 'RETRY_WITH_DIFFERENT_APPROACH' }
                    };
                }
            }
            throw error;
        }
    }
    
    /**
     * 获取操作超时时间
     */
    getOperationTimeout(agentName, method) {
        // 根据不同的Agent和方法设置不同的超时时间
        const timeoutMap = {
            'orchestrator': {
                'analyzeWorkOrder': 10000,      // 10秒
                'coordinateNextStep': 5000,     // 5秒
                'evaluateCompletion': 5000      // 5秒
            },
            'observer': {
                'observePage': 8000              // 8秒
            },
            'decision': {
                'makeDecision': 10000,           // 10秒
                'makeRecoveryDecision': 5000     // 5秒
            },
            'executor': {
                'executeAction': 30000           // 30秒（实际操作可能较慢）
            },
            'validator': {
                'validateOperation': 8000,       // 8秒
                'validateTaskCompletion': 10000  // 10秒
            }
        };
        
        return timeoutMap[agentName]?.[method] || 15000; // 默认15秒
    }
    
    /**
     * 是否允许重试
     */
    shouldAllowRetry(agentName, method) {
        // 某些关键操作不应该自动重试
        const noRetryList = [
            { agent: 'executor', method: 'executeAction' }, // 执行操作失败不应自动重试
            { agent: 'orchestrator', method: 'analyzeWorkOrder' } // 工单分析失败不重试
        ];
        
        return !noRetryList.some(item => 
            item.agent === agentName && item.method === method
        );
    }

    /**
     * 生成执行摘要
     */
    generateExecutionSummary() {
        return {
            totalSteps: this.context.executionHistory.length,
            successfulSteps: this.context.executionHistory.filter(
                h => h.validation?.validation?.passed
            ).length,
            recentSteps: this.context.executionHistory.slice(-5).map(h => ({
                action: h.decision?.action?.type,
                result: h.validation?.validation?.passed ? 'success' : 'failed',
                timestamp: h.timestamp
            }))
        };
    }

    /**
     * 生成最终报告
     */
    async generateReport(result) {
        const duration = Date.now() - this.context.startTime;
        
        return {
            workOrderId: this.context.workOrder.id,
            success: result.success,
            taskGoal: this.context.taskGoal,
            executionSummary: {
                totalIterations: this.stats.totalIterations,
                successfulOperations: this.stats.successfulOperations,
                failedOperations: this.stats.failedOperations,
                duration: duration,
                agentCalls: this.stats.agentCalls
            },
            finalState: this.context.currentState,
            needsHumanHelp: result.needsHumanHelp,
            completionEvidence: result.finalEvaluation?.completionEvidence || [],
            executionHistory: this.context.executionHistory,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 重置协调器状态
     */
    reset() {
        this.context = {
            workOrder: null,
            taskGoal: null,
            executionHistory: [],
            currentState: null,
            startTime: null
        };
        
        this.stats = {
            totalIterations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            agentCalls: {}
        };
        
        // 重置验证循环检测器
        this.validationCycleDetector = {
            consecutiveValidations: 0,
            maxConsecutiveValidations: 3,
            lastValidationPassed: true
        };
        
        // 清空所有Agent的历史
        [this.orchestrator, this.observer, this.decision, this.executor, this.validator]
            .forEach(agent => agent.clearHistory());
    }
}

module.exports = AgentCoordinator;