/**
 * AI驱动的任务分析器
 * 使用AI来理解任务内容，而不是硬编码规则
 */

const axios = require('axios');
const logger = require('../utils/logger');

class AITaskAnalyzer {
    constructor() {
        this.baseUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1';
        this.model = 'qwen-plus';
        this.apiKey = process.env.DASHSCOPE_API_KEY;
        this.systemGuide = null;
    }

    /**
     * 加载系统指南
     */
    async loadSystemGuide() {
        try {
            const fs = require('fs').promises;
            const path = require('path');
            const guidePath = path.join(__dirname, '../../../../system_guide.md');
            this.systemGuide = await fs.readFile(guidePath, 'utf-8');
            logger.info('✅ AI任务分析器系统指南加载成功');
        } catch (error) {
            logger.error('❌ AI任务分析器系统指南加载失败:', error);
            this.systemGuide = '';
        }
    }

    /**
     * AI驱动的任务分析
     * @param {string} taskContent - 任务内容
     * @param {Object} context - 上下文信息
     * @returns {Object} 任务分析结果
     */
    async analyzeTask(taskContent, context = {}) {
        try {
            // 确保系统指南已加载
            if (!this.systemGuide) {
                await this.loadSystemGuide();
            }

            const analysisPrompt = this.buildAnalysisPrompt(taskContent, context);
            
            const response = await axios.post(`${this.baseUrl}/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的RPA任务分析专家。请分析用户的任务需求，返回结构化的JSON分析结果。'
                    },
                    {
                        role: 'user',
                        content: analysisPrompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 1000
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            const aiResponse = response.data.choices[0].message.content;
            const analysis = this.parseAnalysisResult(aiResponse);
            
            logger.info('🧠 AI任务分析完成:', analysis);
            return analysis;

        } catch (error) {
            logger.error('❌ AI任务分析失败:', error.message);
            return this.getFallbackAnalysis(taskContent);
        }
    }

    /**
     * 构建任务分析提示词
     */
    buildAnalysisPrompt(taskContent, context) {
        return `
请分析以下RPA自动化任务，返回JSON格式的分析结果：

任务内容：${taskContent}

系统操作指南：
${this.systemGuide || '暂无系统指南'}

上下文信息：${JSON.stringify(context, null, 2)}

请返回以下格式的JSON分析：
{
    "taskType": "任务类型(web_automation/data_entry/file_processing/api_testing/general)",
    "targetWebsite": "推测的目标网站URL(如果是web任务)",
    "keyEntities": ["关键实体1", "关键实体2"],
    "actionVerbs": ["主要动作1", "主要动作2"],
    "completionCriteria": ["完成标准1", "完成标准2"],
    "estimatedSteps": 预估步骤数,
    "complexityLevel": "复杂度(low/medium/high)",
    "requiresLogin": true/false,
    "requiresUserInput": true/false,
    "riskLevel": "风险等级(low/medium/high)",
    "suggestedTimeout": 预估超时时间(秒),
    "dependencies": ["依赖项1", "依赖项2"]
}

注意：
1. 请仔细阅读系统操作指南，从中提取正确的URL地址和操作流程
2. 如果任务涉及BD商户后台，请使用系统指南中的正确地址
3. 基于系统指南中的步骤来估算任务复杂度和步骤数
4. 请基于任务内容和系统指南进行智能推理
5. 不要硬编码特定的业务逻辑
6. 返回纯JSON格式，不要包含其他文本
`;
    }

    /**
     * 解析AI分析结果
     */
    parseAnalysisResult(aiResponse) {
        try {
            // 提取JSON部分
            const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            // 如果没有找到JSON，尝试直接解析
            return JSON.parse(aiResponse);
        } catch (error) {
            logger.warn('⚠️ AI分析结果解析失败，使用默认分析');
            return this.getFallbackAnalysis(aiResponse);
        }
    }

    /**
     * 获取回退分析结果
     */
    getFallbackAnalysis(taskContent) {
        return {
            taskType: 'general',
            targetWebsite: null,
            keyEntities: [taskContent.substring(0, 50)],
            actionVerbs: ['执行'],
            completionCriteria: ['任务执行完成'],
            estimatedSteps: 5,
            complexityLevel: 'medium',
            requiresLogin: false,
            requiresUserInput: false,
            riskLevel: 'low',
            suggestedTimeout: 300,
            dependencies: []
        };
    }

    /**
     * AI驱动的完成度验证
     * @param {string} aiResponse - AI的响应内容
     * @param {Object} originalTask - 原始任务信息
     * @param {Array} executionHistory - 执行历史
     * @returns {Object} 验证结果
     */
    async validateCompletion(aiResponse, originalTask, executionHistory = []) {
        try {
            const validationPrompt = this.buildValidationPrompt(aiResponse, originalTask, executionHistory);
            
            const response = await axios.post(`${this.baseUrl}/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的RPA任务验证专家。请客观分析任务是否真正完成，不要被表面的回复误导。'
                    },
                    {
                        role: 'user',
                        content: validationPrompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 800
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            const validationResponse = response.data.choices[0].message.content;
            const validation = this.parseValidationResult(validationResponse);
            
            logger.info('🔍 AI完成度验证结果:', validation);
            return validation;

        } catch (error) {
            logger.error('❌ AI完成度验证失败:', error.message);
            return this.getFallbackValidation(aiResponse);
        }
    }

    /**
     * 构建验证提示词
     */
    buildValidationPrompt(aiResponse, originalTask, executionHistory) {
        return `
请验证RPA任务是否真正完成：

原始任务：${originalTask.content}
任务目标：${originalTask.title}

AI最新回复：${aiResponse}

执行历史摘要：
${executionHistory.slice(-5).map((step, index) => 
    `步骤${index + 1}: ${step.action} - ${step.result}`
).join('\n')}

请返回JSON格式的验证结果：
{
    "isCompleted": true/false,
    "confidence": 0.0-1.0,
    "reasoning": "判断理由",
    "evidence": ["支持证据1", "支持证据2"],
    "missingSteps": ["缺失步骤1", "缺失步骤2"],
    "nextAction": "建议的下一步行动",
    "requiresUserIntervention": true/false,
    "interventionType": "需要的用户干预类型(login/input/confirmation/none)"
}

验证标准：
1. 任务是否达到了预期目标
2. 是否有实际的操作执行
3. 是否只是在等待或提示，而没有真正完成
4. 是否需要用户干预才能继续

注意：不要被"任务完成"等表面词汇误导，要看实际执行结果。
`;
    }

    /**
     * 解析验证结果
     */
    parseValidationResult(validationResponse) {
        try {
            const jsonMatch = validationResponse.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            return JSON.parse(validationResponse);
        } catch (error) {
            logger.warn('⚠️ 验证结果解析失败，使用保守验证');
            return this.getFallbackValidation(validationResponse);
        }
    }

    /**
     * 获取回退验证结果
     */
    getFallbackValidation(aiResponse) {
        // 保守策略：如果解析失败，假设任务未完成
        return {
            isCompleted: false,
            confidence: 0.3,
            reasoning: '无法准确验证任务完成状态',
            evidence: [],
            missingSteps: ['需要进一步验证'],
            nextAction: '继续执行任务',
            requiresUserIntervention: false,
            interventionType: 'none'
        };
    }

    /**
     * 检测用户干预需求
     * @param {string} aiResponse - AI响应
     * @param {Object} context - 上下文
     * @returns {Object} 干预检测结果
     */
    async detectUserIntervention(aiResponse, context = {}) {
        try {
            const detectionPrompt = `
分析AI回复是否需要用户干预：

AI回复：${aiResponse}
上下文：${JSON.stringify(context)}

返回JSON格式：
{
    "requiresIntervention": true/false,
    "interventionType": "login/input/confirmation/manual_action/none",
    "urgency": "low/medium/high",
    "description": "需要用户做什么",
    "suggestedAction": "建议的用户操作",
    "canWait": true/false,
    "maxWaitTime": 等待时间(秒)
}
`;

            const response = await axios.post(`${this.baseUrl}/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: '你是用户干预检测专家，能准确识别何时需要用户介入。'
                    },
                    {
                        role: 'user',
                        content: detectionPrompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 500
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            const detectionResponse = response.data.choices[0].message.content;
            const detection = this.parseValidationResult(detectionResponse);
            
            logger.info('🚨 用户干预检测结果:', detection);
            return detection;

        } catch (error) {
            logger.error('❌ 用户干预检测失败:', error.message);
            return {
                requiresIntervention: false,
                interventionType: 'none',
                urgency: 'low',
                description: '检测失败',
                suggestedAction: '继续执行',
                canWait: true,
                maxWaitTime: 60
            };
        }
    }
}

module.exports = { AITaskAnalyzer };
