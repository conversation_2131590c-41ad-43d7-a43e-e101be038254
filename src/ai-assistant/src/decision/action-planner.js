/**
 * 动作规划器 - AI-First RPA架构的决策层
 * 负责基于页面状态制定操作策略，动态选择最佳操作方式
 */

class ActionPlanner {
    constructor(modelClient, logger = console) {
        this.modelClient = modelClient;
        this.logger = logger;
        this.actionHistory = [];
        this.systemGuide = null;
    }

    /**
     * 设置系统指南
     */
    setSystemGuide(systemGuide) {
        this.systemGuide = systemGuide;
    }

    /**
     * 规划下一步操作
     */
    async planNextAction(pageAnalysis, executionContext) {
        try {
            this.logger.info('🧠 AI正在规划下一步操作...');

            // 构建AI提示词
            const prompt = this.buildPlanningPrompt(pageAnalysis, executionContext);
            
            // 调用AI模型获取决策
            const response = await this.modelClient.callMainModel(prompt);
            
            // 解析AI响应
            const action = this.parseActionResponse(response);
            
            // 验证和优化操作
            const optimizedAction = this.optimizeAction(action, pageAnalysis);
            
            // 记录操作历史
            this.actionHistory.push({
                pageAnalysis,
                plannedAction: optimizedAction,
                timestamp: new Date().toISOString()
            });

            this.logger.info(`✅ 规划完成: ${optimizedAction.action_type} - ${optimizedAction.reasoning}`);
            
            return optimizedAction;
        } catch (error) {
            this.logger.error('❌ 操作规划失败:', error);
            return this.getFallbackAction(pageAnalysis, executionContext);
        }
    }

    /**
     * 构建AI规划提示词
     */
    buildPlanningPrompt(pageAnalysis, executionContext) {
        const prompt = `
你是一个专业的RPA自动化执行助手，需要根据当前页面状态和任务上下文，规划下一步最佳操作。

## 任务信息
任务描述: ${executionContext.taskDescription}
当前目标: ${executionContext.currentObjective}

## 页面状态分析
URL: ${pageAnalysis.url}
标题: ${pageAnalysis.title}
页面摘要: ${pageAnalysis.summary}

## 可用的交互元素
${this.formatInteractiveElements(pageAnalysis.interactive)}

## 已完成的步骤
${this.formatCompletedSteps(executionContext.completedSteps)}

## 遇到的问题
${this.formatIssues(executionContext.encounteredIssues)}

## 系统操作指南
${this.systemGuide || '暂无系统指南'}

## 规划要求
请根据以上信息，规划下一步最佳操作。考虑以下因素：
1. 任务的最终目标
2. 当前页面的状态和可用元素
3. 已完成的步骤，避免重复操作
4. 系统操作指南的要求
5. 操作的成功概率和风险

请返回JSON格式的操作计划：
{
    "action_type": "navigate|click|type|wait|screenshot|manual_intervention",
    "target_element": {
        "selector": "元素选择器或描述",
        "description": "元素的详细描述",
        "confidence": 0.8
    },
    "action_data": {
        "url": "导航URL（仅navigate操作）",
        "text": "输入文本（仅type操作）",
        "timeout": "等待时间毫秒（仅wait操作）",
        "screenshot_description": "截图描述（仅screenshot操作）"
    },
    "reasoning": "选择此操作的详细理由",
    "expected_outcome": "执行此操作后的预期结果",
    "success_criteria": "判断操作成功的标准",
    "risk_level": "low|medium|high",
    "estimated_duration": "预估执行时间（秒）",
    "fallback_strategy": "如果操作失败的备选方案"
}

注意：
- 如果需要用户手动干预（如登录），使用 manual_intervention 操作类型
- 优先选择成功率高、风险低的操作
- 确保操作符合系统指南的要求
- 如果页面还在加载，选择 wait 操作
`;

        return prompt;
    }

    /**
     * 格式化交互元素信息
     */
    formatInteractiveElements(elements) {
        if (!elements || elements.length === 0) {
            return '暂无可交互元素';
        }

        return elements.slice(0, 10).map((element, index) => {
            let description = `${index + 1}. ${element.type}`;
            
            if (element.text) {
                description += ` - 文本: "${element.text}"`;
            }
            
            if (element.placeholder) {
                description += ` - 占位符: "${element.placeholder}"`;
            }
            
            if (element.href) {
                description += ` - 链接: ${element.href}`;
            }
            
            if (element.selector) {
                description += ` - 选择器: ${element.selector}`;
            }
            
            return description;
        }).join('\n');
    }

    /**
     * 格式化已完成步骤
     */
    formatCompletedSteps(steps) {
        if (!steps || steps.length === 0) {
            return '暂无已完成步骤';
        }

        return steps.slice(-5).map((step, index) => {
            const action = step.action;
            const result = step.result;
            const status = result.success ? '✅' : '❌';
            
            return `${steps.length - 4 + index}. ${status} ${action.action_type} - ${action.reasoning} (${result.message})`;
        }).join('\n');
    }

    /**
     * 格式化问题信息
     */
    formatIssues(issues) {
        if (!issues || issues.length === 0) {
            return '暂无问题';
        }

        return issues.slice(-3).map((issue, index) => {
            return `${index + 1}. ${issue.error} (${issue.timestamp})`;
        }).join('\n');
    }

    /**
     * 解析AI响应
     */
    parseActionResponse(response) {
        try {
            // 尝试解析JSON响应
            const action = JSON.parse(response);
            
            // 验证必需字段
            if (!action.action_type) {
                throw new Error('缺少action_type字段');
            }
            
            // 设置默认值
            action.target_element = action.target_element || {};
            action.action_data = action.action_data || {};
            action.reasoning = action.reasoning || '未提供理由';
            action.expected_outcome = action.expected_outcome || '未知结果';
            action.confidence = action.target_element.confidence || 0.7;
            action.risk_level = action.risk_level || 'medium';
            action.estimated_duration = action.estimated_duration || 5;
            
            return action;
        } catch (error) {
            this.logger.error('❌ 解析AI响应失败:', error);
            this.logger.error('原始响应:', response);
            
            // 返回默认的等待操作
            return {
                action_type: 'wait',
                target_element: {
                    selector: 'body',
                    description: '页面主体',
                    confidence: 0.5
                },
                action_data: {
                    timeout: 3000
                },
                reasoning: 'AI响应解析失败，执行等待操作',
                expected_outcome: '等待页面稳定',
                confidence: 0.5,
                risk_level: 'low',
                estimated_duration: 3
            };
        }
    }

    /**
     * 优化操作
     */
    optimizeAction(action, pageAnalysis) {
        try {
            // 优化选择器
            if (action.target_element && action.target_element.selector) {
                action.target_element.selector = this.optimizeSelector(
                    action.target_element.selector, 
                    pageAnalysis.interactive
                );
            }

            // 优化等待时间
            if (action.action_type === 'wait' && action.action_data.timeout) {
                action.action_data.timeout = Math.min(
                    Math.max(action.action_data.timeout, 1000), // 最少1秒
                    10000 // 最多10秒
                );
            }

            // 优化导航URL
            if (action.action_type === 'navigate' && action.action_data.url) {
                action.action_data.url = this.normalizeUrl(action.action_data.url);
            }

            return action;
        } catch (error) {
            this.logger.error('❌ 操作优化失败:', error);
            return action;
        }
    }

    /**
     * 优化选择器
     */
    optimizeSelector(selector, interactiveElements) {
        try {
            // 如果选择器是文本描述，尝试找到匹配的元素
            if (!selector.startsWith('#') && !selector.startsWith('.') && !selector.startsWith('[')) {
                const matchingElement = interactiveElements.find(element => 
                    element.text && element.text.toLowerCase().includes(selector.toLowerCase())
                );
                
                if (matchingElement && matchingElement.selector) {
                    return matchingElement.selector;
                }
            }

            return selector;
        } catch (error) {
            return selector;
        }
    }

    /**
     * 标准化URL
     */
    normalizeUrl(url) {
        try {
            // 如果是相对URL，保持原样
            if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
                return url;
            }
            
            // 如果没有协议，添加https
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                return `https://${url}`;
            }
            
            return url;
        } catch (error) {
            return url;
        }
    }

    /**
     * 获取后备操作
     */
    getFallbackAction(pageAnalysis, executionContext) {
        this.logger.warn('⚠️ 使用后备操作策略');

        // 如果页面正在加载，等待
        if (pageAnalysis.url === 'about:blank' || !pageAnalysis.title) {
            return {
                action_type: 'wait',
                target_element: {
                    selector: 'body',
                    description: '等待页面加载',
                    confidence: 0.8
                },
                action_data: {
                    timeout: 5000
                },
                reasoning: '页面似乎正在加载，等待页面稳定',
                expected_outcome: '页面加载完成',
                confidence: 0.8,
                risk_level: 'low',
                estimated_duration: 5
            };
        }

        // 如果有可点击的按钮，尝试点击第一个
        const buttons = pageAnalysis.interactive?.filter(el => el.type === 'button') || [];
        if (buttons.length > 0) {
            const button = buttons[0];
            return {
                action_type: 'click',
                target_element: {
                    selector: button.selector,
                    description: button.text || '按钮',
                    confidence: 0.6
                },
                action_data: {},
                reasoning: '尝试点击页面上的第一个按钮',
                expected_outcome: '触发按钮相关的操作',
                confidence: 0.6,
                risk_level: 'medium',
                estimated_duration: 3
            };
        }

        // 默认等待操作
        return {
            action_type: 'wait',
            target_element: {
                selector: 'body',
                description: '页面主体',
                confidence: 0.5
            },
            action_data: {
                timeout: 3000
            },
            reasoning: '无法确定最佳操作，执行等待',
            expected_outcome: '等待页面状态变化',
            confidence: 0.5,
            risk_level: 'low',
            estimated_duration: 3
        };
    }

    /**
     * 获取操作历史
     */
    getActionHistory() {
        return this.actionHistory;
    }

    /**
     * 清除操作历史
     */
    clearActionHistory() {
        this.actionHistory = [];
    }

    /**
     * 分析操作模式
     */
    analyzeActionPatterns() {
        if (this.actionHistory.length < 3) {
            return null;
        }

        const recentActions = this.actionHistory.slice(-5);
        const actionTypes = recentActions.map(h => h.plannedAction.action_type);
        
        // 检测循环模式
        const isRepeating = actionTypes.every(type => type === actionTypes[0]);
        
        return {
            recentActionTypes: actionTypes,
            isRepeating,
            totalActions: this.actionHistory.length,
            analysis: isRepeating ? '检测到重复操作模式' : '操作模式正常'
        };
    }
}

module.exports = ActionPlanner;
