/**
 * LLM客户端工厂 - 智能选择真实API或Mock客户端
 * 支持自动降级和环境适配
 */

const SimpleLLMClient = require('./simple-llm-client');
const MockLLMClient = require('./mock-llm-client');
const logger = require('../utils/logger');

class LLMClientFactory {
    constructor() {
        this.realClient = null;
        this.mockClient = null;
        this.currentClient = null;
        this.useMock = false;
        this.autoFallback = true;
        this.healthCheckInterval = 60000; // 1分钟
        this.lastHealthCheck = 0;
    }

    /**
     * 创建LLM客户端实例
     */
    async createClient(options = {}) {
        const {
            forceMock = false,
            autoFallback = true,
            enableHealthCheck = true
        } = options;

        this.autoFallback = autoFallback;

        // 如果强制使用Mock
        if (forceMock || process.env.NODE_ENV === 'test') {
            logger.info('🤖 使用Mock LLM客户端 (强制模式)');
            this.mockClient = new MockLLMClient();
            this.currentClient = this.mockClient;
            this.useMock = true;
            return this.currentClient;
        }

        // 尝试创建真实客户端
        try {
            this.realClient = new SimpleLLMClient();
            
            // 检查真实客户端是否可用
            if (enableHealthCheck) {
                const isHealthy = await this.checkRealClientHealth();
                if (!isHealthy) {
                    throw new Error('真实LLM客户端健康检查失败');
                }
            }

            logger.info('✅ 使用真实LLM客户端');
            this.currentClient = this.realClient;
            this.useMock = false;

        } catch (error) {
            logger.warn(`⚠️ 真实LLM客户端不可用: ${error.message}`);
            
            if (this.autoFallback) {
                logger.info('🔄 自动降级到Mock LLM客户端');
                this.mockClient = new MockLLMClient();
                this.currentClient = this.mockClient;
                this.useMock = true;
            } else {
                throw error;
            }
        }

        // 启动定期健康检查
        if (enableHealthCheck && this.autoFallback) {
            this.startHealthCheck();
        }

        return this.currentClient;
    }

    /**
     * 获取当前客户端
     */
    getCurrentClient() {
        if (!this.currentClient) {
            throw new Error('LLM客户端未初始化，请先调用createClient()');
        }
        return this.currentClient;
    }

    /**
     * 检查真实客户端健康状态
     */
    async checkRealClientHealth() {
        try {
            if (!this.realClient) {
                this.realClient = new SimpleLLMClient();
            }

            // 发送简单的测试请求
            const response = await this.realClient.chat('健康检查');
            const isHealthy = response && response.length > 0 && !response.includes('LLM服务不可用');
            
            logger.debug(`🏥 真实LLM客户端健康检查: ${isHealthy ? '健康' : '异常'}`);
            return isHealthy;

        } catch (error) {
            logger.debug(`🏥 真实LLM客户端健康检查失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 启动定期健康检查
     */
    startHealthCheck() {
        setInterval(async () => {
            const now = Date.now();
            if (now - this.lastHealthCheck < this.healthCheckInterval) {
                return;
            }

            this.lastHealthCheck = now;

            // 如果当前使用Mock，尝试恢复到真实客户端
            if (this.useMock) {
                const isRealHealthy = await this.checkRealClientHealth();
                if (isRealHealthy) {
                    logger.info('🔄 真实LLM客户端已恢复，切换回真实客户端');
                    this.currentClient = this.realClient;
                    this.useMock = false;
                }
            } else {
                // 如果当前使用真实客户端，检查是否仍然健康
                const isRealHealthy = await this.checkRealClientHealth();
                if (!isRealHealthy) {
                    logger.warn('⚠️ 真实LLM客户端异常，切换到Mock客户端');
                    if (!this.mockClient) {
                        this.mockClient = new MockLLMClient();
                    }
                    this.currentClient = this.mockClient;
                    this.useMock = true;
                }
            }
        }, this.healthCheckInterval);

        logger.info(`🏥 启动LLM客户端健康检查 (间隔: ${this.healthCheckInterval}ms)`);
    }

    /**
     * 手动切换到Mock客户端
     */
    switchToMock() {
        if (!this.mockClient) {
            this.mockClient = new MockLLMClient();
        }
        this.currentClient = this.mockClient;
        this.useMock = true;
        logger.info('🔄 手动切换到Mock LLM客户端');
    }

    /**
     * 手动切换到真实客户端
     */
    async switchToReal() {
        if (!this.realClient) {
            this.realClient = new SimpleLLMClient();
        }

        const isHealthy = await this.checkRealClientHealth();
        if (!isHealthy) {
            throw new Error('真实LLM客户端不可用，无法切换');
        }

        this.currentClient = this.realClient;
        this.useMock = false;
        logger.info('🔄 手动切换到真实LLM客户端');
    }

    /**
     * 获取客户端状态信息
     */
    getStatus() {
        return {
            currentType: this.useMock ? 'mock' : 'real',
            autoFallback: this.autoFallback,
            lastHealthCheck: this.lastHealthCheck,
            stats: this.currentClient ? this.currentClient.getStats() : null
        };
    }

    /**
     * 设置健康检查间隔
     */
    setHealthCheckInterval(ms) {
        this.healthCheckInterval = ms;
        logger.info(`🔧 设置健康检查间隔: ${ms}ms`);
    }

    /**
     * 代理方法 - chat
     */
    async chat(prompt) {
        const client = this.getCurrentClient();
        return await client.chat(prompt);
    }

    /**
     * 代理方法 - chatWithMessages
     */
    async chatWithMessages(messages) {
        const client = this.getCurrentClient();
        return await client.chatWithMessages(messages);
    }

    /**
     * 代理方法 - checkHealth
     */
    async checkHealth() {
        const client = this.getCurrentClient();
        return await client.checkHealth();
    }

    /**
     * 代理方法 - getModel
     */
    getModel() {
        const client = this.getCurrentClient();
        return client.getModel();
    }

    /**
     * 代理方法 - setModel
     */
    setModel(model) {
        const client = this.getCurrentClient();
        return client.setModel(model);
    }
}

// 创建单例实例
const llmClientFactory = new LLMClientFactory();

module.exports = {
    LLMClientFactory,
    llmClientFactory
};
