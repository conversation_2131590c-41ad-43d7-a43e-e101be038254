/**
 * CheckerAgent - PDCA架构中的检查器
 * 职责：验证执行结果，决定下一步行动
 */

const logger = require('../utils/logger');

class CheckerAgent {
    constructor(llmClient) {
        this.llmClient = llmClient;
        this.checkingId = 0;
    }

    /**
     * 检查执行结果并决定下一步行动
     * @param {Object} plan - 执行计划
     * @param {Object} evidence - 执行证据
     * @param {Object} context - 执行上下文
     * @returns {Object} 检查结果
     */
    async check(plan, evidence, context) {
        this.checkingId++;
        
        logger.info(`🔍 CheckerAgent开始检查`, {
            checkingId: this.checkingId,
            intent: plan.intent,
            executionSuccess: evidence.success
        });

        try {
            // 首先进行基础检查
            const basicCheck = this.performBasicCheck(plan, evidence);
            
            // 如果基础检查失败，直接返回
            if (basicCheck.status === 'FAILURE') {
                return basicCheck;
            }
            
            // 进行深度检查（使用LLM）
            const deepCheck = await this.performDeepCheck(plan, evidence, context);
            
            // 合并检查结果
            const finalResult = this.combineCheckResults(basicCheck, deepCheck);
            
            logger.info(`✅ 检查完成: ${finalResult.status}`, {
                checkingId: this.checkingId,
                confidence: finalResult.confidence,
                reason: finalResult.reason
            });
            
            return finalResult;
            
        } catch (error) {
            logger.error(`❌ 检查失败:`, error);
            
            return {
                status: 'FAILURE',
                reason: `检查过程出错: ${error.message}`,
                confidence: 0.0,
                nextAction: 'RETRY_OR_ESCALATE',
                suggestions: '检查器本身出现问题，建议重试或请求人工干预'
            };
        }
    }

    /**
     * 执行基础检查（不依赖LLM的快速检查）
     */
    performBasicCheck(plan, evidence) {
        // 检查执行是否成功
        if (!evidence.success) {
            return {
                status: 'FAILURE',
                reason: `执行失败: ${evidence.error?.message || '未知错误'}`,
                confidence: 0.9,
                nextAction: 'RETRY_OR_ADJUST',
                suggestions: this.generateFailureSuggestions(evidence.error),
                checkType: 'basic'
            };
        }
        
        // 检查执行时间是否合理
        if (evidence.executionTime > 120000) { // 超过2分钟
            return {
                status: 'NEEDS_ATTENTION',
                reason: `执行时间过长: ${evidence.executionTime}ms`,
                confidence: 0.7,
                nextAction: 'CONTINUE_WITH_CAUTION',
                suggestions: '执行时间异常，可能存在性能问题',
                checkType: 'basic'
            };
        }
        
        // 检查状态变化
        const stateChanged = this.detectStateChange(evidence.beforeState, evidence.afterState);
        if (!stateChanged) {
            return {
                status: 'NEEDS_VERIFICATION',
                reason: '页面状态未发生明显变化',
                confidence: 0.6,
                nextAction: 'VERIFY_WITH_LLM',
                suggestions: '需要进一步验证操作是否真正生效',
                checkType: 'basic'
            };
        }
        
        // 基础检查通过
        return {
            status: 'BASIC_SUCCESS',
            reason: '基础检查通过',
            confidence: 0.7,
            nextAction: 'PROCEED_TO_DEEP_CHECK',
            checkType: 'basic'
        };
    }

    /**
     * 执行深度检查（使用LLM进行智能分析）
     */
    async performDeepCheck(plan, evidence, context) {
        const prompt = this.buildCheckingPrompt(plan, evidence, context);
        
        try {
            const response = await this.llmClient.chat(prompt);
            return this.parseCheckingResponse(response);
        } catch (error) {
            logger.warn('LLM深度检查失败，使用启发式检查:', error.message);
            return this.performHeuristicCheck(plan, evidence, context);
        }
    }

    /**
     * 构建检查提示词
     */
    buildCheckingPrompt(plan, evidence, context) {
        const beforeState = this.formatState(evidence.beforeState);
        const afterState = this.formatState(evidence.afterState);
        const executionResult = JSON.stringify(evidence.executionResult, null, 2);
        
        return `你是一个RPA系统的智能检查器，负责验证执行结果并决定下一步行动。

🎯 **任务目标**: ${context.task_goal}

📋 **执行计划**:
- 意图: ${plan.intent}
- 参数: ${JSON.stringify(plan.parameters, null, 2)}
- 预期结果: ${plan.expectedOutcome || '未指定'}

🔧 **执行结果**:
${executionResult}

📸 **状态对比**:
**执行前**:
${beforeState}

**执行后**:
${afterState}

📊 **检查要求**:
1. 分析执行是否达到预期目标
2. 检查页面状态变化是否符合意图
3. 评估执行质量和完整性
4. 判断是否需要继续、调整或求助

请以JSON格式返回检查结果:
{
    "status": "SUCCESS|PARTIAL_SUCCESS|FAILURE|NEEDS_ADJUSTMENT",
    "reason": "详细的检查结果说明",
    "confidence": 0.85,
    "nextAction": "CONTINUE|RETRY|ADJUST_STRATEGY|REQUEST_USER_HELP|TASK_COMPLETED",
    "suggestions": "给规划器的具体建议",
    "evidence": {
        "positive": ["支持成功的证据"],
        "negative": ["表明问题的证据"],
        "neutral": ["中性观察"]
    }
}`;
    }

    /**
     * 解析检查响应
     */
    parseCheckingResponse(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('响应中未找到JSON格式的检查结果');
            }
            
            const result = JSON.parse(jsonMatch[0]);
            
            // 验证必需字段
            result.status = result.status || 'NEEDS_VERIFICATION';
            result.reason = result.reason || '未提供检查原因';
            result.confidence = result.confidence || 0.5;
            result.nextAction = result.nextAction || 'CONTINUE';
            result.suggestions = result.suggestions || '无特殊建议';
            result.checkType = 'deep';
            
            return result;
            
        } catch (error) {
            logger.error('解析检查响应失败:', error);
            
            return {
                status: 'NEEDS_VERIFICATION',
                reason: `解析检查结果失败: ${error.message}`,
                confidence: 0.3,
                nextAction: 'CONTINUE',
                suggestions: '检查器解析失败，建议继续执行并观察',
                checkType: 'deep'
            };
        }
    }

    /**
     * 启发式检查（当LLM不可用时的备用方案）
     */
    performHeuristicCheck(plan, evidence, context) {
        const intent = plan.intent;
        const afterState = evidence.afterState;
        
        // 根据意图类型进行启发式检查
        switch (intent) {
            case 'NAVIGATION.NAVIGATE_TO_PAGE':
                return this.checkNavigation(plan, evidence);
                
            case 'AUTHENTICATION.LOGIN':
                return this.checkLogin(plan, evidence);
                
            case 'AUTHENTICATION.WAIT_FOR_USER_LOGIN':
                return this.checkUserLogin(plan, evidence);
                
            case 'NAVIGATION.SELECT_STORE':
                return this.checkStoreSelection(plan, evidence);
                
            case 'PRODUCT_OPERATIONS.FIND_PRODUCT':
                return this.checkProductSearch(plan, evidence);
                
            case 'PRODUCT_OPERATIONS.DELIST_PRODUCT':
                return this.checkProductDelist(plan, evidence);
                
            default:
                return {
                    status: 'PARTIAL_SUCCESS',
                    reason: '使用启发式检查，无法完全验证结果',
                    confidence: 0.6,
                    nextAction: 'CONTINUE',
                    suggestions: '建议继续执行并观察后续结果',
                    checkType: 'heuristic'
                };
        }
    }

    /**
     * 检查导航操作
     */
    checkNavigation(plan, evidence) {
        const targetUrl = plan.parameters.url;
        const finalUrl = evidence.executionResult?.finalUrl;
        
        if (finalUrl && finalUrl.includes(targetUrl.split('/')[2])) {
            return {
                status: 'SUCCESS',
                reason: '成功导航到目标页面',
                confidence: 0.8,
                nextAction: 'CONTINUE'
            };
        }
        
        return {
            status: 'NEEDS_VERIFICATION',
            reason: '无法确认导航是否成功',
            confidence: 0.5,
            nextAction: 'CONTINUE'
        };
    }

    /**
     * 检查登录操作
     */
    checkLogin(plan, evidence) {
        const steps = evidence.executionResult?.steps || [];
        
        if (steps.includes('clicked_login_button')) {
            return {
                status: 'PARTIAL_SUCCESS',
                reason: '已执行登录操作，需要验证登录结果',
                confidence: 0.7,
                nextAction: 'CONTINUE'
            };
        }
        
        return {
            status: 'NEEDS_VERIFICATION',
            reason: '登录操作执行情况不明确',
            confidence: 0.5,
            nextAction: 'CONTINUE'
        };
    }

    /**
     * 辅助方法
     */
    detectStateChange(beforeState, afterState) {
        if (!beforeState || !afterState) return false;
        
        // 比较URL变化
        if (beforeState.url !== afterState.url) return true;
        
        // 比较DOM内容变化（简单比较）
        if (beforeState.domSnapshot && afterState.domSnapshot) {
            const sizeDiff = Math.abs(beforeState.domSnapshot.length - afterState.domSnapshot.length);
            return sizeDiff > 100; // 内容变化超过100字符
        }
        
        return false;
    }
    
    formatState(state) {
        if (!state) return '无状态信息';
        
        const parts = [];
        if (state.url) parts.push(`URL: ${state.url}`);
        if (state.timestamp) parts.push(`时间: ${new Date(state.timestamp).toLocaleString()}`);
        if (state.domSnapshot) {
            const snippet = state.domSnapshot.substring(0, 300);
            parts.push(`内容: ${snippet}${state.domSnapshot.length > 300 ? '...' : ''}`);
        }
        
        return parts.join('\n');
    }
    
    generateFailureSuggestions(error) {
        if (!error) return '无具体错误信息';
        
        const errorMessage = error.message?.toLowerCase() || '';
        
        if (errorMessage.includes('timeout')) {
            return '建议增加等待时间或检查网络连接';
        }
        
        if (errorMessage.includes('element not found')) {
            return '建议检查页面结构或使用备用选择器';
        }
        
        if (errorMessage.includes('network')) {
            return '建议检查网络连接或重试';
        }
        
        return '建议重试操作或请求用户干预';
    }
    
    combineCheckResults(basicCheck, deepCheck) {
        // 如果基础检查失败，直接返回
        if (basicCheck.status === 'FAILURE') {
            return basicCheck;
        }
        
        // 合并检查结果，深度检查优先
        return {
            ...deepCheck,
            basicCheck: basicCheck,
            combinedConfidence: (basicCheck.confidence + deepCheck.confidence) / 2
        };
    }
}

module.exports = CheckerAgent;
