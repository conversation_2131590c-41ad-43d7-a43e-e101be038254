/**
 * Mock LLM客户端 - 用于测试和开发
 * 当真实API不可用时提供智能的模拟响应
 */

const logger = require('../utils/logger');

class MockLLMClient {
    constructor() {
        this.model = 'mock-qwen-plus';
        this.requestCount = 0;
        this.responseDelay = 500; // 模拟网络延迟
        
        // ⚠️ AI-First原则：完全移除预定义响应模板
        // 这些模板违反了"测试AI智能而非验证预设输出"的核心原则

        // 新的AI-First方法：基于真实AI推理生成响应
        this.aiCapabilities = {
            understanding: true,    // 语义理解能力
            reasoning: true,        // 推理能力
            adaptation: true,       // 适应能力
            learning: true          // 学习能力
        };

        // 用于测试的基础AI行为模拟（非预定义响应）
        this.behaviorPatterns = {
            analysisDepth: 'comprehensive',     // 分析深度
            responseStyle: 'natural',           // 响应风格
            reasoningChain: 'explicit',         // 推理链展示
            adaptability: 'high'                // 适应性水平
        };
    }

    /**
     * 模拟聊天请求
     */
    async chat(prompt) {
        this.requestCount++;
        logger.info(`🤖 Mock LLM请求 #${this.requestCount}: ${prompt.substring(0, 50)}...`);
        
        // 模拟网络延迟
        await this.sleep(this.responseDelay);
        
        // 根据提示词内容生成智能响应
        const response = this.generateIntelligentResponse(prompt);
        
        logger.info(`✅ Mock LLM响应: ${JSON.stringify(response).substring(0, 100)}...`);
        return typeof response === 'string' ? response : JSON.stringify(response, null, 2);
    }

    /**
     * 模拟消息格式聊天请求
     */
    async chatWithMessages(messages) {
        const lastUserMessage = messages.filter(m => m.role === 'user').pop();
        return await this.chat(lastUserMessage?.content || '');
    }

    /**
     * 生成AI-First智能响应
     * 🎯 核心原则：模拟真实AI的推理过程，而非返回预设模板
     */
    generateIntelligentResponse(prompt) {
        logger.debug(`🧠 AI-First响应生成: ${prompt.substring(0, 50)}...`);

        // 分析提示词的意图和上下文
        const intentAnalysis = this.analyzePromptIntent(prompt);

        // 基于意图生成自然的AI响应
        return this.generateNaturalAIResponse(prompt, intentAnalysis);
    }

    /**
     * 分析提示词意图（模拟AI的理解过程）
     */
    analyzePromptIntent(prompt) {
        const analysis = {
            type: 'unknown',
            entities: [],
            context: {},
            complexity: 'medium',
            requiresReasoning: false
        };

        // 识别任务类型（基于语义而非模板匹配）
        if (prompt.includes('分析') || prompt.includes('理解') || prompt.includes('识别')) {
            analysis.type = 'semantic_analysis';
            analysis.requiresReasoning = true;
        } else if (prompt.includes('步骤') || prompt.includes('计划') || prompt.includes('如何')) {
            analysis.type = 'planning';
            analysis.requiresReasoning = true;
            analysis.complexity = 'high';
        } else if (prompt.includes('验证') || prompt.includes('检查') || prompt.includes('确认')) {
            analysis.type = 'validation';
        } else if (prompt.includes('观察') || prompt.includes('页面') || prompt.includes('元素')) {
            analysis.type = 'observation';
        }

        // 提取实体（动态识别，非预设列表）
        analysis.entities = this.extractEntitiesFromPrompt(prompt);

        // 分析上下文复杂度
        analysis.context = this.analyzeContextComplexity(prompt);

        return analysis;
    }

    /**
     * 生成自然的AI响应
     */
    generateNaturalAIResponse(prompt, intentAnalysis) {
        const response = {
            understanding: this.generateUnderstandingResponse(prompt, intentAnalysis),
            reasoning: this.generateReasoningChain(prompt, intentAnalysis),
            conclusion: this.generateConclusion(prompt, intentAnalysis),
            confidence: this.calculateConfidence(intentAnalysis),
            metadata: {
                requestId: `ai-first-${this.requestCount}`,
                timestamp: new Date().toISOString(),
                analysisType: intentAnalysis.type,
                complexity: intentAnalysis.complexity
            }
        };

        // 根据不同类型生成不同风格的响应
        switch (intentAnalysis.type) {
            case 'semantic_analysis':
                return this.formatSemanticAnalysisResponse(response, prompt, intentAnalysis);
            case 'planning':
                return this.formatPlanningResponse(response, prompt, intentAnalysis);
            case 'validation':
                return this.formatValidationResponse(response, prompt, intentAnalysis);
            case 'observation':
                return this.formatObservationResponse(response, prompt, intentAnalysis);
            default:
                return this.formatGeneralResponse(response, prompt, intentAnalysis);
        }
    }

    /**
     * 生成理解层面的响应
     */
    generateUnderstandingResponse(prompt, intentAnalysis) {
        const entities = intentAnalysis.entities;
        const understanding = [];

        if (entities.length > 0) {
            understanding.push(`我理解这个请求涉及以下关键要素：${entities.join('、')}`);
        }

        if (intentAnalysis.type === 'semantic_analysis') {
            understanding.push('这是一个需要语义分析的任务');
        } else if (intentAnalysis.type === 'planning') {
            understanding.push('这需要制定具体的执行计划');
        }

        return understanding.join('。');
    }

    /**
     * 生成推理链
     */
    generateReasoningChain(prompt, intentAnalysis) {
        if (!intentAnalysis.requiresReasoning) {
            return '基于直接分析得出结论';
        }

        const reasoning = [];
        reasoning.push('基于提供的信息进行分析');

        if (intentAnalysis.complexity === 'high') {
            reasoning.push('考虑多个相关因素和约束条件');
            reasoning.push('评估不同方案的可行性');
        }

        reasoning.push('综合各方面因素得出最优方案');

        return reasoning.join('，');
    }

    /**
     * 生成结论
     */
    generateConclusion(prompt, intentAnalysis) {
        return `基于分析，我认为这是一个${intentAnalysis.complexity}复杂度的${intentAnalysis.type}任务`;
    }

    /**
     * 计算置信度
     */
    calculateConfidence(intentAnalysis) {
        let baseConfidence = 0.8;

        if (intentAnalysis.entities.length > 2) {
            baseConfidence += 0.1; // 实体信息丰富
        }

        if (intentAnalysis.complexity === 'high') {
            baseConfidence -= 0.1; // 复杂任务降低置信度
        }

        return Math.min(0.95, Math.max(0.6, baseConfidence));
    }

    /**
     * 提取实体信息
     */
    extractEntitiesFromPrompt(prompt) {
        const entities = [];

        // 动态识别中文实体（而非预设列表）
        const chineseEntityPattern = /[\u4e00-\u9fa5]{2,}/g;
        const matches = prompt.match(chineseEntityPattern);

        if (matches) {
            // 过滤掉常见词汇，保留可能的实体
            const filteredEntities = matches.filter(match =>
                !['分析', '理解', '请求', '任务', '操作', '系统', '页面', '元素'].includes(match)
            );
            entities.push(...filteredEntities.slice(0, 5)); // 最多5个实体
        }

        return [...new Set(entities)]; // 去重
    }

    /**
     * 分析上下文复杂度
     */
    analyzeContextComplexity(prompt) {
        const context = {
            hasConstraints: false,
            hasMultipleSteps: false,
            hasBusinessContext: false,
            requiresDecision: false
        };

        if (prompt.includes('约束') || prompt.includes('限制') || prompt.includes('条件')) {
            context.hasConstraints = true;
        }

        if (prompt.includes('步骤') || prompt.includes('然后') || prompt.includes('接下来')) {
            context.hasMultipleSteps = true;
        }

        if (prompt.includes('商家') || prompt.includes('商品') || prompt.includes('业务')) {
            context.hasBusinessContext = true;
        }

        if (prompt.includes('如何') || prompt.includes('怎么') || prompt.includes('选择')) {
            context.requiresDecision = true;
        }

        return context;
    }

    /**
     * 格式化语义分析响应
     */
    formatSemanticAnalysisResponse(response, prompt, intentAnalysis) {
        return `${response.understanding}\n\n推理过程：${response.reasoning}\n\n结论：${response.conclusion}\n\n置信度：${(response.confidence * 100).toFixed(1)}%`;
    }

    /**
     * 格式化规划响应
     */
    formatPlanningResponse(response, prompt, intentAnalysis) {
        const steps = this.generateDynamicSteps(prompt, intentAnalysis);
        return `${response.understanding}\n\n执行计划：\n${steps.map((step, i) => `${i + 1}. ${step}`).join('\n')}\n\n推理依据：${response.reasoning}\n\n置信度：${(response.confidence * 100).toFixed(1)}%`;
    }

    /**
     * 格式化验证响应
     */
    formatValidationResponse(response, prompt, intentAnalysis) {
        const validationResult = this.generateValidationResult(prompt, intentAnalysis);
        return `${response.understanding}\n\n验证结果：${validationResult}\n\n分析过程：${response.reasoning}\n\n置信度：${(response.confidence * 100).toFixed(1)}%`;
    }

    /**
     * 格式化观察响应
     */
    formatObservationResponse(response, prompt, intentAnalysis) {
        const observations = this.generateDynamicObservations(prompt, intentAnalysis);
        return `${response.understanding}\n\n观察结果：\n${observations.join('\n')}\n\n分析说明：${response.reasoning}\n\n置信度：${(response.confidence * 100).toFixed(1)}%`;
    }

    /**
     * 格式化通用响应
     */
    formatGeneralResponse(response, prompt, intentAnalysis) {
        return `${response.understanding}\n\n${response.reasoning}\n\n${response.conclusion}\n\n置信度：${(response.confidence * 100).toFixed(1)}%`;
    }

    /**
     * 生成动态步骤（基于上下文推理）
     */
    generateDynamicSteps(prompt, intentAnalysis) {
        const steps = [];
        const entities = intentAnalysis.entities;

        if (entities.length > 0) {
            steps.push(`分析关键要素：${entities.join('、')}`);
        }

        if (intentAnalysis.context.hasBusinessContext) {
            steps.push('理解业务上下文和目标');
        }

        if (intentAnalysis.context.hasConstraints) {
            steps.push('识别约束条件和限制');
        }

        steps.push('制定具体执行方案');
        steps.push('验证方案可行性');

        return steps;
    }

    /**
     * 生成验证结果
     */
    generateValidationResult(prompt, intentAnalysis) {
        if (intentAnalysis.complexity === 'high') {
            return '复杂任务验证：需要多层次验证机制';
        } else {
            return '基础任务验证：符合预期标准';
        }
    }

    /**
     * 生成动态观察结果
     */
    generateDynamicObservations(prompt, intentAnalysis) {
        const observations = [];

        observations.push('页面结构分析完成');

        if (intentAnalysis.entities.length > 0) {
            observations.push(`识别到关键元素：${intentAnalysis.entities.slice(0, 3).join('、')}`);
        }

        observations.push('交互元素可用性检查完成');

        if (intentAnalysis.context.hasBusinessContext) {
            observations.push('业务流程上下文已理解');
        }

        return observations;
    }

    /**
     * 睡眠函数
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 设置响应延迟
     */
    setResponseDelay(ms) {
        this.responseDelay = ms;
        logger.info(`🔧 设置Mock LLM响应延迟: ${ms}ms`);
    }

    /**
     * 健康检查
     */
    async checkHealth() {
        return true; // Mock客户端总是可用
    }

    /**
     * 获取模型名称
     */
    getModel() {
        return this.model;
    }

    /**
     * 设置模型（Mock中无实际效果）
     */
    setModel(model) {
        this.model = `mock-${model}`;
        logger.info(`🔧 Mock LLM模型设置为: ${this.model}`);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            requestCount: this.requestCount,
            errorCount: 0,
            successRate: 1.0,
            currentProvider: 'mock',
            lastSuccessTime: Date.now(),
            responseDelay: this.responseDelay
        };
    }
}

module.exports = MockLLMClient;
