/**
 * Mock LLM客户端 - 用于测试和开发
 * 当真实API不可用时提供智能的模拟响应
 */

const logger = require('../utils/logger');

class MockLLMClient {
    constructor() {
        this.model = 'mock-qwen-plus';
        this.requestCount = 0;
        this.responseDelay = 500; // 模拟网络延迟
        
        // 预定义的智能响应模板
        this.responseTemplates = {
            // 业务语义理解
            semantic_analysis: {
                pattern: /分析|理解|解析|语义/,
                response: {
                    action: "PRODUCT_LISTING",
                    merchant: "榮記豆腐麵食",
                    location: "官也街",
                    product: "菜遠牛肉飯",
                    category: "外卖商品",
                    confidence: 0.95,
                    reasoning: "基于工单内容识别出商品上架操作"
                }
            },
            
            // 页面观察
            page_observation: {
                pattern: /观察|页面|元素|截图|识别.*页面|页面.*元素/,
                response: {
                    elements: [
                        { type: "button", text: "添加商品", selector: ".add-product-btn", confidence: 0.9 },
                        { type: "input", placeholder: "商品名称", selector: "#product-name", confidence: 0.95 },
                        { type: "select", label: "商品分类", selector: "#category", confidence: 0.85 }
                    ],
                    pageType: "商品管理页面",
                    actionable: true,
                    confidence: 0.92
                }
            },

            // 操作决策
            decision_making: {
                pattern: /决策|计划|步骤|操作|制定.*步骤|操作.*步骤|执行.*步骤/,
                response: {
                    steps: [
                        { action: "CLICK", target: ".add-product-btn", description: "点击添加商品按钮" },
                        { action: "TYPE", target: "#product-name", value: "菜遠牛肉飯", description: "输入商品名称" },
                        { action: "SELECT", target: "#category", value: "外卖", description: "选择商品分类" },
                        { action: "CLICK", target: ".save-btn", description: "保存商品信息" }
                    ],
                    strategy: "sequential_execution",
                    confidence: 0.88
                }
            },

            // 执行验证
            validation: {
                pattern: /验证|检查|确认|结果|验证.*结果|执行.*结果/,
                response: {
                    status: "SUCCESS",
                    evidence: [
                        "商品'菜遠牛肉飯'已成功添加到系统",
                        "商品状态显示为'已上架'",
                        "商品ID: PRD-20250714-001"
                    ],
                    confidence: 0.93,
                    nextAction: "COMPLETE"
                }
            }
        };
    }

    /**
     * 模拟聊天请求
     */
    async chat(prompt) {
        this.requestCount++;
        logger.info(`🤖 Mock LLM请求 #${this.requestCount}: ${prompt.substring(0, 50)}...`);
        
        // 模拟网络延迟
        await this.sleep(this.responseDelay);
        
        // 根据提示词内容生成智能响应
        const response = this.generateIntelligentResponse(prompt);
        
        logger.info(`✅ Mock LLM响应: ${JSON.stringify(response).substring(0, 100)}...`);
        return typeof response === 'string' ? response : JSON.stringify(response, null, 2);
    }

    /**
     * 模拟消息格式聊天请求
     */
    async chatWithMessages(messages) {
        const lastUserMessage = messages.filter(m => m.role === 'user').pop();
        return await this.chat(lastUserMessage?.content || '');
    }

    /**
     * 生成智能响应
     */
    generateIntelligentResponse(prompt) {
        // 优先级匹配：先检查具体的业务场景
        if (prompt.includes('榮記豆腐麵食') || prompt.includes('菜遠牛肉飯')) {
            // 进一步细分业务场景
            if (prompt.includes('页面') || prompt.includes('元素') || prompt.includes('观察')) {
                logger.debug(`🎯 匹配业务场景: 页面观察`);
                return this.addContextualVariation(this.responseTemplates.page_observation.response, prompt);
            }
            if (prompt.includes('步骤') || prompt.includes('操作') || prompt.includes('计划') || prompt.includes('制定')) {
                logger.debug(`🎯 匹配业务场景: 操作决策`);
                return this.addContextualVariation(this.responseTemplates.decision_making.response, prompt);
            }
            if (prompt.includes('验证') || prompt.includes('结果') || prompt.includes('确认')) {
                logger.debug(`🎯 匹配业务场景: 执行验证`);
                return this.addContextualVariation(this.responseTemplates.validation.response, prompt);
            }
            // 默认为语义分析
            logger.debug(`🎯 匹配业务场景: 语义分析`);
            return this.addContextualVariation(this.responseTemplates.semantic_analysis.response, prompt);
        }

        // 检查是否匹配预定义模板
        for (const [key, template] of Object.entries(this.responseTemplates)) {
            if (template.pattern.test(prompt)) {
                logger.debug(`🎯 匹配响应模板: ${key}`);
                return this.addContextualVariation(template.response, prompt);
            }
        }

        // 默认智能响应
        return this.generateDefaultResponse(prompt);
    }

    /**
     * 添加上下文变化
     */
    addContextualVariation(baseResponse, prompt) {
        const response = JSON.parse(JSON.stringify(baseResponse)); // 深拷贝
        
        // 根据提示词调整置信度
        if (prompt.includes('确定') || prompt.includes('明确')) {
            response.confidence = Math.min(0.98, response.confidence + 0.05);
        }
        
        // 添加时间戳
        response.timestamp = new Date().toISOString();
        response.requestId = `mock-${this.requestCount}`;
        
        return response;
    }

    /**
     * 生成业务特定响应
     */
    generateBusinessSpecificResponse(prompt) {
        return {
            understanding: "理解工单要求：为榮記豆腐麵食上架菜遠牛肉飯外卖商品",
            action_plan: [
                "导航到商品管理页面",
                "点击添加新商品",
                "填写商品信息：菜遠牛肉飯",
                "设置为外卖商品",
                "关联到榮記豆腐麵食商家",
                "保存并上架"
            ],
            confidence: 0.91,
            business_context: {
                merchant: "榮記豆腐麵食",
                location: "官也街",
                product: "菜遠牛肉飯",
                operation: "商品上架"
            }
        };
    }

    /**
     * 生成默认响应
     */
    generateDefaultResponse(prompt) {
        return {
            status: "UNDERSTOOD",
            message: "Mock LLM已理解请求",
            action: "CONTINUE",
            confidence: 0.75,
            note: "这是Mock响应，用于测试目的"
        };
    }

    /**
     * 睡眠函数
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 设置响应延迟
     */
    setResponseDelay(ms) {
        this.responseDelay = ms;
        logger.info(`🔧 设置Mock LLM响应延迟: ${ms}ms`);
    }

    /**
     * 健康检查
     */
    async checkHealth() {
        return true; // Mock客户端总是可用
    }

    /**
     * 获取模型名称
     */
    getModel() {
        return this.model;
    }

    /**
     * 设置模型（Mock中无实际效果）
     */
    setModel(model) {
        this.model = `mock-${model}`;
        logger.info(`🔧 Mock LLM模型设置为: ${this.model}`);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            requestCount: this.requestCount,
            errorCount: 0,
            successRate: 1.0,
            currentProvider: 'mock',
            lastSuccessTime: Date.now(),
            responseDelay: this.responseDelay
        };
    }
}

module.exports = MockLLMClient;
