/**
 * 逻辑意图库 - 定义所有可用的RPA逻辑意图
 * 这是PDCA架构的核心词汇表
 */

const IntentHierarchy = {
    // 认证相关意图
    AUTHENTICATION: {
        LOGIN: {
            name: "用户登录",
            description: "执行用户登录流程",
            atomicOps: ["navigate_to_login", "input_credentials", "submit_login", "verify_login_success"],
            parameters: {
                username: { type: "string", required: false, description: "用户名（如果需要）" },
                password: { type: "string", required: false, description: "密码（如果需要）" },
                loginUrl: { type: "string", required: false, description: "登录页面URL" }
            },
            expectedOutcome: "成功登录并跳转到主页面",
            failureRecovery: ["RETRY_LOGIN", "REQUEST_USER_LOGIN", "CHECK_CREDENTIALS"]
        },
        
        WAIT_FOR_USER_LOGIN: {
            name: "等待用户登录",
            description: "等待用户手动完成登录过程",
            atomicOps: ["monitor_page_changes", "detect_login_success", "timeout_check"],
            parameters: {
                timeout: { type: "number", required: false, default: 300000, description: "等待超时时间(ms)" },
                successIndicators: { type: "array", required: false, description: "登录成功的页面指示器" }
            },
            expectedOutcome: "检测到用户已成功登录",
            failureRecovery: ["EXTEND_TIMEOUT", "REQUEST_USER_HELP", "MANUAL_VERIFICATION"]
        },
        
        LOGOUT: {
            name: "用户登出",
            description: "执行用户登出流程",
            atomicOps: ["find_logout_button", "click_logout", "confirm_logout"],
            parameters: {},
            expectedOutcome: "成功登出并返回登录页面",
            failureRecovery: ["RETRY_LOGOUT", "FORCE_LOGOUT", "CLEAR_SESSION"]
        }
    },

    // 导航相关意图
    NAVIGATION: {
        NAVIGATE_TO_PAGE: {
            name: "导航到指定页面",
            description: "导航到特定的URL或页面",
            atomicOps: ["navigate", "wait_for_load", "verify_page_loaded"],
            parameters: {
                url: { type: "string", required: true, description: "目标URL" },
                waitForSelector: { type: "string", required: false, description: "等待特定元素出现" }
            },
            expectedOutcome: "成功加载目标页面",
            failureRecovery: ["RETRY_NAVIGATION", "TRY_FALLBACK_URL", "CHECK_NETWORK"]
        },
        
        SELECT_STORE: {
            name: "选择门店",
            description: "在门店选择界面选择指定门店",
            atomicOps: ["find_store_selector", "search_store", "click_store", "verify_store_selected"],
            parameters: {
                storeName: { type: "string", required: true, description: "门店名称" },
                storeLocation: { type: "string", required: false, description: "门店位置信息" }
            },
            expectedOutcome: "成功选择门店并进入门店管理界面",
            failureRecovery: ["RETRY_STORE_SEARCH", "TRY_PARTIAL_MATCH", "REQUEST_STORE_LIST"]
        },
        
        GO_TO_PRODUCT_MANAGEMENT: {
            name: "进入商品管理",
            description: "导航到商品管理页面",
            atomicOps: ["find_menu", "click_product_menu", "wait_for_product_page"],
            parameters: {},
            expectedOutcome: "成功进入商品管理页面",
            failureRecovery: ["RETRY_MENU_CLICK", "TRY_DIRECT_URL", "CHECK_PERMISSIONS"]
        }
    },

    // 商品操作相关意图
    PRODUCT_OPERATIONS: {
        FIND_PRODUCT: {
            name: "查找商品",
            description: "在商品列表中查找指定商品",
            atomicOps: ["open_search", "input_product_name", "execute_search", "locate_product"],
            parameters: {
                productName: { type: "string", required: true, description: "商品名称" },
                category: { type: "string", required: false, description: "商品分类" },
                sku: { type: "string", required: false, description: "商品SKU" }
            },
            expectedOutcome: "在搜索结果中找到目标商品",
            failureRecovery: ["TRY_PARTIAL_SEARCH", "SEARCH_BY_CATEGORY", "BROWSE_ALL_PRODUCTS"]
        },
        
        DELIST_PRODUCT: {
            name: "下架商品",
            description: "将指定商品从销售中下架",
            atomicOps: ["select_product", "find_delist_button", "click_delist", "confirm_delist", "verify_delisted"],
            parameters: {
                productId: { type: "string", required: false, description: "商品ID" },
                confirmationRequired: { type: "boolean", required: false, default: true, description: "是否需要确认" }
            },
            expectedOutcome: "商品成功下架，状态更新为已下架",
            failureRecovery: ["RETRY_DELIST", "TRY_BATCH_OPERATION", "MANUAL_STATUS_UPDATE"]
        },
        
        UPDATE_PRODUCT_STATUS: {
            name: "更新商品状态",
            description: "更新商品的销售状态",
            atomicOps: ["select_product", "open_status_menu", "select_new_status", "save_changes"],
            parameters: {
                productId: { type: "string", required: true, description: "商品ID" },
                newStatus: { type: "string", required: true, description: "新状态" }
            },
            expectedOutcome: "商品状态成功更新",
            failureRecovery: ["RETRY_STATUS_UPDATE", "TRY_ALTERNATIVE_METHOD", "VERIFY_PERMISSIONS"]
        }
    },

    // 系统操作相关意图
    SYSTEM_OPERATIONS: {
        TAKE_SCREENSHOT: {
            name: "截取屏幕截图",
            description: "保存当前页面的截图作为证据",
            atomicOps: ["capture_screenshot", "save_to_file"],
            parameters: {
                filename: { type: "string", required: false, description: "截图文件名" },
                fullPage: { type: "boolean", required: false, default: true, description: "是否截取整页" }
            },
            expectedOutcome: "成功保存页面截图",
            failureRecovery: ["RETRY_SCREENSHOT", "TRY_VIEWPORT_ONLY", "SKIP_SCREENSHOT"]
        },
        
        WAIT_FOR_ELEMENT: {
            name: "等待元素出现",
            description: "等待页面上特定元素出现",
            atomicOps: ["monitor_dom", "check_element_presence", "timeout_handling"],
            parameters: {
                selector: { type: "string", required: true, description: "元素选择器" },
                timeout: { type: "number", required: false, default: 30000, description: "等待超时时间" }
            },
            expectedOutcome: "目标元素成功出现在页面上",
            failureRecovery: ["EXTEND_TIMEOUT", "TRY_ALTERNATIVE_SELECTOR", "REFRESH_PAGE"]
        }
    }
};

// 失败恢复策略库
const FailureRecoveryStrategies = {
    // 登录相关恢复策略
    RETRY_LOGIN: {
        description: "重新尝试登录",
        maxRetries: 3,
        delay: 2000
    },
    REQUEST_USER_LOGIN: {
        description: "请求用户手动登录",
        userInteraction: true,
        timeout: 300000
    },
    CHECK_CREDENTIALS: {
        description: "检查登录凭据",
        requiresValidation: true
    },
    
    // 导航相关恢复策略
    RETRY_NAVIGATION: {
        description: "重新尝试导航",
        maxRetries: 2,
        delay: 3000
    },
    TRY_FALLBACK_URL: {
        description: "尝试备用URL",
        fallbackUrls: ["/#/select?noDirect=1", "/#/select"]
    },
    CHECK_NETWORK: {
        description: "检查网络连接",
        diagnostic: true
    },
    
    // 商品操作恢复策略
    TRY_PARTIAL_SEARCH: {
        description: "尝试部分匹配搜索",
        fuzzyMatch: true
    },
    SEARCH_BY_CATEGORY: {
        description: "按分类搜索",
        alternative: true
    },
    
    // 系统级恢复策略
    REFRESH_PAGE: {
        description: "刷新页面",
        action: "reload"
    },
    EXTEND_TIMEOUT: {
        description: "延长等待时间",
        timeoutMultiplier: 2
    }
};

// 意图验证器
class IntentValidator {
    static validateIntent(intentPath, parameters = {}) {
        const intent = this.getIntentByPath(intentPath);
        if (!intent) {
            throw new Error(`未知的意图: ${intentPath}`);
        }
        
        // 验证必需参数
        for (const [paramName, paramDef] of Object.entries(intent.parameters || {})) {
            if (paramDef.required && !(paramName in parameters)) {
                throw new Error(`缺少必需参数: ${paramName}`);
            }
        }
        
        return true;
    }
    
    static getIntentByPath(intentPath) {
        const parts = intentPath.split('.');
        let current = IntentHierarchy;
        
        for (const part of parts) {
            current = current[part];
            if (!current) return null;
        }
        
        return current;
    }
    
    static getAllIntents() {
        const intents = [];
        
        function traverse(obj, path = []) {
            for (const [key, value] of Object.entries(obj)) {
                if (value.name && value.atomicOps) {
                    // 这是一个意图定义
                    intents.push({
                        path: path.concat(key).join('.'),
                        ...value
                    });
                } else {
                    // 这是一个分类，继续遍历
                    traverse(value, path.concat(key));
                }
            }
        }
        
        traverse(IntentHierarchy);
        return intents;
    }
}

module.exports = {
    IntentHierarchy,
    FailureRecoveryStrategies,
    IntentValidator
};
