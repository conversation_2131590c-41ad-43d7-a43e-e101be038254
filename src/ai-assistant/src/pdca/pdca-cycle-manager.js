/**
 * PDCA循环管理器 - 协调三个Agent的工作
 * 实现Plan-Do-Check-Act循环
 */

const PlannerAgent = require('./planner-agent');
const ExecutorAgent = require('./executor-agent');
const CheckerAgent = require('./checker-agent');
const logger = require('../utils/logger');

class PDCACycleManager {
    constructor(llmClient, mcpClient) {
        this.planner = new PlannerAgent(llmClient);
        this.executor = new ExecutorAgent(mcpClient);
        this.checker = new CheckerAgent(llmClient);
        
        // 循环控制参数
        this.maxCycles = 50;
        this.minConfidence = 0.3;
        this.maxConsecutiveFailures = 3;
        this.cycleTimeout = 300000; // 5分钟总超时
        
        this.cycleId = 0;
    }

    /**
     * 执行完整的PDCA循环
     * @param {string} taskGoal - 任务目标
     * @param {Object} initialContext - 初始上下文
     * @returns {Object} 执行结果
     */
    async executePDCACycle(taskGoal, initialContext = {}) {
        this.cycleId++;
        const startTime = Date.now();
        
        logger.info(`🔄 开始PDCA循环`, {
            cycleId: this.cycleId,
            taskGoal: taskGoal
        });

        // 初始化上下文
        let context = this.initializeContext(taskGoal, initialContext);
        
        try {
            while (!this.shouldTerminate(context, startTime)) {
                const cycleStartTime = Date.now();
                
                logger.info(`🔄 PDCA循环 ${context.cycle}/${this.maxCycles}`, {
                    phase: 'starting',
                    taskGoal: context.task_goal
                });

                try {
                    // Plan阶段
                    const plan = await this.executePlanPhase(context);
                    if (this.isTerminalPlan(plan)) {
                        return this.handleTerminalPlan(plan, context);
                    }
                    
                    // Do阶段
                    const evidence = await this.executeDoPhase(plan, context);
                    
                    // Check阶段
                    const checkResult = await this.executeCheckPhase(plan, evidence, context);
                    
                    // Act阶段 - 更新上下文，准备下一轮
                    context = this.executeActPhase(context, plan, evidence, checkResult);
                    
                    // 检查是否应该终止
                    if (checkResult.nextAction === 'TASK_COMPLETED') {
                        return this.completeTask(context, checkResult);
                    }
                    
                    // 准备下一轮循环
                    context.cycle++;
                    
                    logger.info(`✅ PDCA循环 ${context.cycle - 1} 完成`, {
                        status: checkResult.status,
                        nextAction: checkResult.nextAction,
                        cycleTime: Date.now() - cycleStartTime
                    });
                    
                } catch (error) {
                    logger.error(`❌ PDCA循环 ${context.cycle} 出错:`, error);
                    
                    context.errors.push({
                        cycle: context.cycle,
                        error: error.message,
                        timestamp: Date.now()
                    });
                    
                    // 检查是否应该因错误而终止
                    if (this.shouldTerminateOnError(context)) {
                        return this.escalateToUser(context, '连续错误过多');
                    }
                    
                    context.cycle++;
                }
            }
            
            // 达到最大循环次数或超时
            return this.timeoutEscalation(context);
            
        } catch (error) {
            logger.error(`❌ PDCA循环管理器出错:`, error);
            return this.handleCriticalError(context, error);
        }
    }

    /**
     * Plan阶段 - 规划下一步行动
     */
    async executePlanPhase(context) {
        logger.info(`🧠 Plan阶段 - 循环 ${context.cycle}`);
        
        try {
            const plan = await this.planner.plan(context);
            
            // 检查规划置信度
            if (plan.confidence < this.minConfidence) {
                logger.warn(`⚠️ 规划置信度过低: ${plan.confidence}`);
                return this.generateLowConfidencePlan(context, plan);
            }
            
            return plan;
            
        } catch (error) {
            logger.error('Plan阶段失败:', error);
            throw new Error(`规划失败: ${error.message}`);
        }
    }

    /**
     * Do阶段 - 执行计划
     */
    async executeDoPhase(plan, context) {
        logger.info(`🔧 Do阶段 - 执行: ${plan.intent}`);
        
        try {
            const evidence = await this.executor.execute(plan, context);
            return evidence;
            
        } catch (error) {
            logger.error('Do阶段失败:', error);
            throw new Error(`执行失败: ${error.message}`);
        }
    }

    /**
     * Check阶段 - 检查执行结果
     */
    async executeCheckPhase(plan, evidence, context) {
        logger.info(`🔍 Check阶段 - 验证结果`);
        
        try {
            const checkResult = await this.checker.check(plan, evidence, context);
            return checkResult;
            
        } catch (error) {
            logger.error('Check阶段失败:', error);
            
            // 返回一个保守的检查结果
            return {
                status: 'NEEDS_VERIFICATION',
                reason: `检查失败: ${error.message}`,
                confidence: 0.3,
                nextAction: 'CONTINUE',
                suggestions: '检查器出错，建议继续执行并观察'
            };
        }
    }

    /**
     * Act阶段 - 更新上下文，准备下一轮
     */
    executeActPhase(context, plan, evidence, checkResult) {
        logger.info(`⚡ Act阶段 - 更新上下文`);
        
        // 添加到执行历史
        context.execution_history.push({
            cycle: context.cycle,
            plan: plan,
            evidence: evidence,
            check_result: checkResult,
            timestamp: Date.now()
        });
        
        // 更新当前状态
        if (evidence.afterState) {
            context.current_state = evidence.afterState;
        }
        
        // 更新统计信息
        context.stats.totalExecutions++;
        if (checkResult.status === 'SUCCESS') {
            context.stats.successfulExecutions++;
        } else if (checkResult.status === 'FAILURE') {
            context.stats.failedExecutions++;
        }
        
        // 压缩历史记录（保持上下文可管理）
        if (context.execution_history.length > 10) {
            context.execution_history = this.compressHistory(context.execution_history);
        }
        
        // 更新连续失败计数
        if (checkResult.status === 'FAILURE') {
            context.consecutiveFailures++;
        } else {
            context.consecutiveFailures = 0;
        }
        
        return context;
    }

    /**
     * 初始化执行上下文
     */
    initializeContext(taskGoal, initialContext) {
        return {
            task_goal: taskGoal,
            cycle: 1,
            execution_history: [],
            current_state: initialContext.current_state || null,
            errors: [],
            consecutiveFailures: 0,
            stats: {
                totalExecutions: 0,
                successfulExecutions: 0,
                failedExecutions: 0
            },
            startTime: Date.now(),
            ...initialContext
        };
    }

    /**
     * 判断是否应该终止循环
     */
    shouldTerminate(context, startTime) {
        // 检查最大循环次数
        if (context.cycle > this.maxCycles) {
            logger.warn(`达到最大循环次数: ${this.maxCycles}`);
            return true;
        }
        
        // 检查总超时时间
        if (Date.now() - startTime > this.cycleTimeout) {
            logger.warn(`达到总超时时间: ${this.cycleTimeout}ms`);
            return true;
        }
        
        // 检查连续失败次数
        if (context.consecutiveFailures >= this.maxConsecutiveFailures) {
            logger.warn(`连续失败次数过多: ${context.consecutiveFailures}`);
            return true;
        }
        
        return false;
    }

    /**
     * 检查是否为终端计划（需要特殊处理的计划）
     */
    isTerminalPlan(plan) {
        const terminalIntents = [
            'TASK_COMPLETED',
            'USER_INTERVENTION_REQUIRED',
            'CRITICAL_ERROR',
            'INSUFFICIENT_INFORMATION'
        ];
        
        return terminalIntents.includes(plan.intent);
    }

    /**
     * 处理终端计划
     */
    handleTerminalPlan(plan, context) {
        switch (plan.intent) {
            case 'TASK_COMPLETED':
                return this.completeTask(context, { status: 'SUCCESS', reason: '任务完成' });
                
            case 'USER_INTERVENTION_REQUIRED':
                return this.requestUserIntervention(context, plan.parameters.reason);
                
            case 'CRITICAL_ERROR':
                return this.escalateToUser(context, plan.parameters.reason);
                
            default:
                return this.escalateToUser(context, `未知的终端意图: ${plan.intent}`);
        }
    }

    /**
     * 压缩执行历史
     */
    compressHistory(history) {
        // 保留最近5次记录
        const recent = history.slice(-5);
        
        // 总结更早的记录
        const older = history.slice(0, -5);
        if (older.length > 0) {
            const summary = {
                cycle: 'summary',
                plan: { intent: 'HISTORY_SUMMARY' },
                evidence: {
                    summary: `总结了${older.length}次早期执行`,
                    successCount: older.filter(r => r.check_result?.status === 'SUCCESS').length,
                    failureCount: older.filter(r => r.check_result?.status === 'FAILURE').length
                },
                check_result: {
                    status: 'SUMMARY',
                    reason: `历史记录摘要 (${older.length}次执行)`
                }
            };
            
            return [summary, ...recent];
        }
        
        return recent;
    }

    /**
     * 完成任务
     */
    completeTask(context, finalResult) {
        const duration = Date.now() - context.startTime;
        
        logger.info(`🎉 任务完成`, {
            taskGoal: context.task_goal,
            totalCycles: context.cycle,
            duration: duration,
            stats: context.stats
        });
        
        return {
            status: 'COMPLETED',
            result: finalResult,
            context: context,
            summary: {
                taskGoal: context.task_goal,
                totalCycles: context.cycle,
                duration: duration,
                stats: context.stats,
                finalState: context.current_state
            }
        };
    }

    /**
     * 请求用户干预
     */
    requestUserIntervention(context, reason) {
        logger.info(`🤝 请求用户干预: ${reason}`);
        
        return {
            status: 'USER_INTERVENTION_REQUIRED',
            reason: reason,
            context: context,
            instructions: '请完成必要的操作后，点击继续按钮'
        };
    }

    /**
     * 升级到用户处理
     */
    escalateToUser(context, reason) {
        logger.warn(`⚠️ 升级到用户处理: ${reason}`);
        
        return {
            status: 'ESCALATED',
            reason: reason,
            context: context,
            recommendation: '建议人工接管或调整任务参数'
        };
    }

    /**
     * 超时升级
     */
    timeoutEscalation(context) {
        return this.escalateToUser(context, '执行超时，可能任务过于复杂或遇到未预期的情况');
    }

    /**
     * 处理关键错误
     */
    handleCriticalError(context, error) {
        logger.error(`💥 关键错误:`, error);
        
        return {
            status: 'CRITICAL_ERROR',
            error: error.message,
            context: context,
            recommendation: '系统出现关键错误，需要技术支持'
        };
    }
}

module.exports = PDCACycleManager;
