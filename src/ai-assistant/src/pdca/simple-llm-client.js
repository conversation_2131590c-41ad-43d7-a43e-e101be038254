/**
 * 简单的LLM客户端包装器
 * 为PDCA架构提供统一的LLM接口
 */

const axios = require('axios');
const logger = require('../utils/logger');

class SimpleLLMClient {
    constructor() {
        this.model = process.env.MAIN_MODEL || 'qwen-plus';
        this.apiKey = process.env.DASHSCOPE_API_KEY || 'sk-your-api-key';
        this.baseURL = process.env.DASHSCOPE_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1';
    }

    /**
     * 发送聊天请求
     * @param {string} prompt - 提示词
     * @returns {string} AI响应
     */
    async chat(prompt) {
        try {
            logger.info(`🧠 发送LLM请求 (${this.model})`);
            
            const response = await axios.post(`${this.baseURL}/chat/completions`, {
                model: this.model,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 2000,
                top_p: 0.8
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });

            if (response.data.choices && response.data.choices.length > 0) {
                const content = response.data.choices[0].message.content;
                logger.info(`✅ LLM响应成功 (${content.length}字符)`);
                return content;
            } else {
                throw new Error('LLM响应格式异常');
            }

        } catch (error) {
            logger.error('❌ LLM请求失败:', error.message);
            
            // 返回一个安全的默认响应
            return this.generateFallbackResponse(prompt);
        }
    }

    /**
     * 发送聊天请求（使用messages格式）
     * @param {Array} messages - 消息数组
     * @returns {string} AI响应
     */
    async chatWithMessages(messages) {
        try {
            logger.info(`🧠 发送LLM请求 (${this.model}) - ${messages.length}条消息`);
            
            const response = await axios.post(`${this.baseURL}/chat/completions`, {
                model: this.model,
                messages: messages,
                temperature: 0.1,
                max_tokens: 2000,
                top_p: 0.8
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });

            if (response.data.choices && response.data.choices.length > 0) {
                const content = response.data.choices[0].message.content;
                logger.info(`✅ LLM响应成功 (${content.length}字符)`);
                return content;
            } else {
                throw new Error('LLM响应格式异常');
            }

        } catch (error) {
            logger.error('❌ LLM请求失败:', error.message);
            
            // 返回一个安全的默认响应
            const lastUserMessage = messages.filter(m => m.role === 'user').pop();
            return this.generateFallbackResponse(lastUserMessage?.content || '');
        }
    }

    /**
     * 生成备用响应（当LLM不可用时）
     */
    generateFallbackResponse(prompt) {
        logger.warn('🔄 使用备用响应生成器');
        
        // 根据提示词类型生成简单的备用响应
        if (prompt.includes('规划') || prompt.includes('plan')) {
            return JSON.stringify({
                intent: 'SYSTEM_OPERATIONS.TAKE_SCREENSHOT',
                parameters: {},
                confidence: 0.5,
                reasoning: 'LLM不可用，使用备用规划',
                expectedOutcome: '截取当前页面状态'
            });
        }
        
        if (prompt.includes('检查') || prompt.includes('check')) {
            return JSON.stringify({
                status: 'NEEDS_VERIFICATION',
                reason: 'LLM不可用，无法进行深度检查',
                confidence: 0.3,
                nextAction: 'CONTINUE',
                suggestions: '建议继续执行并观察结果'
            });
        }
        
        // 默认响应
        return JSON.stringify({
            status: 'UNKNOWN',
            reason: 'LLM服务不可用',
            confidence: 0.1,
            action: 'CONTINUE'
        });
    }

    /**
     * 设置模型
     */
    setModel(model) {
        this.model = model;
        logger.info(`🔧 切换LLM模型: ${model}`);
    }

    /**
     * 获取当前模型
     */
    getModel() {
        return this.model;
    }

    /**
     * 检查LLM服务是否可用
     */
    async checkHealth() {
        try {
            const response = await this.chat('测试连接');
            return response.length > 0;
        } catch (error) {
            return false;
        }
    }
}

module.exports = SimpleLLMClient;
