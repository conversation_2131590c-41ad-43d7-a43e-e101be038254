/**
 * 增强的LLM客户端包装器
 * 为PDCA架构提供统一的LLM接口，支持多提供商和智能降级
 */

const axios = require('axios');
const logger = require('../utils/logger');

class SimpleLLMClient {
    constructor() {
        this.model = process.env.MAIN_MODEL || 'qwen-plus';
        this.apiKey = process.env.DASHSCOPE_API_KEY || 'sk-your-api-key';
        this.baseURL = process.env.DASHSCOPE_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1';

        // 多提供商配置
        this.providers = [
            {
                name: 'dashscope',
                baseURL: this.baseURL,
                apiKey: this.apiKey,
                model: this.model,
                priority: 1
            }
            // 可以添加更多提供商作为备用
        ];

        this.currentProviderIndex = 0;
        this.requestCount = 0;
        this.errorCount = 0;
        this.lastSuccessTime = Date.now();
    }

    /**
     * 发送聊天请求（增强版，支持重试和提供商切换）
     * @param {string} prompt - 提示词
     * @returns {string} AI响应
     */
    async chat(prompt) {
        const maxRetries = 3;
        let lastError = null;

        for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
                const provider = this.getCurrentProvider();
                logger.info(`🧠 发送LLM请求 (${provider.model}) - 尝试 ${attempt + 1}/${maxRetries}`);

                const response = await this.makeRequest(provider, [
                    {
                        role: 'user',
                        content: prompt
                    }
                ]);

                if (response.data.choices && response.data.choices.length > 0) {
                    const content = response.data.choices[0].message.content;
                    logger.info(`✅ LLM响应成功 (${content.length}字符)`);

                    // 记录成功
                    this.recordSuccess();
                    return content;
                } else {
                    throw new Error('LLM响应格式异常');
                }

            } catch (error) {
                lastError = error;
                this.recordError(error);

                logger.warn(`⚠️ LLM请求失败 (尝试 ${attempt + 1}/${maxRetries}): ${error.message}`);

                // 如果是credits不足，尝试切换提供商
                if (error.response?.status === 402 || error.message.includes('credits')) {
                    logger.warn('💳 Credits不足，尝试切换提供商...');
                    this.switchToNextProvider();
                }

                // 最后一次尝试失败，使用备用响应
                if (attempt === maxRetries - 1) {
                    logger.error('❌ 所有LLM请求尝试失败，使用备用响应');
                    return this.generateFallbackResponse(prompt);
                }

                // 等待后重试
                await this.sleep(1000 * (attempt + 1));
            }
        }

        return this.generateFallbackResponse(prompt);
    }

    /**
     * 发送聊天请求（使用messages格式）
     * @param {Array} messages - 消息数组
     * @returns {string} AI响应
     */
    async chatWithMessages(messages) {
        try {
            logger.info(`🧠 发送LLM请求 (${this.model}) - ${messages.length}条消息`);
            
            const response = await axios.post(`${this.baseURL}/chat/completions`, {
                model: this.model,
                messages: messages,
                temperature: 0.1,
                max_tokens: 2000,
                top_p: 0.8
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });

            if (response.data.choices && response.data.choices.length > 0) {
                const content = response.data.choices[0].message.content;
                logger.info(`✅ LLM响应成功 (${content.length}字符)`);
                return content;
            } else {
                throw new Error('LLM响应格式异常');
            }

        } catch (error) {
            logger.error('❌ LLM请求失败:', error.message);
            
            // 返回一个安全的默认响应
            const lastUserMessage = messages.filter(m => m.role === 'user').pop();
            return this.generateFallbackResponse(lastUserMessage?.content || '');
        }
    }

    /**
     * 生成备用响应（当LLM不可用时）
     */
    generateFallbackResponse(prompt) {
        logger.warn('🔄 使用备用响应生成器');
        
        // 根据提示词类型生成简单的备用响应
        if (prompt.includes('规划') || prompt.includes('plan')) {
            return JSON.stringify({
                intent: 'SYSTEM_OPERATIONS.TAKE_SCREENSHOT',
                parameters: {},
                confidence: 0.5,
                reasoning: 'LLM不可用，使用备用规划',
                expectedOutcome: '截取当前页面状态'
            });
        }
        
        if (prompt.includes('检查') || prompt.includes('check')) {
            return JSON.stringify({
                status: 'NEEDS_VERIFICATION',
                reason: 'LLM不可用，无法进行深度检查',
                confidence: 0.3,
                nextAction: 'CONTINUE',
                suggestions: '建议继续执行并观察结果'
            });
        }
        
        // 默认响应
        return JSON.stringify({
            status: 'UNKNOWN',
            reason: 'LLM服务不可用',
            confidence: 0.1,
            action: 'CONTINUE'
        });
    }

    /**
     * 设置模型
     */
    setModel(model) {
        this.model = model;
        logger.info(`🔧 切换LLM模型: ${model}`);
    }

    /**
     * 获取当前模型
     */
    getModel() {
        return this.model;
    }

    /**
     * 获取当前提供商
     */
    getCurrentProvider() {
        return this.providers[this.currentProviderIndex];
    }

    /**
     * 切换到下一个提供商
     */
    switchToNextProvider() {
        this.currentProviderIndex = (this.currentProviderIndex + 1) % this.providers.length;
        logger.info(`🔄 切换到提供商: ${this.getCurrentProvider().name}`);
    }

    /**
     * 发送HTTP请求
     */
    async makeRequest(provider, messages) {
        return await axios.post(`${provider.baseURL}/chat/completions`, {
            model: provider.model,
            messages: messages,
            temperature: 0.1,
            max_tokens: 2000,
            top_p: 0.8
        }, {
            headers: {
                'Authorization': `Bearer ${provider.apiKey}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
    }

    /**
     * 记录成功请求
     */
    recordSuccess() {
        this.requestCount++;
        this.lastSuccessTime = Date.now();
    }

    /**
     * 记录错误
     */
    recordError(error) {
        this.errorCount++;
        logger.debug(`📊 LLM统计: 请求${this.requestCount}次, 错误${this.errorCount}次`);
    }

    /**
     * 睡眠函数
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 检查LLM服务是否可用
     */
    async checkHealth() {
        try {
            const response = await this.chat('测试连接');
            return response.length > 0;
        } catch (error) {
            return false;
        }
    }

    /**
     * 获取服务统计信息
     */
    getStats() {
        return {
            requestCount: this.requestCount,
            errorCount: this.errorCount,
            successRate: this.requestCount > 0 ? (this.requestCount - this.errorCount) / this.requestCount : 0,
            currentProvider: this.getCurrentProvider().name,
            lastSuccessTime: this.lastSuccessTime
        };
    }
}

module.exports = SimpleLLMClient;
