/**
 * PlannerAgent - PDCA架构中的规划器
 * 职责：分析当前状态，制定下一步逻辑意图
 */

const { IntentValidator } = require('./intent-library');
const logger = require('../utils/logger');

class PlannerAgent {
    constructor(llmClient) {
        this.llmClient = llmClient;
        this.planningId = 0;
    }

    /**
     * 分析当前状态并制定下一步计划
     * @param {Object} context - 当前执行上下文
     * @returns {Object} 执行计划
     */
    async plan(context) {
        this.planningId++;
        
        logger.info(`🧠 PlannerAgent开始规划`, {
            planningId: this.planningId,
            cycle: context.cycle,
            taskGoal: context.task_goal
        });

        try {
            // 构建规划提示词
            const prompt = this.buildPlanningPrompt(context);
            
            // 调用LLM进行规划
            const response = await this.llmClient.chat(prompt);
            
            // 解析LLM响应
            const plan = this.parsePlanningResponse(response);
            
            // 验证计划
            this.validatePlan(plan);
            
            logger.info(`✅ 规划完成: ${plan.intent}`, {
                planningId: this.planningId,
                confidence: plan.confidence,
                reasoning: plan.reasoning
            });
            
            return plan;
            
        } catch (error) {
            logger.error(`❌ 规划失败:`, error);
            throw error;
        }
    }

    /**
     * 构建规划提示词
     */
    buildPlanningPrompt(context) {
        const availableIntents = this.getAvailableIntents();
        const executionHistory = this.formatExecutionHistory(context.execution_history || []);
        const currentState = this.formatCurrentState(context.current_state);
        
        return `你是一个RPA系统的智能规划器，负责分析当前状态并制定下一步行动计划。

🎯 **任务目标**: ${context.task_goal}

📊 **执行历史** (最近${Math.min(5, executionHistory.length)}次):
${executionHistory}

📸 **当前状态**:
${currentState}

🔧 **可用的逻辑意图**:
${availableIntents}

📋 **规划要求**:
1. 分析当前状态和任务目标
2. 选择最合适的下一步逻辑意图
3. 提供必要的参数
4. 评估成功概率
5. 说明选择理由

⚠️ **重要提醒**:
- 只规划下一步，不要规划整个流程
- 如果需要用户干预，选择相应的等待意图
- 如果任务已完成，选择TASK_COMPLETED
- 如果遇到无法解决的问题，选择REQUEST_USER_HELP

请以JSON格式返回你的规划结果:
{
    "intent": "CATEGORY.INTENT_NAME",
    "parameters": {
        "param1": "value1",
        "param2": "value2"
    },
    "confidence": 0.85,
    "reasoning": "选择这个意图的详细原因",
    "expectedOutcome": "预期的执行结果",
    "alternativeIntents": ["备选意图1", "备选意图2"]
}`;
    }

    /**
     * 获取可用意图列表
     */
    getAvailableIntents() {
        const intents = IntentValidator.getAllIntents();
        
        return intents.map(intent => {
            const params = Object.entries(intent.parameters || {})
                .map(([name, def]) => `${name}${def.required ? '*' : ''}`)
                .join(', ');
            
            return `- ${intent.path}: ${intent.description}${params ? ` (参数: ${params})` : ''}`;
        }).join('\n');
    }

    /**
     * 格式化执行历史
     */
    formatExecutionHistory(history) {
        if (history.length === 0) {
            return "无执行历史";
        }
        
        return history.slice(-5).map((record, index) => {
            const cycle = record.cycle || index + 1;
            const intent = record.plan?.intent || '未知意图';
            const status = record.check_result?.status || '未知状态';
            const reason = record.check_result?.reason || '无详细信息';
            
            return `第${cycle}轮: ${intent} -> ${status} (${reason})`;
        }).join('\n');
    }

    /**
     * 格式化当前状态
     */
    formatCurrentState(currentState) {
        if (!currentState) {
            return "无当前状态信息";
        }
        
        const parts = [];
        
        if (currentState.url) {
            parts.push(`URL: ${currentState.url}`);
        }
        
        if (currentState.domSnapshot) {
            // 截取DOM快照的关键部分
            const snippet = currentState.domSnapshot.substring(0, 500);
            parts.push(`页面内容: ${snippet}${currentState.domSnapshot.length > 500 ? '...' : ''}`);
        }
        
        if (currentState.timestamp) {
            parts.push(`时间: ${new Date(currentState.timestamp).toLocaleString()}`);
        }
        
        return parts.join('\n');
    }

    /**
     * 解析LLM规划响应
     */
    parsePlanningResponse(response) {
        try {
            // 尝试从响应中提取JSON
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('响应中未找到JSON格式的规划结果');
            }
            
            const plan = JSON.parse(jsonMatch[0]);
            
            // 确保必需字段存在
            if (!plan.intent) {
                throw new Error('规划结果缺少intent字段');
            }
            
            // 设置默认值
            plan.parameters = plan.parameters || {};
            plan.confidence = plan.confidence || 0.5;
            plan.reasoning = plan.reasoning || '未提供推理过程';
            plan.expectedOutcome = plan.expectedOutcome || '未指定预期结果';
            plan.alternativeIntents = plan.alternativeIntents || [];
            
            return plan;
            
        } catch (error) {
            logger.error('解析规划响应失败:', error);
            logger.error('原始响应:', response);
            
            // 返回一个安全的默认计划
            return {
                intent: 'SYSTEM_OPERATIONS.TAKE_SCREENSHOT',
                parameters: {},
                confidence: 0.1,
                reasoning: `解析LLM响应失败: ${error.message}`,
                expectedOutcome: '截取当前页面状态',
                alternativeIntents: []
            };
        }
    }

    /**
     * 验证规划结果
     */
    validatePlan(plan) {
        // 验证意图是否存在
        try {
            IntentValidator.validateIntent(plan.intent, plan.parameters);
        } catch (error) {
            logger.warn(`规划验证失败: ${error.message}`);
            // 可以选择修正计划或抛出错误
            throw new Error(`无效的规划: ${error.message}`);
        }
        
        // 验证置信度范围
        if (plan.confidence < 0 || plan.confidence > 1) {
            logger.warn(`置信度超出范围: ${plan.confidence}`);
            plan.confidence = Math.max(0, Math.min(1, plan.confidence));
        }
        
        return true;
    }

    /**
     * 分析任务完成状态
     */
    analyzeTaskCompletion(context) {
        const taskGoal = context.task_goal.toLowerCase();
        const currentState = context.current_state;
        
        if (!currentState || !currentState.domSnapshot) {
            return { completed: false, confidence: 0 };
        }
        
        const pageContent = currentState.domSnapshot.toLowerCase();
        
        // 根据任务目标分析完成状态
        if (taskGoal.includes('下架')) {
            // 检查下架相关的成功指示器
            const successIndicators = [
                '下架成功', '已下架', '下架完成',
                'delisted', 'removed', 'disabled'
            ];
            
            const hasSuccessIndicator = successIndicators.some(indicator => 
                pageContent.includes(indicator)
            );
            
            return {
                completed: hasSuccessIndicator,
                confidence: hasSuccessIndicator ? 0.9 : 0.1,
                evidence: hasSuccessIndicator ? '页面显示下架成功信息' : '未发现下架成功信息'
            };
        }
        
        // 其他任务类型的完成检测
        return { completed: false, confidence: 0, evidence: '无法确定任务完成状态' };
    }

    /**
     * 生成用户干预请求
     */
    generateUserInterventionRequest(context, reason) {
        return {
            intent: 'USER_INTERVENTION_REQUIRED',
            parameters: {
                reason: reason,
                currentState: context.current_state,
                taskGoal: context.task_goal,
                executionHistory: context.execution_history
            },
            confidence: 1.0,
            reasoning: `需要用户干预: ${reason}`,
            expectedOutcome: '用户完成必要操作后继续执行'
        };
    }
}

module.exports = PlannerAgent;
