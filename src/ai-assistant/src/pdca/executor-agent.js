/**
 * ExecutorAgent - PDCA架构中的执行器
 * 职责：接收逻辑意图，执行原子操作，收集执行证据
 */

const { IntentValidator } = require('./intent-library');
const logger = require('../utils/logger');

class ExecutorAgent {
    constructor(mcpClient) {
        this.mcpClient = mcpClient;
        this.executionId = 0;
    }

    /**
     * 执行逻辑意图
     * @param {Object} plan - 来自PlannerAgent的执行计划
     * @param {Object} context - 当前执行上下文
     * @returns {Object} 执行证据
     */
    async execute(plan, context) {
        this.executionId++;
        const startTime = Date.now();
        
        logger.info(`🔧 ExecutorAgent开始执行意图: ${plan.intent}`, {
            executionId: this.executionId,
            parameters: plan.parameters
        });

        // 验证意图
        try {
            IntentValidator.validateIntent(plan.intent, plan.parameters);
        } catch (error) {
            return this.createFailureEvidence(plan, null, error, startTime);
        }

        // 捕获执行前状态
        const beforeState = await this.captureState('before_execution');
        
        try {
            // 执行意图对应的原子操作
            const executionResult = await this.executeIntent(plan);
            
            // 捕获执行后状态
            const afterState = await this.captureState('after_execution');
            
            // 创建成功证据
            return this.createSuccessEvidence(plan, beforeState, afterState, executionResult, startTime);
            
        } catch (error) {
            logger.error(`❌ 意图执行失败: ${plan.intent}`, error);
            
            // 捕获错误状态
            const errorState = await this.captureState('error_state');
            
            return this.createFailureEvidence(plan, beforeState, error, startTime, errorState);
        }
    }

    /**
     * 执行具体的逻辑意图
     */
    async executeIntent(plan) {
        const intentPath = plan.intent;
        const parameters = plan.parameters || {};
        
        // 根据意图路径调用相应的执行方法
        switch (intentPath) {
            case 'AUTHENTICATION.LOGIN':
                return await this.executeLogin(parameters);
                
            case 'AUTHENTICATION.WAIT_FOR_USER_LOGIN':
                return await this.executeWaitForUserLogin(parameters);
                
            case 'AUTHENTICATION.LOGOUT':
                return await this.executeLogout(parameters);
                
            case 'NAVIGATION.NAVIGATE_TO_PAGE':
                return await this.executeNavigateToPage(parameters);
                
            case 'NAVIGATION.SELECT_STORE':
                return await this.executeSelectStore(parameters);
                
            case 'NAVIGATION.GO_TO_PRODUCT_MANAGEMENT':
                return await this.executeGoToProductManagement(parameters);
                
            case 'PRODUCT_OPERATIONS.FIND_PRODUCT':
                return await this.executeFindProduct(parameters);
                
            case 'PRODUCT_OPERATIONS.DELIST_PRODUCT':
                return await this.executeDelistProduct(parameters);
                
            case 'SYSTEM_OPERATIONS.TAKE_SCREENSHOT':
                return await this.executeTakeScreenshot(parameters);
                
            case 'SYSTEM_OPERATIONS.WAIT_FOR_ELEMENT':
                return await this.executeWaitForElement(parameters);
                
            default:
                throw new Error(`未实现的意图: ${intentPath}`);
        }
    }

    // ==================== 认证相关意图实现 ====================
    
    async executeLogin(params) {
        const steps = [];
        
        // 1. 导航到登录页面（如果提供了URL）
        if (params.loginUrl) {
            await this.mcpClient.callTool('browser_navigate', { url: params.loginUrl });
            steps.push('navigated_to_login_page');
        }
        
        // 2. 等待页面加载
        await this.sleep(2000);
        steps.push('waited_for_page_load');
        
        // 3. 输入凭据（如果提供）
        if (params.username && params.password) {
            // 查找用户名输入框
            const usernameInput = await this.findElement(['input[name="username"]', 'input[type="text"]', '#username']);
            if (usernameInput) {
                await this.mcpClient.callTool('browser_type', {
                    element: 'username input',
                    ref: usernameInput.ref,
                    text: params.username
                });
                steps.push('entered_username');
            }
            
            // 查找密码输入框
            const passwordInput = await this.findElement(['input[name="password"]', 'input[type="password"]', '#password']);
            if (passwordInput) {
                await this.mcpClient.callTool('browser_type', {
                    element: 'password input',
                    ref: passwordInput.ref,
                    text: params.password
                });
                steps.push('entered_password');
            }
            
            // 查找并点击登录按钮
            const loginButton = await this.findElement(['button[type="submit"]', 'input[type="submit"]', 'button:contains("登录")', 'button:contains("Login")']);
            if (loginButton) {
                await this.mcpClient.callTool('browser_click', {
                    element: 'login button',
                    ref: loginButton.ref
                });
                steps.push('clicked_login_button');
            }
        }
        
        // 4. 等待登录结果
        await this.sleep(3000);
        steps.push('waited_for_login_result');
        
        return {
            steps: steps,
            method: 'automated_login',
            credentialsProvided: !!(params.username && params.password)
        };
    }
    
    async executeWaitForUserLogin(params) {
        const timeout = params.timeout || 300000; // 默认5分钟
        const startTime = Date.now();
        const steps = [];
        
        logger.info(`⏳ 等待用户登录，超时时间: ${timeout}ms`);
        
        while (Date.now() - startTime < timeout) {
            // 检查页面变化，判断是否已登录
            const currentUrl = await this.getCurrentUrl();
            const pageContent = await this.getPageContent();
            
            // 简单的登录检测逻辑
            if (this.detectLoginSuccess(currentUrl, pageContent)) {
                steps.push('login_detected');
                return {
                    steps: steps,
                    method: 'user_manual_login',
                    waitTime: Date.now() - startTime,
                    success: true
                };
            }
            
            // 每5秒检查一次
            await this.sleep(5000);
            steps.push('checked_login_status');
        }
        
        // 超时
        throw new Error(`等待用户登录超时 (${timeout}ms)`);
    }
    
    async executeLogout(params) {
        const steps = [];
        
        // 查找登出按钮或链接
        const logoutElement = await this.findElement([
            'a:contains("登出")', 'a:contains("退出")', 'a:contains("Logout")',
            'button:contains("登出")', 'button:contains("退出")', 'button:contains("Logout")',
            '[data-action="logout"]', '.logout-btn'
        ]);
        
        if (logoutElement) {
            await this.mcpClient.callTool('browser_click', {
                element: 'logout button',
                ref: logoutElement.ref
            });
            steps.push('clicked_logout');
            
            // 等待登出完成
            await this.sleep(2000);
            steps.push('waited_for_logout');
        } else {
            throw new Error('未找到登出按钮');
        }
        
        return {
            steps: steps,
            method: 'click_logout_button'
        };
    }

    // ==================== 导航相关意图实现 ====================
    
    async executeNavigateToPage(params) {
        const steps = [];
        
        await this.mcpClient.callTool('browser_navigate', { url: params.url });
        steps.push('navigated_to_url');
        
        // 等待页面加载
        await this.sleep(3000);
        steps.push('waited_for_page_load');
        
        // 如果指定了等待元素，则等待该元素出现
        if (params.waitForSelector) {
            const element = await this.waitForElement(params.waitForSelector, 10000);
            if (element) {
                steps.push('target_element_found');
            }
        }
        
        return {
            steps: steps,
            targetUrl: params.url,
            finalUrl: await this.getCurrentUrl()
        };
    }
    
    async executeSelectStore(params) {
        const steps = [];
        const storeName = params.storeName;
        
        // 1. 查找门店搜索框
        const searchInput = await this.findElement([
            'input[placeholder*="门店"]', 'input[placeholder*="店铺"]', 
            'input[name*="store"]', '.store-search input'
        ]);
        
        if (searchInput) {
            await this.mcpClient.callTool('browser_type', {
                element: 'store search input',
                ref: searchInput.ref,
                text: storeName
            });
            steps.push('entered_store_name');
            
            // 等待搜索结果
            await this.sleep(2000);
            
            // 2. 查找并点击匹配的门店
            const storeOption = await this.findElement([
                `text:contains("${storeName}")`,
                `[data-store*="${storeName}"]`,
                `.store-option:contains("${storeName}")`
            ]);
            
            if (storeOption) {
                await this.mcpClient.callTool('browser_click', {
                    element: 'store option',
                    ref: storeOption.ref
                });
                steps.push('clicked_store_option');
                
                // 等待选择完成
                await this.sleep(3000);
                steps.push('waited_for_store_selection');
            } else {
                throw new Error(`未找到门店: ${storeName}`);
            }
        } else {
            throw new Error('未找到门店搜索框');
        }
        
        return {
            steps: steps,
            selectedStore: storeName
        };
    }

    // ==================== 系统操作相关意图实现 ====================

    async executeTakeScreenshot(params) {
        const steps = [];
        const filename = params.filename || `screenshot_${Date.now()}.png`;

        await this.mcpClient.callTool('browser_take_screenshot', {
            filename: filename
        });
        steps.push('screenshot_taken');

        return {
            steps: steps,
            filename: filename,
            fullPage: params.fullPage || true
        };
    }

    async executeWaitForElement(params) {
        const steps = [];
        const selector = params.selector;
        const timeout = params.timeout || 30000;

        // 这里需要实现等待元素的逻辑
        // 暂时返回成功
        steps.push('element_wait_attempted');

        return {
            steps: steps,
            selector: selector,
            timeout: timeout
        };
    }

    // ==================== 辅助方法 ====================
    
    async captureState(phase) {
        try {
            const screenshot = await this.mcpClient.callTool('browser_take_screenshot', {
                filename: `${phase}_${this.executionId}_${Date.now()}.png`
            });
            
            const snapshot = await this.mcpClient.callTool('browser_snapshot', {});
            
            return {
                phase: phase,
                timestamp: Date.now(),
                url: await this.getCurrentUrl(),
                screenshot: screenshot,
                domSnapshot: snapshot?.content?.[0]?.text || '',
                executionId: this.executionId
            };
        } catch (error) {
            logger.warn(`状态捕获失败 (${phase}):`, error.message);
            return {
                phase: phase,
                timestamp: Date.now(),
                error: error.message,
                executionId: this.executionId
            };
        }
    }
    
    async findElement(selectors) {
        // 这里需要实现元素查找逻辑
        // 暂时返回模拟结果
        return { ref: 'mock_element_ref' };
    }
    
    async getCurrentUrl() {
        try {
            const snapshot = await this.mcpClient.callTool('browser_snapshot', {});
            // 从快照中提取URL信息
            return 'current_url'; // 需要实际实现
        } catch (error) {
            return 'unknown_url';
        }
    }
    
    async getPageContent() {
        try {
            const snapshot = await this.mcpClient.callTool('browser_snapshot', {});
            return snapshot?.content?.[0]?.text || '';
        } catch (error) {
            return '';
        }
    }
    
    detectLoginSuccess(url, content) {
        // 简单的登录成功检测逻辑
        const loginSuccessIndicators = [
            'dashboard', 'main', 'home', '仪表盘', '主页',
            '欢迎', 'welcome', '管理', 'management'
        ];
        
        const loginFailureIndicators = [
            'login', '登录', 'signin', 'auth'
        ];
        
        const urlLower = url.toLowerCase();
        const contentLower = content.toLowerCase();
        
        // 检查URL和内容中的成功指示器
        const hasSuccessIndicator = loginSuccessIndicators.some(indicator => 
            urlLower.includes(indicator) || contentLower.includes(indicator)
        );
        
        // 检查是否还在登录页面
        const hasFailureIndicator = loginFailureIndicators.some(indicator => 
            urlLower.includes(indicator)
        );
        
        return hasSuccessIndicator && !hasFailureIndicator;
    }
    
    createSuccessEvidence(plan, beforeState, afterState, executionResult, startTime) {
        return {
            success: true,
            executionId: this.executionId,
            plan: plan,
            executionTime: Date.now() - startTime,
            beforeState: beforeState,
            afterState: afterState,
            executionResult: executionResult,
            timestamp: Date.now()
        };
    }
    
    createFailureEvidence(plan, beforeState, error, startTime, errorState = null) {
        return {
            success: false,
            executionId: this.executionId,
            plan: plan,
            executionTime: Date.now() - startTime,
            beforeState: beforeState,
            errorState: errorState,
            error: {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
            },
            timestamp: Date.now()
        };
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = ExecutorAgent;
