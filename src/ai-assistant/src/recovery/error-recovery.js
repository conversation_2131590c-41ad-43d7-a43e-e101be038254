/**
 * 错误恢复引擎 - AI-First RPA架构的自愈层
 * 负责智能错误分析，自动恢复策略，备选方案执行
 */

class ErrorRecovery {
    constructor(modelClient, page, logger = console) {
        this.modelClient = modelClient;
        this.page = page;
        this.logger = logger;
        this.recoveryHistory = [];
        this.maxRecoveryAttempts = 3;
    }

    /**
     * 创建恢复计划
     */
    async createRecoveryPlan(validationResult, executionResult, plannedAction, executionContext) {
        try {
            this.logger.info('🔧 开始创建错误恢复计划...');

            // 分析错误类型
            const errorAnalysis = this.analyzeError(validationResult, executionResult, plannedAction);
            
            // 检查是否可以恢复
            const canRecover = this.canRecover(errorAnalysis, executionContext);
            
            if (!canRecover.possible) {
                return {
                    canRecover: false,
                    reason: canRecover.reason,
                    strategy: 'abort',
                    confidence: 0.1
                };
            }

            // 生成恢复策略
            const recoveryStrategy = await this.generateRecoveryStrategy(errorAnalysis, executionContext);
            
            // 记录恢复历史
            this.recoveryHistory.push({
                error: errorAnalysis,
                strategy: recoveryStrategy,
                timestamp: new Date().toISOString()
            });

            this.logger.info(`✅ 恢复计划创建完成: ${recoveryStrategy.strategy}`);
            
            return recoveryStrategy;
        } catch (error) {
            this.logger.error('❌ 创建恢复计划失败:', error);
            return {
                canRecover: false,
                reason: `恢复计划创建失败: ${error.message}`,
                strategy: 'abort',
                confidence: 0.1
            };
        }
    }

    /**
     * 分析错误类型
     */
    analyzeError(validationResult, executionResult, plannedAction) {
        const analysis = {
            errorType: 'unknown',
            severity: 'medium',
            isRecoverable: true,
            errorMessage: '',
            context: {
                action: plannedAction.action_type,
                targetElement: plannedAction.target_element,
                validationResult,
                executionResult
            }
        };

        // 分析验证结果错误
        if (validationResult && !validationResult.isValid) {
            analysis.errorMessage = validationResult.error;
            
            switch (validationResult.validationType) {
                case 'execution_failure':
                    analysis.errorType = 'execution_failure';
                    analysis.severity = 'high';
                    analysis.isRecoverable = true;
                    break;
                
                case 'timeout':
                    analysis.errorType = 'timeout';
                    analysis.severity = 'medium';
                    analysis.isRecoverable = true;
                    break;
                
                case 'navigation_validation':
                    analysis.errorType = 'navigation_failure';
                    analysis.severity = 'high';
                    analysis.isRecoverable = true;
                    break;
                
                case 'click_validation':
                    analysis.errorType = 'click_failure';
                    analysis.severity = 'medium';
                    analysis.isRecoverable = true;
                    break;
                
                case 'type_validation':
                    analysis.errorType = 'input_failure';
                    analysis.severity = 'medium';
                    analysis.isRecoverable = true;
                    break;
                
                default:
                    analysis.errorType = 'validation_failure';
                    analysis.severity = 'medium';
                    analysis.isRecoverable = true;
            }
        }

        // 分析执行结果错误
        if (executionResult && !executionResult.success) {
            analysis.errorMessage = executionResult.message;
            
            if (executionResult.message.includes('timeout')) {
                analysis.errorType = 'timeout';
                analysis.severity = 'medium';
            } else if (executionResult.message.includes('element not found')) {
                analysis.errorType = 'element_not_found';
                analysis.severity = 'high';
            } else if (executionResult.message.includes('not visible')) {
                analysis.errorType = 'element_not_visible';
                analysis.severity = 'medium';
            } else if (executionResult.message.includes('not enabled')) {
                analysis.errorType = 'element_not_enabled';
                analysis.severity = 'medium';
            }
        }

        return analysis;
    }

    /**
     * 检查是否可以恢复
     */
    canRecover(errorAnalysis, executionContext) {
        // 检查恢复尝试次数
        const recoveryAttempts = this.recoveryHistory.length;
        if (recoveryAttempts >= this.maxRecoveryAttempts) {
            return {
                possible: false,
                reason: `已达到最大恢复尝试次数 (${this.maxRecoveryAttempts})`
            };
        }

        // 检查错误严重程度
        if (errorAnalysis.severity === 'critical') {
            return {
                possible: false,
                reason: '错误严重程度过高，无法恢复'
            };
        }

        // 检查是否为可恢复错误
        if (!errorAnalysis.isRecoverable) {
            return {
                possible: false,
                reason: '错误类型不可恢复'
            };
        }

        // 检查是否陷入循环
        const recentErrors = this.recoveryHistory.slice(-3);
        const sameErrorCount = recentErrors.filter(h => h.error.errorType === errorAnalysis.errorType).length;
        if (sameErrorCount >= 2) {
            return {
                possible: false,
                reason: '检测到错误循环，停止恢复尝试'
            };
        }

        return {
            possible: true,
            reason: '错误可以恢复'
        };
    }

    /**
     * 生成恢复策略
     */
    async generateRecoveryStrategy(errorAnalysis, executionContext) {
        try {
            // 根据错误类型选择恢复策略
            switch (errorAnalysis.errorType) {
                case 'timeout':
                    return this.createTimeoutRecoveryStrategy(errorAnalysis);
                
                case 'element_not_found':
                    return await this.createElementNotFoundRecoveryStrategy(errorAnalysis, executionContext);
                
                case 'element_not_visible':
                    return this.createElementNotVisibleRecoveryStrategy(errorAnalysis);
                
                case 'element_not_enabled':
                    return this.createElementNotEnabledRecoveryStrategy(errorAnalysis);
                
                case 'navigation_failure':
                    return this.createNavigationFailureRecoveryStrategy(errorAnalysis);
                
                case 'click_failure':
                    return this.createClickFailureRecoveryStrategy(errorAnalysis);
                
                case 'input_failure':
                    return this.createInputFailureRecoveryStrategy(errorAnalysis);
                
                default:
                    return this.createGenericRecoveryStrategy(errorAnalysis);
            }
        } catch (error) {
            this.logger.error('❌ 生成恢复策略失败:', error);
            return this.createGenericRecoveryStrategy(errorAnalysis);
        }
    }

    /**
     * 创建超时恢复策略
     */
    createTimeoutRecoveryStrategy(errorAnalysis) {
        return {
            canRecover: true,
            strategy: 'wait_and_retry',
            actions: [
                {
                    action_type: 'wait',
                    action_data: { timeout: 5000 },
                    reasoning: '等待页面稳定后重试'
                }
            ],
            confidence: 0.7,
            estimatedTime: 5,
            description: '超时错误恢复：等待页面稳定后重试原操作'
        };
    }

    /**
     * 创建元素未找到恢复策略
     */
    async createElementNotFoundRecoveryStrategy(errorAnalysis, executionContext) {
        const actions = [];

        // 1. 等待页面加载
        actions.push({
            action_type: 'wait',
            action_data: { timeout: 3000 },
            reasoning: '等待页面完全加载'
        });

        // 2. 刷新页面
        actions.push({
            action_type: 'refresh',
            reasoning: '刷新页面重新加载元素'
        });

        // 3. 使用AI重新分析页面寻找替代元素
        actions.push({
            action_type: 'ai_reanalyze',
            reasoning: '使用AI重新分析页面寻找目标元素或替代元素'
        });

        return {
            canRecover: true,
            strategy: 'element_recovery',
            actions: actions,
            confidence: 0.6,
            estimatedTime: 10,
            description: '元素未找到恢复：等待、刷新、重新分析'
        };
    }

    /**
     * 创建元素不可见恢复策略
     */
    createElementNotVisibleRecoveryStrategy(errorAnalysis) {
        return {
            canRecover: true,
            strategy: 'visibility_recovery',
            actions: [
                {
                    action_type: 'scroll',
                    action_data: { direction: 'down', amount: 300 },
                    reasoning: '滚动页面使元素可见'
                },
                {
                    action_type: 'wait',
                    action_data: { timeout: 2000 },
                    reasoning: '等待滚动完成'
                }
            ],
            confidence: 0.8,
            estimatedTime: 5,
            description: '元素不可见恢复：滚动页面使元素进入视野'
        };
    }

    /**
     * 创建元素不可用恢复策略
     */
    createElementNotEnabledRecoveryStrategy(errorAnalysis) {
        return {
            canRecover: true,
            strategy: 'enable_recovery',
            actions: [
                {
                    action_type: 'wait',
                    action_data: { timeout: 3000 },
                    reasoning: '等待元素变为可用状态'
                },
                {
                    action_type: 'check_prerequisites',
                    reasoning: '检查是否需要先完成其他操作'
                }
            ],
            confidence: 0.6,
            estimatedTime: 5,
            description: '元素不可用恢复：等待元素启用或检查前置条件'
        };
    }

    /**
     * 创建导航失败恢复策略
     */
    createNavigationFailureRecoveryStrategy(errorAnalysis) {
        return {
            canRecover: true,
            strategy: 'navigation_recovery',
            actions: [
                {
                    action_type: 'wait',
                    action_data: { timeout: 5000 },
                    reasoning: '等待网络连接稳定'
                },
                {
                    action_type: 'retry_navigation',
                    reasoning: '重试导航操作'
                }
            ],
            confidence: 0.7,
            estimatedTime: 8,
            description: '导航失败恢复：等待网络稳定后重试导航'
        };
    }

    /**
     * 创建点击失败恢复策略
     */
    createClickFailureRecoveryStrategy(errorAnalysis) {
        return {
            canRecover: true,
            strategy: 'click_recovery',
            actions: [
                {
                    action_type: 'wait',
                    action_data: { timeout: 2000 },
                    reasoning: '等待页面稳定'
                },
                {
                    action_type: 'alternative_click',
                    reasoning: '尝试使用不同的点击方式'
                }
            ],
            confidence: 0.7,
            estimatedTime: 5,
            description: '点击失败恢复：等待稳定后尝试替代点击方式'
        };
    }

    /**
     * 创建输入失败恢复策略
     */
    createInputFailureRecoveryStrategy(errorAnalysis) {
        return {
            canRecover: true,
            strategy: 'input_recovery',
            actions: [
                {
                    action_type: 'clear_and_retry',
                    reasoning: '清空输入框后重新输入'
                },
                {
                    action_type: 'focus_element',
                    reasoning: '确保输入框获得焦点'
                }
            ],
            confidence: 0.8,
            estimatedTime: 5,
            description: '输入失败恢复：清空重新输入并确保焦点'
        };
    }

    /**
     * 创建通用恢复策略
     */
    createGenericRecoveryStrategy(errorAnalysis) {
        return {
            canRecover: true,
            strategy: 'generic_recovery',
            actions: [
                {
                    action_type: 'wait',
                    action_data: { timeout: 3000 },
                    reasoning: '通用恢复：等待页面稳定'
                },
                {
                    action_type: 'screenshot',
                    action_data: { description: '错误恢复时的页面状态' },
                    reasoning: '记录当前页面状态'
                }
            ],
            confidence: 0.5,
            estimatedTime: 5,
            description: '通用错误恢复：等待和记录状态'
        };
    }

    /**
     * 执行恢复策略
     */
    async executeRecoveryStrategy(recoveryPlan, aiExecutionController) {
        try {
            this.logger.info(`🔧 执行恢复策略: ${recoveryPlan.strategy}`);

            const results = [];
            
            for (const action of recoveryPlan.actions) {
                try {
                    this.logger.info(`🔄 执行恢复操作: ${action.action_type}`);
                    
                    let result;
                    switch (action.action_type) {
                        case 'wait':
                            await this.page.waitForTimeout(action.action_data.timeout);
                            result = { success: true, message: `等待 ${action.action_data.timeout}ms` };
                            break;
                        
                        case 'refresh':
                            await this.page.reload();
                            result = { success: true, message: '页面刷新完成' };
                            break;
                        
                        case 'scroll':
                            await this.page.mouse.wheel(0, action.action_data.amount || 300);
                            result = { success: true, message: '页面滚动完成' };
                            break;
                        
                        case 'screenshot':
                            const screenshot = await this.page.screenshot();
                            result = { 
                                success: true, 
                                message: '恢复截图完成',
                                screenshot: screenshot.toString('base64')
                            };
                            break;
                        
                        default:
                            result = { success: true, message: `恢复操作 ${action.action_type} 已跳过` };
                    }
                    
                    results.push({
                        action: action,
                        result: result,
                        timestamp: new Date().toISOString()
                    });
                    
                } catch (actionError) {
                    this.logger.error(`❌ 恢复操作失败: ${action.action_type}`, actionError);
                    results.push({
                        action: action,
                        result: { success: false, error: actionError.message },
                        timestamp: new Date().toISOString()
                    });
                }
            }

            const successfulActions = results.filter(r => r.result.success);
            const success = successfulActions.length > 0;

            this.logger.info(`✅ 恢复策略执行完成，成功操作: ${successfulActions.length}/${results.length}`);

            return {
                success: success,
                strategy: recoveryPlan.strategy,
                results: results,
                message: success ? '恢复策略执行成功' : '恢复策略执行失败'
            };
        } catch (error) {
            this.logger.error('❌ 执行恢复策略失败:', error);
            return {
                success: false,
                strategy: recoveryPlan.strategy,
                results: [],
                error: error.message
            };
        }
    }

    /**
     * 获取恢复历史
     */
    getRecoveryHistory() {
        return this.recoveryHistory;
    }

    /**
     * 清除恢复历史
     */
    clearRecoveryHistory() {
        this.recoveryHistory = [];
    }

    /**
     * 分析恢复模式
     */
    analyzeRecoveryPatterns() {
        if (this.recoveryHistory.length === 0) {
            return null;
        }

        const errorTypes = this.recoveryHistory.map(h => h.error.errorType);
        const strategies = this.recoveryHistory.map(h => h.strategy.strategy);
        
        const errorFrequency = {};
        errorTypes.forEach(type => {
            errorFrequency[type] = (errorFrequency[type] || 0) + 1;
        });

        const mostCommonError = Object.keys(errorFrequency).reduce((a, b) => 
            errorFrequency[a] > errorFrequency[b] ? a : b
        );

        return {
            totalRecoveries: this.recoveryHistory.length,
            errorTypes: [...new Set(errorTypes)],
            strategies: [...new Set(strategies)],
            mostCommonError: mostCommonError,
            errorFrequency: errorFrequency,
            recentTrend: this.recoveryHistory.slice(-5).map(h => h.error.errorType)
        };
    }
}

module.exports = ErrorRecovery;
