/**
 * 工单轮询器 - 解决WebSocket连接问题的临时方案
 * 定期轮询工单系统，检查是否有新的待处理工单
 */

const axios = require('axios');
const logger = require('./utils/logger');

class TicketPoller {
    constructor(taskProcessor, workorderApiUrl) {
        this.taskProcessor = taskProcessor;
        this.workorderApiUrl = workorderApiUrl;
        this.pollingInterval = 10000; // 10秒轮询一次
        this.isPolling = false;
        this.processedTickets = new Set(); // 记录已处理的工单ID
    }

    /**
     * 开始轮询
     */
    start() {
        if (this.isPolling) {
            logger.warn('工单轮询器已经在运行中');
            return;
        }

        this.isPolling = true;
        logger.info('🔄 启动工单轮询器，每10秒检查一次新工单');
        
        // 立即执行一次
        this.poll();
        
        // 设置定时轮询
        this.intervalId = setInterval(() => {
            this.poll();
        }, this.pollingInterval);
    }

    /**
     * 停止轮询
     */
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.isPolling = false;
        logger.info('⏹️ 工单轮询器已停止');
    }

    /**
     * 执行一次轮询
     */
    async poll() {
        try {
            // 获取待处理的工单（排队中状态）
            const response = await axios.get(`${this.workorderApiUrl}/api/tickets`, {
                params: {
                    status: '排队中',
                    page: 1,
                    pageSize: 50
                }
            });

            const tickets = response.data.data || response.data.tickets || [];
            
            if (tickets.length === 0) {
                logger.debug('🔍 轮询检查：暂无待处理工单');
                return;
            }

            logger.info(`🎯 发现 ${tickets.length} 个待处理工单`);

            // 一次只处理一个工单，避免API频率限制
            for (const ticket of tickets) {
                if (!this.processedTickets.has(ticket.id)) {
                    logger.info(`📋 发现新工单: ${ticket.id} - ${ticket.title}`);

                    // 标记为已发现，避免重复处理
                    this.processedTickets.add(ticket.id);

                    // 同步处理工单，一次只处理一个
                    await this.processTicketAsync(ticket);

                    // 处理完一个工单后，跳出循环，等待下次轮询
                    break;
                }
            }

        } catch (error) {
            logger.error('❌ 工单轮询失败:', error.message);
        }
    }

    /**
     * 异步处理工单
     */
    async processTicketAsync(ticket) {
        try {
            logger.info(`🚀 开始处理工单: ${ticket.id}`);
            
            // 更新工单状态为处理中
            await axios.patch(`${this.workorderApiUrl}/api/tickets/${ticket.id}/status`, {
                status: '处理中',
                notes: 'AI-First RPA系统开始处理工单 (轮询模式)'
            });

            // 使用任务处理器处理工单
            const result = await this.taskProcessor.processTicket(ticket);
            
            if (result.success) {
                logger.info(`✅ 工单处理成功: ${ticket.id}`);
            } else {
                logger.error(`❌ 工单处理失败: ${ticket.id} - ${result.error}`);
            }

        } catch (error) {
            logger.error(`❌ 异步处理工单失败 ${ticket.id}:`, error);
            
            // 更新工单状态为失败
            try {
                await axios.patch(`${this.workorderApiUrl}/api/tickets/${ticket.id}/status`, {
                    status: '处理失败',
                    notes: `AI-First RPA系统异常: ${error.message}`
                });
            } catch (updateError) {
                logger.error('更新工单状态失败:', updateError);
            }
        }
    }

    /**
     * 获取轮询状态
     */
    getStatus() {
        return {
            isPolling: this.isPolling,
            pollingInterval: this.pollingInterval,
            processedCount: this.processedTickets.size,
            processedTickets: Array.from(this.processedTickets)
        };
    }

    /**
     * 清理已处理工单记录（可选）
     */
    clearProcessedTickets() {
        this.processedTickets.clear();
        logger.info('🧹 已清理处理记录');
    }
}

module.exports = TicketPoller;
