/**
 * 多Agent LangGraph任务处理器
 * 使用多Agent系统替代硬编码的执行逻辑
 */

const { WorkflowState } = require('./state/workflow-state');
const WorkflowStateManager = require('./state/workflow-state-manager');
const RPAWorkflow = require('./workflows/rpa-workflow');
const MultiAgentExecutionNodes = require('./nodes/multi-agent-execution-nodes');

// 导入MCP客户端
const { EnhancedSimpleMCPClient } = require('../playwright-mcp/enhanced-simple-mcp-client');

// 导入必要组件
const TicketManager = require('../../../shared/core/ticket-manager');
const MessageQueue = require('../../../shared/core/message-queue');
const logger = require('../utils/logger');

class MultiAgentLangGraphProcessor {
    constructor() {
        this.workorderApiUrl = process.env.WORKORDER_API_URL || 'http://localhost:3001';
        
        // 初始化MCP客户端
        this.mcpClient = new EnhancedSimpleMCPClient(logger);
        
        // 初始化依赖组件
        this.ticketManager = new TicketManager();
        this.messageQueue = new MessageQueue();
        this.stateManager = new WorkflowStateManager();
        
        // 创建依赖注入对象
        this.dependencies = {
            mcpClient: this.mcpClient,
            ticketManager: this.ticketManager,
            messageQueue: this.messageQueue,
            stateManager: this.stateManager,
            logger: logger
        };
        
        // 初始化多Agent执行节点
        this.agentNodes = new MultiAgentExecutionNodes(this.dependencies);
        
        // 初始化工作流（稍后创建）
        this.workflow = null;
        
        // 处理状态
        this.isProcessing = false;
        this.currentTicketId = null;
        
        logger.info('✅ 多Agent LangGraph处理器初始化完成');
    }

    /**
     * 获取处理器状态
     */
    getStatus() {
        return {
            isProcessing: this.isProcessing,
            currentTicketId: this.currentTicketId,
            processorType: 'MultiAgentLangGraphProcessor',
            agentNodes: this.agentNodes ? 'initialized' : 'not_initialized',
            mcpClient: this.mcpClient ? 'initialized' : 'not_initialized'
        };
    }

    /**
     * 获取队列大小
     */
    getQueueSize() {
        // 返回当前处理状态，MAS系统不使用传统队列
        return {
            pending: this.isProcessing ? 0 : 1,
            processing: this.isProcessing ? 1 : 0,
            total: this.isProcessing ? 1 : 0
        };
    }

    /**
     * 初始化处理器
     */
    async initialize() {
        try {
            logger.info('🚀 初始化多Agent处理器...');
            
            // 初始化MCP客户端
            await this.mcpClient.initialize();
            
            // 初始化数据库连接
            await this.ticketManager.initialize();
            await this.stateManager.initialize();
            
            // 创建工作流
            this.workflow = this.createAgentWorkflow();
            
            // 设置消息队列监听
            this.setupMessageHandlers();
            
            logger.info('✅ 多Agent处理器初始化成功');
            return true;
            
        } catch (error) {
            logger.error('❌ 多Agent处理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建Agent工作流
     */
    createAgentWorkflow() {
        const { Graph } = require('langraph');
        
        // 创建工作流图
        const workflow = new Graph({
            state: WorkflowState
        });

        // 添加节点
        workflow.addNode('start', async (state) => {
            logger.info('🎬 工作流开始', { ticketId: state.ticketId });
            state.updateCurrentNode('start');
            return state;
        });

        workflow.addNode('task_analysis', async (state) => {
            logger.info('🔍 任务分析', { ticketId: state.ticketId });
            state.updateCurrentNode('task_analysis');
            
            // 分析任务并创建任务队列
            const tasks = await this.analyzeTicketTasks(state.checkpointData.originalTicket);
            state.queuedTasks = tasks;
            
            return state;
        });

        workflow.addNode('agent_execution', 
            this.agentNodes.agentExecutionNode.bind(this.agentNodes));
        
        workflow.addNode('human_intervention',
            this.agentNodes.humanInterventionNode.bind(this.agentNodes));
            
        workflow.addNode('task_completion',
            this.agentNodes.taskCompletionNode.bind(this.agentNodes));
            
        workflow.addNode('retry_decision',
            this.agentNodes.retryDecisionNode.bind(this.agentNodes));
            
        workflow.addNode('final_summary',
            this.agentNodes.finalSummaryNode.bind(this.agentNodes));
            
        workflow.addNode('error_handling',
            this.agentNodes.errorHandlingNode.bind(this.agentNodes));
            
        workflow.addNode('cleanup', async (state) => {
            logger.info('🧹 清理资源', { ticketId: state.ticketId });
            state.updateCurrentNode('cleanup');
            
            // 保存最终状态
            await this.saveWorkflowState(state);
            
            return state;
        });

        workflow.addNode('end', async (state) => {
            logger.info('🏁 工作流结束', { 
                ticketId: state.ticketId,
                success: state.stats.successCount > 0
            });
            return state;
        });

        // 添加边
        workflow.addEdge('start', 'task_analysis');
        workflow.addEdge('task_analysis', 'agent_execution');
        
        // 条件边
        workflow.addConditionalEdge('agent_execution', (state) => {
            const lastResult = state.checkpointData.lastExecutionResult;
            if (lastResult?.needsHumanHelp) {
                return 'human_intervention';
            } else if (lastResult?.success) {
                return 'task_completion';
            } else {
                return 'retry_decision';
            }
        });

        workflow.addEdge('human_intervention', 'cleanup');
        
        workflow.addConditionalEdge('task_completion', (state) => {
            if (state.queuedTasks.length > 0) {
                return 'agent_execution';
            } else {
                return 'final_summary';
            }
        });

        workflow.addConditionalEdge('retry_decision', (state) => {
            const lastDecision = state.checkpointData.retryDecision;
            if (lastDecision?.retry) {
                return 'agent_execution';
            } else {
                return 'error_handling';
            }
        });

        workflow.addEdge('final_summary', 'cleanup');
        workflow.addEdge('error_handling', 'cleanup');
        workflow.addEdge('cleanup', 'end');

        // 设置入口点
        workflow.setEntry('start');

        return workflow.compile();
    }

    /**
     * 处理工单
     */
    async processTicket(ticket) {
        if (this.isProcessing) {
            logger.warn('处理器正忙，拒绝新任务', { ticketId: ticket.id });
            return { success: false, error: '处理器正忙' };
        }

        this.isProcessing = true;
        this.currentTicketId = ticket.id;

        try {
            logger.info('📥 开始处理工单', {
                ticketId: ticket.id,
                type: ticket.type,
                description: ticket.description
            });

            // 创建或恢复工作流状态
            const state = await this.createOrRestoreState(ticket);

            // 运行工作流
            const result = await this.workflow.run(state);

            logger.info('✅ 工单处理完成', {
                ticketId: ticket.id,
                success: result.stats.successCount > 0,
                duration: Date.now() - result.startTime
            });

            return {
                success: true,
                result: result,
                ticketId: ticket.id
            };

        } catch (error) {
            logger.error('❌ 工单处理失败:', error);
            
            // 更新工单状态
            await this.updateTicketStatus(ticket.id, '执行失败', {
                error_details: error.message,
                timestamp: new Date().toISOString()
            });

            return {
                success: false,
                error: error.message,
                ticketId: ticket.id
            };

        } finally {
            this.isProcessing = false;
            this.currentTicketId = null;
        }
    }

    /**
     * 创建或恢复工作流状态
     */
    async createOrRestoreState(ticket) {
        // 尝试恢复现有状态
        const existingState = await this.stateManager.getLatestState(ticket.id);
        
        if (existingState && existingState.status === 'suspended') {
            logger.info('恢复暂停的工作流', { ticketId: ticket.id });
            return WorkflowState.fromCheckpoint(existingState.state);
        }

        // 创建新状态
        const state = new WorkflowState(ticket.id);
        state.checkpointData.originalTicket = ticket;
        
        return state;
    }

    /**
     * AI-First分析工单任务 - 读取system_guide.md并智能解析
     */
    async analyzeTicketTasks(ticket) {
        try {
            logger.info('🧠 开始AI-First智能分析工单任务...');

            // 1. 读取系统操作指引
            const systemGuide = await this.loadSystemGuide();
            logger.info('📖 已加载系统操作指引');

            // 2. 使用OrchestratorAgent分析工单
            const orchestrator = this.agentNodes.orchestratorAgent;
            const analysis = await orchestrator.analyzeWorkOrder({
                id: ticket.id,
                title: ticket.title,
                content: ticket.content,
                systemGuide: systemGuide
            });

            logger.info('🎯 工单分析完成:', analysis);

            // 3. 返回分析结果作为任务队列
            return [{
                id: `task_${Date.now()}`,
                content: ticket.content,
                analysis: analysis,
                systemGuide: systemGuide,
                title: ticket.title || '执行RPA任务',
                metadata: {
                    ticketId: ticket.id,
                    priority: ticket.priority || 'medium'
                }
            }];
        } catch (error) {
            logger.error('❌ AI-First分析工单任务失败:', error);
            // 回退到简单实现
            return [{
                id: `task_${Date.now()}`,
                content: ticket.content,
                type: 'fallback',
                error: error.message
            }];
        }
    }

    /**
     * 加载系统操作指引
     */
    async loadSystemGuide() {
        try {
            const fs = require('fs').promises;
            const path = require('path');

            // system_guide.md的路径
            const guidePath = path.resolve(__dirname, '../../../../system_guide.md');
            const guideContent = await fs.readFile(guidePath, 'utf8');

            logger.info('📖 成功加载系统操作指引');
            return {
                content: guideContent,
                loadedAt: new Date().toISOString(),
                path: guidePath
            };
        } catch (error) {
            logger.error('❌ 加载系统操作指引失败:', error);
            return {
                content: '# 系统操作指引加载失败\n请检查system_guide.md文件是否存在',
                loadedAt: new Date().toISOString(),
                error: error.message
            };
        }
    }

    /**
     * 保存工作流状态
     */
    async saveWorkflowState(state) {
        try {
            await this.stateManager.saveState({
                ticketId: state.ticketId,
                workflowId: this.workflow.id,
                state: state.toCheckpoint(),
                status: state.suspended ? 'suspended' : 'completed',
                currentNode: state.currentNode,
                stats: state.stats,
                checkpointData: state.checkpointData
            });
        } catch (error) {
            logger.error('保存工作流状态失败:', error);
        }
    }

    /**
     * 设置消息处理器
     */
    setupMessageHandlers() {
        // 监听新工单
        this.messageQueue.on('new_ticket', async (ticket) => {
            logger.info('收到新工单', { ticketId: ticket.id });
            await this.processTicket(ticket);
        });

        // 监听恢复请求
        this.messageQueue.on('resume_workflow', async ({ ticketId }) => {
            logger.info('收到恢复请求', { ticketId });
            const ticket = await this.ticketManager.getTicketById(ticketId);
            if (ticket) {
                await this.processTicket(ticket);
            }
        });
    }

    /**
     * 更新工单状态
     */
    async updateTicketStatus(ticketId, status, details = {}) {
        try {
            await this.ticketManager.updateTicketStatus(ticketId, status, details);
            
            // 通过消息队列通知
            this.messageQueue.emit('ticket_status_updated', {
                ticketId,
                status,
                details,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            logger.error('更新工单状态失败:', error);
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        logger.info('🧹 清理多Agent处理器资源...');
        
        if (this.mcpClient) {
            await this.mcpClient.close();
        }
        
        if (this.ticketManager) {
            await this.ticketManager.close();
        }
        
        if (this.stateManager) {
            await this.stateManager.close();
        }
        
        logger.info('✅ 资源清理完成');
    }
}

module.exports = MultiAgentLangGraphProcessor;