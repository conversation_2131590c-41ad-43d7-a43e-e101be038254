/**
 * 自适应提示词系统
 * 根据历史成功率和复杂度动态调整提示词
 */

const logger = require('../../utils/logger');

class AdaptivePromptSystem {
    constructor() {
        // 提示词模板库
        this.templates = {
            planning: {
                BASIC: 'basic_planning_template',
                ENHANCED: 'enhanced_planning_template',
                ADVANCED: 'advanced_planning_template'
            },
            validation: {
                L2: {
                    BASIC: 'l2_basic_validation_template',
                    ENHANCED: 'l2_enhanced_validation_template',
                    ADVANCED: 'l2_advanced_validation_template'
                },
                L3: {
                    BASIC: 'l3_basic_validation_template',
                    ENHANCED: 'l3_enhanced_validation_template',
                    ADVANCED: 'l3_advanced_validation_template'
                }
            },
            recovery: {
                BASIC: 'basic_recovery_template',
                ENHANCED: 'enhanced_recovery_template',
                ADVANCED: 'advanced_recovery_template'
            }
        };
        
        // 成功率追踪
        this.successRates = {
            planning: { attempts: 0, successes: 0, rate: 0.8 },
            validation: { attempts: 0, successes: 0, rate: 0.8 },
            recovery: { attempts: 0, successes: 0, rate: 0.6 }
        };
        
        // 复杂度评估器
        this.complexityAssessor = new ComplexityAssessor();
        
        // 配置
        this.config = {
            SUCCESS_RATE_THRESHOLD: 0.7,
            MIN_ATTEMPTS_FOR_ADJUSTMENT: 3,
            COMPLEXITY_THRESHOLD_BASIC: 0.3,
            COMPLEXITY_THRESHOLD_ADVANCED: 0.7
        };
    }
    
    /**
     * 生成自适应规划提示词
     */
    generatePlanningPrompt(state) {
        try {
            // 评估复杂度
            const complexity = this.complexityAssessor.assessPlanningComplexity(state);
            
            // 获取当前成功率
            const successRate = this.successRates.planning.rate;
            
            // 选择模板级别
            let templateLevel = this.selectTemplateLevel(complexity.level, successRate);
            
            // 获取基础模板
            let baseTemplate = this.getPlanningTemplate(templateLevel);
            
            // 如果成功率低，增强提示词
            if (successRate < this.config.SUCCESS_RATE_THRESHOLD) {
                baseTemplate = this.enhancePromptForLowSuccessRate(baseTemplate, 'planning');
            }
            
            // 应用上下文信息
            const contextualPrompt = this.applyContextualInformation(baseTemplate, state);
            
            logger.info('生成自适应规划提示词', {
                complexity: complexity.level,
                successRate: successRate,
                templateLevel: templateLevel,
                promptLength: contextualPrompt.length
            });
            
            return contextualPrompt;
            
        } catch (error) {
            logger.error('生成规划提示词失败:', error);
            return this.getFallbackPlanningPrompt(state);
        }
    }
    
    /**
     * 生成自适应验证提示词
     */
    generateValidationPrompt(layer, state, plan, evidence) {
        try {
            // 评估复杂度
            const complexity = this.complexityAssessor.assessValidationComplexity(state, plan, evidence);
            
            // 获取当前成功率
            const successRate = this.successRates.validation.rate;
            
            // 选择模板级别
            let templateLevel = this.selectTemplateLevel(complexity.level, successRate);
            
            // 获取基础模板
            let baseTemplate = this.getValidationTemplate(layer, templateLevel);
            
            // 如果成功率低，增强提示词
            if (successRate < this.config.SUCCESS_RATE_THRESHOLD) {
                baseTemplate = this.enhancePromptForLowSuccessRate(baseTemplate, 'validation');
            }
            
            // 应用上下文信息
            const contextualPrompt = this.applyValidationContext(baseTemplate, state, plan, evidence);
            
            logger.info(`生成自适应${layer}验证提示词`, {
                complexity: complexity.level,
                successRate: successRate,
                templateLevel: templateLevel,
                promptLength: contextualPrompt.length
            });
            
            return contextualPrompt;
            
        } catch (error) {
            logger.error(`生成${layer}验证提示词失败:`, error);
            return this.getFallbackValidationPrompt(layer, state, plan, evidence);
        }
    }
    
    /**
     * 生成自适应恢复提示词
     */
    generateRecoveryPrompt(error, context) {
        try {
            // 评估复杂度
            const complexity = this.complexityAssessor.assessRecoveryComplexity(error, context);
            
            // 获取当前成功率
            const successRate = this.successRates.recovery.rate;
            
            // 选择模板级别
            let templateLevel = this.selectTemplateLevel(complexity.level, successRate);
            
            // 获取基础模板
            let baseTemplate = this.getRecoveryTemplate(templateLevel);
            
            // 如果成功率低，增强提示词
            if (successRate < this.config.SUCCESS_RATE_THRESHOLD) {
                baseTemplate = this.enhancePromptForLowSuccessRate(baseTemplate, 'recovery');
            }
            
            // 应用上下文信息
            const contextualPrompt = this.applyRecoveryContext(baseTemplate, error, context);
            
            logger.info('生成自适应恢复提示词', {
                complexity: complexity.level,
                successRate: successRate,
                templateLevel: templateLevel,
                promptLength: contextualPrompt.length
            });
            
            return contextualPrompt;
            
        } catch (error) {
            logger.error('生成恢复提示词失败:', error);
            return this.getFallbackRecoveryPrompt(error, context);
        }
    }
    
    /**
     * 更新成功率
     */
    updateSuccessRate(promptType, success) {
        if (this.successRates[promptType]) {
            this.successRates[promptType].attempts++;
            if (success) {
                this.successRates[promptType].successes++;
            }
            
            // 重新计算成功率
            this.successRates[promptType].rate = 
                this.successRates[promptType].successes / this.successRates[promptType].attempts;
            
            logger.info(`更新${promptType}成功率`, {
                attempts: this.successRates[promptType].attempts,
                successes: this.successRates[promptType].successes,
                rate: this.successRates[promptType].rate
            });
        }
    }
    
    /**
     * 选择模板级别
     */
    selectTemplateLevel(complexityLevel, successRate) {
        // 如果成功率低，提升模板级别
        if (successRate < this.config.SUCCESS_RATE_THRESHOLD) {
            if (complexityLevel === 'LOW') return 'ENHANCED';
            if (complexityLevel === 'MEDIUM') return 'ADVANCED';
            if (complexityLevel === 'HIGH') return 'ADVANCED';
        }
        
        // 根据复杂度选择模板
        if (complexityLevel === 'LOW') return 'BASIC';
        if (complexityLevel === 'MEDIUM') return 'ENHANCED';
        if (complexityLevel === 'HIGH') return 'ADVANCED';
        
        return 'BASIC';
    }
    
    /**
     * 为低成功率增强提示词
     */
    enhancePromptForLowSuccessRate(baseTemplate, promptType) {
        const enhancements = {
            planning: [
                '注意：当前执行成功率较低，请特别注意以下几点：',
                '1. 仔细分析历史失败原因',
                '2. 选择更保守和可靠的操作',
                '3. 提高操作的精确性',
                '4. 考虑分步骤执行复杂操作'
            ],
            validation: [
                '注意：当前验证成功率较低，请加强验证标准：',
                '1. 提高验证的严格性',
                '2. 检查更多的验证维度',
                '3. 对边缘情况进行额外检查',
                '4. 降低验证通过的阈值'
            ],
            recovery: [
                '注意：当前恢复成功率较低，请采用更保守的恢复策略：',
                '1. 采用多层次恢复机制',
                '2. 增加恢复重试次数',
                '3. 提供更详细的恢复建议',
                '4. 考虑请求用户干预'
            ]
        };
        
        const enhancement = enhancements[promptType] || [];
        return baseTemplate + '\n\n' + enhancement.join('\n');
    }
    
    /**
     * 应用上下文信息
     */
    applyContextualInformation(template, state) {
        const context = {
            task_goal: state.taskGoal || '未指定任务目标',
            cycle_count: state.cycleCount || 0,
            max_cycles: state.maxCycles || 10,
            success_rate: (this.successRates.planning.rate * 100).toFixed(1) + '%',
            execution_history: this.formatExecutionHistory(state.executionContext?.evidenceHistory || []),
            error_history: this.formatErrorHistory(state.errorHistory || []),
            current_state: JSON.stringify(state.executionContext?.browserState || {}, null, 2)
        };
        
        let contextualPrompt = template;
        
        // 替换模板变量
        Object.keys(context).forEach(key => {
            const placeholder = `\\$\\{${key}\\}`;
            contextualPrompt = contextualPrompt.replace(new RegExp(placeholder, 'g'), context[key]);
        });
        
        return contextualPrompt;
    }
    
    /**
     * 应用验证上下文
     */
    applyValidationContext(template, state, plan, evidence) {
        const context = {
            task_goal: state.taskGoal || '未指定任务目标',
            cycle_count: state.cycleCount || 0,
            plan_intent: plan.intent || '未知意图',
            plan_parameters: JSON.stringify(plan.parameters || {}, null, 2),
            plan_reasoning: plan.reasoning || '无规划理由',
            execution_success: evidence.success ? '成功' : '失败',
            execution_duration: evidence.duration || 0,
            execution_result: JSON.stringify(evidence.executionResult || {}, null, 2)
        };
        
        let contextualPrompt = template;
        
        // 替换模板变量
        Object.keys(context).forEach(key => {
            const placeholder = `\\$\\{${key}\\}`;
            contextualPrompt = contextualPrompt.replace(new RegExp(placeholder, 'g'), context[key]);
        });
        
        return contextualPrompt;
    }
    
    /**
     * 应用恢复上下文
     */
    applyRecoveryContext(template, error, context) {
        const recoveryContext = {
            error_message: error.message || '未知错误',
            error_type: error.type || 'unknown',
            current_node: context.currentNode || '未知节点',
            ticket_id: context.ticketId || '未知工单',
            context_info: JSON.stringify(context, null, 2)
        };
        
        let contextualPrompt = template;
        
        // 替换模板变量
        Object.keys(recoveryContext).forEach(key => {
            const placeholder = `\\$\\{${key}\\}`;
            contextualPrompt = contextualPrompt.replace(new RegExp(placeholder, 'g'), recoveryContext[key]);
        });
        
        return contextualPrompt;
    }
    
    /**
     * 格式化执行历史
     */
    formatExecutionHistory(history) {
        if (!history || history.length === 0) {
            return '暂无执行历史';
        }
        
        return history.slice(-5).map((item, index) => {
            const cycle = item.cycle || index + 1;
            const intent = item.plan?.intent || '未知操作';
            const success = item.evidence?.success ? '成功' : '失败';
            const validation = item.validation?.status || '未验证';
            
            return `${cycle}. ${intent} - ${success} (验证: ${validation})`;
        }).join('\n');
    }
    
    /**
     * 格式化错误历史
     */
    formatErrorHistory(errorHistory) {
        if (!errorHistory || errorHistory.length === 0) {
            return '暂无错误历史';
        }
        
        return errorHistory.slice(-3).map((error, index) => {
            const message = error.error || error.message || '未知错误';
            const node = error.node || '未知节点';
            
            return `${index + 1}. ${node}: ${message}`;
        }).join('\n');
    }
    
    /**
     * 获取规划模板
     */
    getPlanningTemplate(level) {
        switch (level) {
            case 'BASIC':
                return this.getBasicPlanningTemplate();
            case 'ENHANCED':
                return this.getEnhancedPlanningTemplate();
            case 'ADVANCED':
                return this.getAdvancedPlanningTemplate();
            default:
                return this.getBasicPlanningTemplate();
        }
    }
    
    /**
     * 获取验证模板
     */
    getValidationTemplate(layer, level) {
        if (layer === 'L2') {
            switch (level) {
                case 'BASIC':
                    return this.getL2BasicValidationTemplate();
                case 'ENHANCED':
                    return this.getL2EnhancedValidationTemplate();
                case 'ADVANCED':
                    return this.getL2AdvancedValidationTemplate();
                default:
                    return this.getL2BasicValidationTemplate();
            }
        } else if (layer === 'L3') {
            switch (level) {
                case 'BASIC':
                    return this.getL3BasicValidationTemplate();
                case 'ENHANCED':
                    return this.getL3EnhancedValidationTemplate();
                case 'ADVANCED':
                    return this.getL3AdvancedValidationTemplate();
                default:
                    return this.getL3BasicValidationTemplate();
            }
        }
        
        return this.getL2BasicValidationTemplate();
    }
    
    /**
     * 获取恢复模板
     */
    getRecoveryTemplate(level) {
        switch (level) {
            case 'BASIC':
                return this.getBasicRecoveryTemplate();
            case 'ENHANCED':
                return this.getEnhancedRecoveryTemplate();
            case 'ADVANCED':
                return this.getAdvancedRecoveryTemplate();
            default:
                return this.getBasicRecoveryTemplate();
        }
    }
    
    // === 模板定义 ===
    
    getBasicPlanningTemplate() {
        return `# RPA任务规划器

## 当前任务目标
\${task_goal}

## 当前状态
- 执行周期: \${cycle_count}/\${max_cycles}
- 当前成功率: \${success_rate}

## 执行历史
\${execution_history}

请分析当前状态并规划下一步操作。返回JSON格式：
{
  "intent": "操作意图",
  "parameters": {},
  "reasoning": "规划理由",
  "confidence": 0.8
}`;
    }
    
    getEnhancedPlanningTemplate() {
        return `# RPA任务规划器 - 增强版

## 当前任务目标
\${task_goal}

## 当前状态
- 执行周期: \${cycle_count}/\${max_cycles}
- 当前成功率: \${success_rate}

## 执行历史
\${execution_history}

## 错误历史
\${error_history}

## 详细分析要求
1. 分析当前状态和历史执行情况
2. 识别可能的风险和挑战
3. 选择最佳的操作策略
4. 评估操作的可行性和风险

请分析当前状态并规划下一步操作。返回JSON格式：
{
  "intent": "操作意图",
  "parameters": {},
  "reasoning": "详细规划理由",
  "confidence": 0.8,
  "risk_assessment": "风险评估",
  "alternative_plans": "备选方案"
}`;
    }
    
    getAdvancedPlanningTemplate() {
        return `# RPA任务规划器 - 高级版

## 当前任务目标
\${task_goal}

## 当前状态
- 执行周期: \${cycle_count}/\${max_cycles}
- 当前成功率: \${success_rate}

## 执行历史
\${execution_history}

## 错误历史
\${error_history}

## 当前页面状态
\${current_state}

## 高级分析要求
1. 深度分析当前状态和历史执行情况
2. 识别潜在的风险和挑战
3. 制定多层次的操作策略
4. 评估操作的可行性、风险和收益
5. 考虑异常情况的处理方案
6. 优化操作的执行效率

请进行深度分析并制定最优的执行计划。返回JSON格式：
{
  "intent": "操作意图",
  "parameters": {},
  "reasoning": "深度分析和规划理由",
  "confidence": 0.8,
  "risk_assessment": "详细风险评估",
  "alternative_plans": "多个备选方案",
  "optimization_suggestions": "优化建议",
  "contingency_plans": "应急预案"
}`;
    }
    
    getL2BasicValidationTemplate() {
        return `# L2功能层验证

## 执行计划
- 意图: \${plan_intent}
- 参数: \${plan_parameters}
- 理由: \${plan_reasoning}

## 执行结果
- 成功: \${execution_success}
- 耗时: \${execution_duration}ms
- 结果: \${execution_result}

## 任务目标
\${task_goal}

请验证执行结果的业务相关性。返回JSON格式：
{
  "goalRelevance": 0.8,
  "stateConsistency": 0.9,
  "sideEffectCheck": 0.95,
  "progressAssessment": 0.7,
  "score": 0.85,
  "reason": "验证理由",
  "suggestions": "改进建议"
}`;
    }
    
    getL2EnhancedValidationTemplate() {
        return `# L2功能层验证 - 增强版

## 执行计划
- 意图: \${plan_intent}
- 参数: \${plan_parameters}
- 理由: \${plan_reasoning}

## 执行结果
- 成功: \${execution_success}
- 耗时: \${execution_duration}ms
- 结果: \${execution_result}

## 任务目标
\${task_goal}

## 当前周期
\${cycle_count}/\${max_cycles}

## 增强验证要求
1. 深度分析目标相关性
2. 检查状态一致性
3. 评估副作用影响
4. 分析进度贡献度
5. 识别潜在问题

请进行增强的业务相关性验证。返回JSON格式：
{
  "goalRelevance": 0.8,
  "stateConsistency": 0.9,
  "sideEffectCheck": 0.95,
  "progressAssessment": 0.7,
  "score": 0.85,
  "reason": "详细验证理由",
  "suggestions": "具体改进建议",
  "riskFactors": "风险因素分析",
  "qualityScore": 0.8
}`;
    }
    
    getL2AdvancedValidationTemplate() {
        return `# L2功能层验证 - 高级版

## 执行计划
- 意图: \${plan_intent}
- 参数: \${plan_parameters}
- 理由: \${plan_reasoning}

## 执行结果
- 成功: \${execution_success}
- 耗时: \${execution_duration}ms
- 结果: \${execution_result}

## 任务目标
\${task_goal}

## 当前周期
\${cycle_count}/\${max_cycles}

## 高级验证要求
1. 全面分析目标相关性和对齐度
2. 深度检查状态一致性和完整性
3. 全面评估副作用和连锁影响
4. 量化分析进度贡献度和效率
5. 识别和评估所有潜在风险
6. 提供优化建议和改进方向

请进行高级的业务相关性验证。返回JSON格式：
{
  "goalRelevance": 0.8,
  "stateConsistency": 0.9,
  "sideEffectCheck": 0.95,
  "progressAssessment": 0.7,
  "score": 0.85,
  "reason": "深度验证理由",
  "suggestions": "详细改进建议",
  "riskFactors": "全面风险因素分析",
  "qualityScore": 0.8,
  "efficiencyScore": 0.85,
  "optimizationRecommendations": "优化建议",
  "strategicInsights": "战略洞察"
}`;
    }
    
    getL3BasicValidationTemplate() {
        return `# L3智能层验证

## 任务目标
\${task_goal}

## 当前周期
\${cycle_count}/\${max_cycles}

请分析任务完成情况和上下文连贯性。返回JSON格式：
{
  "completionAssessment": 0.7,
  "pathOptimality": 0.8,
  "contextCoherence": 0.85,
  "completionProbability": 0.75,
  "score": 0.8,
  "reason": "智能分析理由",
  "nextStepRecommendation": "CONTINUE",
  "strategicAdvice": "战略建议"
}`;
    }
    
    getL3EnhancedValidationTemplate() {
        return `# L3智能层验证 - 增强版

## 任务目标
\${task_goal}

## 当前周期
\${cycle_count}/\${max_cycles}

## 执行计划
- 意图: \${plan_intent}
- 理由: \${plan_reasoning}

## 执行结果
- 成功: \${execution_success}
- 耗时: \${execution_duration}ms

## 增强分析要求
1. 深度评估任务完成度
2. 分析执行路径的优化程度
3. 检查上下文连贯性
4. 预测任务完成概率
5. 提供战略性建议

请进行增强的智能层验证。返回JSON格式：
{
  "completionAssessment": 0.7,
  "pathOptimality": 0.8,
  "contextCoherence": 0.85,
  "completionProbability": 0.75,
  "score": 0.8,
  "reason": "详细智能分析理由",
  "nextStepRecommendation": "CONTINUE",
  "strategicAdvice": "详细战略建议",
  "optimizationSuggestions": "优化建议",
  "contextualInsights": "上下文洞察"
}`;
    }
    
    getL3AdvancedValidationTemplate() {
        return `# L3智能层验证 - 高级版

## 任务目标
\${task_goal}

## 当前周期
\${cycle_count}/\${max_cycles}

## 执行计划
- 意图: \${plan_intent}
- 理由: \${plan_reasoning}

## 执行结果
- 成功: \${execution_success}
- 耗时: \${execution_duration}ms

## 高级分析要求
1. 全面评估任务完成度和质量
2. 深度分析执行路径的优化程度和效率
3. 全面检查上下文连贯性和一致性
4. 精确预测任务完成概率和时间
5. 提供多维度的战略性建议
6. 识别潜在的改进机会

请进行高级的智能层验证。返回JSON格式：
{
  "completionAssessment": 0.7,
  "pathOptimality": 0.8,
  "contextCoherence": 0.85,
  "completionProbability": 0.75,
  "score": 0.8,
  "reason": "深度智能分析理由",
  "nextStepRecommendation": "CONTINUE",
  "strategicAdvice": "全面战略建议",
  "optimizationSuggestions": "详细优化建议",
  "contextualInsights": "深度上下文洞察",
  "riskAssessment": "风险评估",
  "improvementOpportunities": "改进机会",
  "performanceMetrics": "性能指标"
}`;
    }
    
    getBasicRecoveryTemplate() {
        return `# 错误恢复处理

## 错误信息
- 错误消息: \${error_message}
- 错误类型: \${error_type}
- 发生节点: \${current_node}

请分析错误并提供恢复建议。返回JSON格式：
{
  "errorAnalysis": "错误分析",
  "recoveryStrategy": "恢复策略",
  "confidence": 0.7,
  "estimatedTime": "预计时间",
  "recommendations": "建议"
}`;
    }
    
    getEnhancedRecoveryTemplate() {
        return `# 错误恢复处理 - 增强版

## 错误信息
- 错误消息: \${error_message}
- 错误类型: \${error_type}
- 发生节点: \${current_node}
- 工单ID: \${ticket_id}

## 上下文信息
\${context_info}

## 增强分析要求
1. 深度分析错误原因
2. 制定多层次恢复策略
3. 评估恢复可行性
4. 预估恢复时间和成本

请进行增强的错误恢复分析。返回JSON格式：
{
  "errorAnalysis": "详细错误分析",
  "recoveryStrategy": "多层次恢复策略",
  "confidence": 0.7,
  "estimatedTime": "预计时间",
  "recommendations": "详细建议",
  "alternativeStrategies": "备选策略",
  "riskAssessment": "风险评估"
}`;
    }
    
    getAdvancedRecoveryTemplate() {
        return `# 错误恢复处理 - 高级版

## 错误信息
- 错误消息: \${error_message}
- 错误类型: \${error_type}
- 发生节点: \${current_node}
- 工单ID: \${ticket_id}

## 上下文信息
\${context_info}

## 高级分析要求
1. 全面分析错误根本原因
2. 制定多维度恢复策略
3. 评估恢复可行性和成本
4. 预估恢复时间和资源需求
5. 提供预防性建议
6. 制定应急预案

请进行高级的错误恢复分析。返回JSON格式：
{
  "errorAnalysis": "深度错误分析",
  "recoveryStrategy": "多维度恢复策略",
  "confidence": 0.7,
  "estimatedTime": "预计时间",
  "recommendations": "全面建议",
  "alternativeStrategies": "多个备选策略",
  "riskAssessment": "详细风险评估",
  "preventiveMeasures": "预防措施",
  "contingencyPlan": "应急预案",
  "resourceRequirements": "资源需求"
}`;
    }
    
    // === 降级模板 ===
    
    getFallbackPlanningPrompt(state) {
        return `# RPA任务规划器 - 降级版

## 任务目标
${state.taskGoal || '完成RPA任务'}

## 当前状态
- 执行周期: ${state.cycleCount || 0}/${state.maxCycles || 10}

请规划下一步操作。返回JSON格式：
{
  "intent": "SCREENSHOT",
  "parameters": {},
  "reasoning": "降级模式，执行安全操作",
  "confidence": 0.5
}`;
    }
    
    getFallbackValidationPrompt(layer, state, plan, evidence) {
        return `# ${layer}验证 - 降级版

## 基础验证
请对执行结果进行基础验证。返回JSON格式：
{
  "score": 0.6,
  "reason": "降级模式验证",
  "suggestions": "建议继续执行"
}`;
    }
    
    getFallbackRecoveryPrompt(error, context) {
        return `# 错误恢复 - 降级版

## 错误信息
${error.message || '未知错误'}

请提供基础恢复建议。返回JSON格式：
{
  "errorAnalysis": "基础错误分析",
  "recoveryStrategy": "基础恢复策略",
  "confidence": 0.4,
  "recommendations": "建议重试或请求用户干预"
}`;
    }
    
    /**
     * 获取提示词统计
     */
    getPromptStats() {
        return {
            successRates: this.successRates,
            totalAttempts: Object.values(this.successRates).reduce((sum, rate) => sum + rate.attempts, 0),
            averageSuccessRate: Object.values(this.successRates).reduce((sum, rate) => sum + rate.rate, 0) / Object.keys(this.successRates).length
        };
    }
    
    /**
     * 重置统计
     */
    resetStats() {
        this.successRates = {
            planning: { attempts: 0, successes: 0, rate: 0.8 },
            validation: { attempts: 0, successes: 0, rate: 0.8 },
            recovery: { attempts: 0, successes: 0, rate: 0.6 }
        };
    }
}

/**
 * 复杂度评估器
 */
class ComplexityAssessor {
    constructor() {
        this.complexityFactors = {
            cycleCount: { weight: 0.2, threshold: 5 },
            errorHistory: { weight: 0.3, threshold: 3 },
            evidenceHistory: { weight: 0.1, threshold: 10 },
            taskComplexity: { weight: 0.4, threshold: 0.5 }
        };
    }
    
    /**
     * 评估规划复杂度
     */
    assessPlanningComplexity(state) {
        let complexityScore = 0;
        
        // 周期数影响
        const cycleRatio = (state.cycleCount || 0) / (state.maxCycles || 10);
        complexityScore += this.complexityFactors.cycleCount.weight * cycleRatio;
        
        // 错误历史影响
        const errorCount = (state.errorHistory || []).length;
        const errorRatio = Math.min(errorCount / this.complexityFactors.errorHistory.threshold, 1);
        complexityScore += this.complexityFactors.errorHistory.weight * errorRatio;
        
        // 执行历史影响
        const evidenceCount = (state.executionContext?.evidenceHistory || []).length;
        const evidenceRatio = Math.min(evidenceCount / this.complexityFactors.evidenceHistory.threshold, 1);
        complexityScore += this.complexityFactors.evidenceHistory.weight * evidenceRatio;
        
        // 任务复杂度
        const taskComplexity = this.assessTaskComplexity(state.taskGoal || '');
        complexityScore += this.complexityFactors.taskComplexity.weight * taskComplexity;
        
        return {
            score: complexityScore,
            level: this.getComplexityLevel(complexityScore),
            factors: {
                cycleRatio,
                errorRatio,
                evidenceRatio,
                taskComplexity
            }
        };
    }
    
    /**
     * 评估验证复杂度
     */
    assessValidationComplexity(state, plan, evidence) {
        let complexityScore = 0;
        
        // 基础复杂度
        const planningComplexity = this.assessPlanningComplexity(state);
        complexityScore += planningComplexity.score * 0.5;
        
        // 执行结果复杂度
        const executionComplexity = this.assessExecutionComplexity(plan, evidence);
        complexityScore += executionComplexity * 0.3;
        
        // 验证历史复杂度
        const validationHistoryComplexity = this.assessValidationHistoryComplexity(state);
        complexityScore += validationHistoryComplexity * 0.2;
        
        return {
            score: complexityScore,
            level: this.getComplexityLevel(complexityScore),
            factors: {
                planningComplexity: planningComplexity.score,
                executionComplexity,
                validationHistoryComplexity
            }
        };
    }
    
    /**
     * 评估恢复复杂度
     */
    assessRecoveryComplexity(error, context) {
        let complexityScore = 0;
        
        // 错误类型复杂度
        const errorTypeComplexity = this.assessErrorTypeComplexity(error);
        complexityScore += errorTypeComplexity * 0.4;
        
        // 上下文复杂度
        const contextComplexity = this.assessContextComplexity(context);
        complexityScore += contextComplexity * 0.3;
        
        // 恢复历史复杂度
        const recoveryHistoryComplexity = this.assessRecoveryHistoryComplexity(context);
        complexityScore += recoveryHistoryComplexity * 0.3;
        
        return {
            score: complexityScore,
            level: this.getComplexityLevel(complexityScore),
            factors: {
                errorTypeComplexity,
                contextComplexity,
                recoveryHistoryComplexity
            }
        };
    }
    
    /**
     * 评估任务复杂度
     */
    assessTaskComplexity(taskGoal) {
        const complexityKeywords = {
            high: ['多步骤', '复杂', '批量', '条件判断', '循环', '异步'],
            medium: ['查询', '修改', '上传', '下载', '表单', '验证'],
            low: ['点击', '输入', '截图', '导航', '等待', '简单']
        };
        
        const goal = taskGoal.toLowerCase();
        
        if (complexityKeywords.high.some(keyword => goal.includes(keyword))) {
            return 0.8;
        } else if (complexityKeywords.medium.some(keyword => goal.includes(keyword))) {
            return 0.5;
        } else if (complexityKeywords.low.some(keyword => goal.includes(keyword))) {
            return 0.2;
        }
        
        return 0.4; // 默认中等复杂度
    }
    
    /**
     * 评估执行复杂度
     */
    assessExecutionComplexity(plan, evidence) {
        let complexity = 0;
        
        // 意图复杂度
        const intentComplexity = {
            'NAVIGATE': 0.3,
            'CLICK': 0.2,
            'TYPE': 0.3,
            'WAIT': 0.1,
            'SCREENSHOT': 0.1,
            'TASK_COMPLETED': 0.1,
            'USER_INTERVENTION_REQUIRED': 0.8
        };
        
        complexity += intentComplexity[plan.intent] || 0.4;
        
        // 执行结果复杂度
        if (!evidence.success) {
            complexity += 0.3;
        }
        
        // 执行时间复杂度
        const duration = evidence.duration || 0;
        if (duration > 10000) {
            complexity += 0.2;
        } else if (duration > 5000) {
            complexity += 0.1;
        }
        
        return Math.min(complexity, 1);
    }
    
    /**
     * 评估验证历史复杂度
     */
    assessValidationHistoryComplexity(state) {
        const history = state.executionContext?.evidenceHistory || [];
        let complexity = 0;
        
        // 失败次数
        const failures = history.filter(item => item.validation?.status === 'FAILURE').length;
        complexity += Math.min(failures / 5, 1) * 0.5;
        
        // 验证分数低的次数
        const lowScores = history.filter(item => (item.validation?.score || 0) < 0.5).length;
        complexity += Math.min(lowScores / 3, 1) * 0.3;
        
        // 需要用户干预的次数
        const interventions = history.filter(item => item.validation?.nextAction === 'USER_INTERVENTION_REQUIRED').length;
        complexity += Math.min(interventions / 2, 1) * 0.2;
        
        return Math.min(complexity, 1);
    }
    
    /**
     * 评估错误类型复杂度
     */
    assessErrorTypeComplexity(error) {
        const errorTypeComplexity = {
            'NETWORK_ERROR': 0.3,
            'PERMISSION_ERROR': 0.6,
            'TIMEOUT_ERROR': 0.4,
            'ELEMENT_ERROR': 0.5,
            'STRUCTURE_ERROR': 0.8,
            'GENERIC_ERROR': 0.4
        };
        
        return errorTypeComplexity[error.type] || 0.4;
    }
    
    /**
     * 评估上下文复杂度
     */
    assessContextComplexity(context) {
        let complexity = 0;
        
        // 节点复杂度
        const nodeComplexity = {
            'planning_node': 0.3,
            'execution_node': 0.5,
            'validation_node': 0.4,
            'decision_node': 0.6,
            'recovery_node': 0.8
        };
        
        complexity += nodeComplexity[context.currentNode] || 0.4;
        
        // 上下文信息复杂度
        const contextInfo = JSON.stringify(context);
        if (contextInfo.length > 1000) {
            complexity += 0.2;
        }
        
        return Math.min(complexity, 1);
    }
    
    /**
     * 评估恢复历史复杂度
     */
    assessRecoveryHistoryComplexity(context) {
        // 这里可以基于历史恢复记录评估复杂度
        // 目前返回默认值
        return 0.3;
    }
    
    /**
     * 获取复杂度级别
     */
    getComplexityLevel(score) {
        if (score >= 0.7) return 'HIGH';
        if (score >= 0.4) return 'MEDIUM';
        return 'LOW';
    }
}

module.exports = AdaptivePromptSystem;