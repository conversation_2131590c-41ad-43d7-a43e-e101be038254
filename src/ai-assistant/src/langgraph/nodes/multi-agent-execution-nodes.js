/**
 * 多Agent执行节点
 * 使用Agent系统替代硬编码的执行逻辑
 */

const logger = require('../../utils/logger');
const { NodeResult } = require('../state/workflow-state');
const AgentCoordinator = require('../../core/agent-coordinator');

class MultiAgentExecutionNodes {
    constructor(dependencies) {
        this.logger = logger;
        this.mcpClient = dependencies.mcpClient;
        this.coordinator = new AgentCoordinator(this.mcpClient);
        this.executionId = `agent_exec_${Date.now()}`;
    }

    /**
     * Agent执行节点 - 完全由Agent系统驱动
     */
    async agentExecutionNode(state) {
        try {
            this.logger.info('🤖 多Agent智能执行开始', {
                executionId: this.executionId,
                ticketId: state.ticketId,
                taskCount: state.queuedTasks.length
            });

            state.updateCurrentNode('agent_execution_node');
            
            // 获取当前任务
            const currentTask = state.queuedTasks[0];
            if (!currentTask) {
                this.logger.warn('没有待执行的任务');
                return new NodeResult(true, { 
                    noTasks: true 
                }, 'cleanup_node');
            }

            // 构建工单对象
            const workOrder = {
                id: state.ticketId,
                title: currentTask.title || '执行任务',
                content: currentTask.content,
                originalTicket: state.checkpointData.originalTicket
            };

            // 重置协调器状态
            this.coordinator.reset();
            
            // 确保浏览器在正确的页面
            const targetUrl = this.extractTargetUrl(workOrder);
            if (targetUrl && this.mcpClient) {
                this.logger.info('🌐 导航到目标页面', { targetUrl });
                try {
                    await this.mcpClient.navigate(targetUrl);
                    // 等待页面加载
                    await new Promise(resolve => setTimeout(resolve, 3000));
                } catch (navError) {
                    this.logger.error('导航失败', navError);
                    // 导航失败不应该阻止任务执行，AI可能会自己处理
                }
            }
            
            // 执行工单
            const startTime = Date.now();
            const result = await this.coordinator.executeWorkOrder(workOrder);
            const duration = Date.now() - startTime;

            // 更新状态
            state.checkpointData.lastExecutionResult = result;
            state.updateStats(result.success, duration);

            // 记录执行结果
            this.logger.info('✅ Agent执行完成', {
                executionId: this.executionId,
                success: result.success,
                duration: duration,
                iterations: result.executionSummary.totalIterations,
                needsHumanHelp: result.needsHumanHelp
            });

            // 决定下一步
            if (result.needsHumanHelp) {
                // 需要人工介入
                state.checkpointData.needsHumanIntervention = true;
                state.checkpointData.interventionReason = result.finalState?.recommendation?.reasoning || '需要人工协助完成任务';
                return new NodeResult(true, result, 'human_intervention_node');
            } else if (result.success) {
                // 任务成功完成
                state.removeCompletedTask();
                return new NodeResult(true, result, 'task_completion_node');
            } else {
                // 任务失败，但可能可以重试
                if (state.cycleCount < 3) {
                    this.logger.info('任务失败，准备重试', {
                        cycleCount: state.cycleCount,
                        reason: result.error
                    });
                    return new NodeResult(true, result, 'retry_decision_node');
                } else {
                    this.logger.error('达到最大重试次数，任务失败');
                    return new NodeResult(false, {
                        error: '达到最大重试次数',
                        originalError: result.error
                    });
                }
            }

        } catch (error) {
            this.logger.error('Agent执行节点异常:', error);
            return new NodeResult(false, {
                error: error.message,
                stack: error.stack
            });
        }
    }

    /**
     * 人工介入节点
     */
    async humanInterventionNode(state) {
        try {
            this.logger.info('🤝 需要人工介入', {
                reason: state.checkpointData.interventionReason,
                ticketId: state.ticketId
            });

            state.updateCurrentNode('human_intervention_node');

            // 暂停工作流，等待人工处理
            state.suspend({
                reason: 'NEED_HUMAN_HELP',
                message: state.checkpointData.interventionReason,
                currentState: state.checkpointData.lastExecutionResult?.finalState
            });

            // 通知工单系统
            await this.notifyHumanInterventionNeeded(state);

            return new NodeResult(true, {
                suspended: true,
                reason: state.checkpointData.interventionReason
            }, 'wait_for_resume');

        } catch (error) {
            this.logger.error('人工介入节点错误:', error);
            return new NodeResult(false, { error: error.message });
        }
    }

    /**
     * 任务完成节点
     */
    async taskCompletionNode(state) {
        try {
            this.logger.info('🎉 任务完成处理', {
                ticketId: state.ticketId,
                remainingTasks: state.queuedTasks.length
            });

            state.updateCurrentNode('task_completion_node');

            const result = state.checkpointData.lastExecutionResult;
            
            // 保存执行证据
            if (result?.completionEvidence) {
                state.checkpointData.completionEvidence = 
                    state.checkpointData.completionEvidence || [];
                state.checkpointData.completionEvidence.push({
                    taskId: state.completedTasks.length,
                    evidence: result.completionEvidence,
                    timestamp: new Date().toISOString()
                });
            }

            // 检查是否还有更多任务
            if (state.queuedTasks.length > 0) {
                this.logger.info('还有待执行的任务，继续执行');
                return new NodeResult(true, {
                    continueExecution: true
                }, 'agent_execution_node');
            } else {
                this.logger.info('所有任务已完成');
                return new NodeResult(true, {
                    allTasksCompleted: true
                }, 'final_summary_node');
            }

        } catch (error) {
            this.logger.error('任务完成节点错误:', error);
            return new NodeResult(false, { error: error.message });
        }
    }

    /**
     * 重试决策节点
     */
    async retryDecisionNode(state) {
        try {
            this.logger.info('🔄 重试决策', {
                cycleCount: state.cycleCount,
                lastError: state.checkpointData.lastExecutionResult?.error
            });

            state.updateCurrentNode('retry_decision_node');

            // 分析失败原因
            const failureAnalysis = this.analyzeFailure(
                state.checkpointData.lastExecutionResult
            );

            if (failureAnalysis.shouldRetry) {
                state.cycleCount++;
                this.logger.info('决定重试', {
                    reason: failureAnalysis.reason,
                    strategy: failureAnalysis.retryStrategy
                });

                // 应用重试策略
                if (failureAnalysis.retryStrategy) {
                    state.checkpointData.retryStrategy = failureAnalysis.retryStrategy;
                }

                return new NodeResult(true, {
                    retry: true,
                    strategy: failureAnalysis.retryStrategy
                }, 'agent_execution_node');
            } else {
                this.logger.warn('决定不重试', {
                    reason: failureAnalysis.reason
                });

                return new NodeResult(true, {
                    retry: false,
                    reason: failureAnalysis.reason
                }, 'error_handling_node');
            }

        } catch (error) {
            this.logger.error('重试决策节点错误:', error);
            return new NodeResult(false, { error: error.message });
        }
    }

    /**
     * 最终总结节点
     */
    async finalSummaryNode(state) {
        try {
            this.logger.info('📊 生成最终执行总结', {
                ticketId: state.ticketId,
                completedTasks: state.completedTasks.length
            });

            state.updateCurrentNode('final_summary_node');

            const summary = {
                ticketId: state.ticketId,
                totalTasks: state.completedTasks.length,
                successfulTasks: state.stats.successCount,
                failedTasks: state.stats.failureCount,
                totalDuration: Date.now() - state.startTime,
                completionEvidence: state.checkpointData.completionEvidence || [],
                executionPath: this.summarizeExecutionPath(state),
                finalStatus: 'COMPLETED'
            };

            // 更新工单状态
            await this.updateTicketStatus(state, 'completed', summary);

            return new NodeResult(true, summary, 'cleanup_node');

        } catch (error) {
            this.logger.error('最终总结节点错误:', error);
            return new NodeResult(false, { error: error.message });
        }
    }

    /**
     * 错误处理节点
     */
    async errorHandlingNode(state) {
        try {
            this.logger.error('❌ 错误处理', {
                ticketId: state.ticketId,
                error: state.checkpointData.lastExecutionResult?.error
            });

            state.updateCurrentNode('error_handling_node');

            const errorSummary = {
                ticketId: state.ticketId,
                failedTask: state.queuedTasks[0],
                error: state.checkpointData.lastExecutionResult?.error,
                attemptCount: state.cycleCount,
                timestamp: new Date().toISOString()
            };

            // 更新工单状态为失败
            await this.updateTicketStatus(state, 'failed', errorSummary);

            return new NodeResult(true, errorSummary, 'cleanup_node');

        } catch (error) {
            this.logger.error('错误处理节点异常:', error);
            return new NodeResult(false, { error: error.message });
        }
    }

    /**
     * 分析失败原因
     */
    analyzeFailure(executionResult) {
        if (!executionResult || !executionResult.error) {
            return { shouldRetry: false, reason: '无错误信息' };
        }

        const error = executionResult.error;
        
        // 临时性错误，应该重试
        if (error.includes('timeout') || 
            error.includes('network') || 
            error.includes('加载')) {
            return {
                shouldRetry: true,
                reason: '临时性错误',
                retryStrategy: {
                    waitTime: 5000,
                    adjustments: ['增加等待时间']
                }
            };
        }

        // 元素定位失败，可能需要调整策略
        if (error.includes('element') || 
            error.includes('selector') ||
            error.includes('找不到')) {
            return {
                shouldRetry: true,
                reason: '元素定位失败',
                retryStrategy: {
                    waitTime: 3000,
                    adjustments: ['使用备选定位策略', '等待页面稳定']
                }
            };
        }

        // 权限或业务错误，不应重试
        if (error.includes('权限') || 
            error.includes('不存在') ||
            error.includes('已完成')) {
            return {
                shouldRetry: false,
                reason: '业务规则限制'
            };
        }

        // 默认重试一次
        return {
            shouldRetry: true,
            reason: '未知错误，尝试重试'
        };
    }

    /**
     * 总结执行路径
     */
    summarizeExecutionPath(state) {
        const path = [];
        
        // 添加每个任务的执行摘要
        state.completedTasks.forEach((task, index) => {
            path.push({
                taskIndex: index,
                taskDescription: task.content,
                success: true,
                duration: task.duration || 'N/A'
            });
        });

        // 如果有失败的任务
        if (state.queuedTasks.length > 0 && state.checkpointData.lastExecutionResult?.error) {
            path.push({
                taskIndex: state.completedTasks.length,
                taskDescription: state.queuedTasks[0].content,
                success: false,
                error: state.checkpointData.lastExecutionResult.error
            });
        }

        return path;
    }

    /**
     * 通知需要人工介入
     */
    async notifyHumanInterventionNeeded(state) {
        // 这里应该通过消息队列或API通知工单系统
        this.logger.info('通知工单系统：需要人工介入', {
            ticketId: state.ticketId,
            reason: state.checkpointData.interventionReason
        });
    }

    /**
     * 更新工单状态
     */
    async updateTicketStatus(state, status, details) {
        // 这里应该通过API更新工单系统
        this.logger.info('更新工单状态', {
            ticketId: state.ticketId,
            status: status,
            details: details
        });
    }
    
    /**
     * 从工单中提取目标URL
     */
    extractTargetUrl(workOrder) {
        // 常见的管理后台URL模式
        const commonUrls = {
            '商品': 'https://example.com/admin/products',
            '订单': 'https://example.com/admin/orders',
            '用户': 'https://example.com/admin/users',
            '设置': 'https://example.com/admin/settings'
        };
        
        // 从工单内容中查找URL
        const urlPattern = /https?:\/\/[^\s]+/i;
        const match = workOrder.content?.match(urlPattern);
        if (match) {
            return match[0];
        }
        
        // 根据关键词推断URL
        for (const [keyword, url] of Object.entries(commonUrls)) {
            if (workOrder.content?.includes(keyword) || workOrder.title?.includes(keyword)) {
                return url;
            }
        }
        
        // 检查原始工单数据
        if (workOrder.originalTicket?.metadata?.targetUrl) {
            return workOrder.originalTicket.metadata.targetUrl;
        }
        
        // 如果没有找到URL，返回null，让AI自己决定
        this.logger.info('未从工单中提取到目标URL，将由AI决定导航目标');
        return null;
    }
}

module.exports = MultiAgentExecutionNodes;