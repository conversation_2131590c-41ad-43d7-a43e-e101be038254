/**
 * 智能验证引擎 - 三层验证机制
 * 实现L1基础层、L2功能层、L3智能层的完整验证体系
 */

const logger = require('../../utils/logger');
const AdaptivePromptSystem = require('./adaptive-prompt-system');

class IntelligentValidationEngine {
    constructor(dependencies = {}) {
        this.llmClient = dependencies.llmClient;
        this.mcpClient = dependencies.mcpClient;
        this.stateManager = dependencies.stateManager;
        
        // 初始化自适应提示词系统
        this.promptSystem = new AdaptivePromptSystem();
        
        // 验证配置
        this.validationConfig = {
            L1_THRESHOLD: 1.0,   // L1基础层必须100%通过
            L2_THRESHOLD: 0.9,   // L2功能层90%通过
            L3_THRESHOLD: 0.8,   // L3智能层80%通过
            
            // 权重配置
            WEIGHTS: {
                L1_BASIC: 0.5,      // 基础层权重50%
                L2_FUNCTIONAL: 0.3, // 功能层权重30%
                L3_INTELLIGENT: 0.2  // 智能层权重20%
            }
        };
        
        // 验证历史统计
        this.validationStats = {
            totalValidations: 0,
            l1PassRate: 0,
            l2PassRate: 0,
            l3PassRate: 0,
            overallAccuracy: 0
        };
    }
    
    /**
     * 执行三层验证
     */
    async performThreeLayerValidation(plan, evidence, state) {
        const validationId = `validation_${Date.now()}`;
        
        try {
            logger.info(`🔍 开始三层验证`, {
                validationId,
                intent: plan.intent,
                cycleCount: state.cycleCount
            });
            
            // 执行三层验证
            const results = await Promise.all([
                this.performL1BasicValidation(plan, evidence, state),
                this.performL2FunctionalValidation(plan, evidence, state),
                this.performL3IntelligentValidation(plan, evidence, state)
            ]);
            
            const [l1Result, l2Result, l3Result] = results;
            
            // 计算综合结果
            const combinedResult = this.combineValidationResults(l1Result, l2Result, l3Result);
            
            // 更新统计数据
            this.updateValidationStats(l1Result, l2Result, l3Result);
            
            logger.info(`✅ 三层验证完成`, {
                validationId,
                l1Score: l1Result.score,
                l2Score: l2Result.score,
                l3Score: l3Result.score,
                finalScore: combinedResult.score,
                decision: combinedResult.nextAction
            });
            
            return combinedResult;
            
        } catch (error) {
            logger.error('三层验证执行失败:', error);
            
            return {
                status: 'VALIDATION_ERROR',
                reason: `验证引擎错误: ${error.message}`,
                confidence: 0.2,
                nextAction: 'CONTINUE',
                layer: 'ENGINE',
                error: error.message
            };
        }
    }
    
    /**
     * L1基础层验证 - 技术操作成功性检查
     */
    async performL1BasicValidation(plan, evidence, state) {
        const startTime = Date.now();
        
        try {
            logger.info(`🔧 L1基础层验证开始`, {
                intent: plan.intent,
                executionSuccess: evidence.success
            });
            
            const checks = {
                technicalSuccess: this.checkTechnicalSuccess(evidence),
                errorDetection: this.checkErrorDetection(evidence),
                stateChange: this.checkStateChange(evidence),
                timeoutCheck: this.checkTimeout(evidence),
                resourceCheck: this.checkResourceAvailability(evidence)
            };
            
            // 计算L1层分数
            const scores = Object.values(checks).map(check => check.score);
            const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
            
            const result = {
                layer: 'L1_BASIC',
                score: averageScore,
                passed: averageScore >= this.validationConfig.L1_THRESHOLD,
                checks: checks,
                reason: this.generateL1Reason(checks, averageScore),
                duration: Date.now() - startTime
            };
            
            logger.info(`✅ L1基础层验证完成`, {
                score: averageScore,
                passed: result.passed,
                duration: result.duration
            });
            
            return result;
            
        } catch (error) {
            logger.error('L1基础层验证失败:', error);
            
            return {
                layer: 'L1_BASIC',
                score: 0,
                passed: false,
                error: error.message,
                reason: `L1验证失败: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    }
    
    /**
     * L2功能层验证 - 业务目标相关性评估 (使用自适应提示词)
     */
    async performL2FunctionalValidation(plan, evidence, state) {
        const startTime = Date.now();
        
        try {
            logger.info(`🎯 L2功能层验证开始`, {
                intent: plan.intent,
                goal: state.taskGoal,
                adaptivePrompt: 'enabled'
            });
            
            // 使用自适应提示词系统生成提示词
            const prompt = this.promptSystem.generateValidationPrompt('L2_FUNCTIONAL', plan, evidence, state);
            
            // 调用LLM进行功能性分析
            const llmResponse = await this.llmClient.chat(prompt);
            
            // 解析L2验证结果
            const l2Analysis = this.parseL2ValidationResponse(llmResponse);
            
            // 更新提示词系统的成功率
            const success = l2Analysis.score >= this.validationConfig.L2_THRESHOLD;
            this.promptSystem.updateSuccessRate('validation', success);
            
            const result = {
                layer: 'L2_FUNCTIONAL',
                score: l2Analysis.score,
                passed: success,
                analysis: l2Analysis,
                reason: l2Analysis.reason,
                suggestions: l2Analysis.suggestions,
                duration: Date.now() - startTime,
                adaptivePrompt: true
            };
            
            logger.info(`✅ L2功能层验证完成`, {
                score: l2Analysis.score,
                passed: result.passed,
                relevance: l2Analysis.goalRelevance,
                duration: result.duration,
                adaptivePrompt: 'used'
            });
            
            return result;
            
        } catch (error) {
            logger.error('L2功能层验证失败:', error);
            
            // 更新提示词系统的失败率
            this.promptSystem.updateSuccessRate('validation', false);
            
            return {
                layer: 'L2_FUNCTIONAL',
                score: 0.5, // 默认中等分数
                passed: false,
                error: error.message,
                reason: `L2验证失败: ${error.message}`,
                duration: Date.now() - startTime,
                adaptivePrompt: false
            };
        }
    }
    
    /**
     * L3智能层验证 - 上下文完整性判断 (使用自适应提示词)
     */
    async performL3IntelligentValidation(plan, evidence, state) {
        const startTime = Date.now();
        
        try {
            logger.info(`🧠 L3智能层验证开始`, {
                intent: plan.intent,
                cycleCount: state.cycleCount,
                historyLength: state.executionContext.evidenceHistory?.length || 0,
                adaptivePrompt: 'enabled'
            });
            
            // 使用自适应提示词系统生成提示词
            const prompt = this.promptSystem.generateValidationPrompt('L3_INTELLIGENT', plan, evidence, state);
            
            // 调用LLM进行智能分析
            const llmResponse = await this.llmClient.chat(prompt);
            
            // 解析L3验证结果
            const l3Analysis = this.parseL3ValidationResponse(llmResponse);
            
            // 更新提示词系统的成功率
            const success = l3Analysis.score >= this.validationConfig.L3_THRESHOLD;
            this.promptSystem.updateSuccessRate('validation', success);
            
            const result = {
                layer: 'L3_INTELLIGENT',
                score: l3Analysis.score,
                passed: success,
                analysis: l3Analysis,
                reason: l3Analysis.reason,
                completionAssessment: l3Analysis.completionAssessment,
                nextStepRecommendation: l3Analysis.nextStepRecommendation,
                duration: Date.now() - startTime,
                adaptivePrompt: true
            };
            
            logger.info(`✅ L3智能层验证完成`, {
                score: l3Analysis.score,
                passed: result.passed,
                completionLevel: l3Analysis.completionAssessment,
                recommendation: l3Analysis.nextStepRecommendation,
                duration: result.duration,
                adaptivePrompt: 'used'
            });
            
            return result;
            
        } catch (error) {
            logger.error('L3智能层验证失败:', error);
            
            // 更新提示词系统的失败率
            this.promptSystem.updateSuccessRate('validation', false);
            
            return {
                layer: 'L3_INTELLIGENT',
                score: 0.6, // 默认中等分数
                passed: false,
                error: error.message,
                reason: `L3验证失败: ${error.message}`,
                duration: Date.now() - startTime,
                adaptivePrompt: false
            };
        }
    }
    
    /**
     * 检查技术成功性
     */
    checkTechnicalSuccess(evidence) {
        if (!evidence.success) {
            return {
                name: 'technicalSuccess',
                score: 0,
                passed: false,
                reason: '执行失败',
                details: evidence.error || '未知错误'
            };
        }
        
        return {
            name: 'technicalSuccess',
            score: 1.0,
            passed: true,
            reason: '技术执行成功',
            details: evidence.executionResult
        };
    }
    
    /**
     * 检查错误检测
     */
    checkErrorDetection(evidence) {
        const hasError = evidence.error || 
                        (evidence.executionResult && evidence.executionResult.error);
        
        if (hasError) {
            return {
                name: 'errorDetection',
                score: 0,
                passed: false,
                reason: '检测到执行错误',
                details: evidence.error || evidence.executionResult.error
            };
        }
        
        return {
            name: 'errorDetection',
            score: 1.0,
            passed: true,
            reason: '无错误检测',
            details: '执行过程中无错误'
        };
    }
    
    /**
     * 检查状态变化
     */
    checkStateChange(evidence) {
        const beforeState = evidence.beforeState;
        const afterState = evidence.afterState;
        
        if (!beforeState || !afterState) {
            return {
                name: 'stateChange',
                score: 0.5,
                passed: true,
                reason: '状态捕获不完整',
                details: '缺少前后状态对比'
            };
        }
        
        // 简单的状态变化检测
        const stateChanged = JSON.stringify(beforeState) !== JSON.stringify(afterState);
        
        return {
            name: 'stateChange',
            score: stateChanged ? 1.0 : 0.8,
            passed: true,
            reason: stateChanged ? '检测到状态变化' : '无明显状态变化',
            details: {
                beforeState: beforeState?.snapshot?.substring(0, 100) || '无',
                afterState: afterState?.snapshot?.substring(0, 100) || '无'
            }
        };
    }
    
    /**
     * 检查超时
     */
    checkTimeout(evidence) {
        const duration = evidence.duration || 0;
        const maxDuration = 30000; // 30秒最大执行时间
        
        if (duration > maxDuration) {
            return {
                name: 'timeoutCheck',
                score: 0.3,
                passed: false,
                reason: '执行超时',
                details: `执行时间 ${duration}ms 超过限制 ${maxDuration}ms`
            };
        }
        
        return {
            name: 'timeoutCheck',
            score: 1.0,
            passed: true,
            reason: '执行时间正常',
            details: `执行时间 ${duration}ms`
        };
    }
    
    /**
     * 检查资源可用性
     */
    checkResourceAvailability(evidence) {
        // 简单的资源检查
        return {
            name: 'resourceCheck',
            score: 1.0,
            passed: true,
            reason: '资源可用',
            details: '系统资源正常'
        };
    }
    
    /**
     * 生成L1验证理由
     */
    generateL1Reason(checks, score) {
        const failedChecks = Object.values(checks).filter(check => !check.passed);
        
        if (failedChecks.length === 0) {
            return 'L1基础层验证全部通过';
        }
        
        const failedNames = failedChecks.map(check => check.name).join(', ');
        return `L1基础层验证失败: ${failedNames}`;
    }
    
    /**
     * 构建L2验证提示词
     */
    buildL2ValidationPrompt(plan, evidence, state) {
        return `# L2功能层验证 - 业务目标相关性评估

## 任务目标
${state.taskGoal}

## 执行计划
- 意图: ${plan.intent}
- 参数: ${JSON.stringify(plan.parameters)}
- 推理: ${plan.reasoning}
- 置信度: ${plan.confidence}

## 执行结果
- 技术成功: ${evidence.success}
- 执行操作: ${evidence.executionResult?.action || '未知'}
- 执行时间: ${evidence.duration}ms

## 执行历史
最近3次执行结果:
${state.executionContext.evidenceHistory?.slice(-3).map((item, index) => 
    `${index + 1}. ${item.plan?.intent || '未知'} - ${item.evidence?.success ? '成功' : '失败'}`
).join('\n') || '无历史记录'}

## 验证要求
请从以下维度评估本次执行的业务功能相关性：

1. **目标相关性** (0-1): 本次操作是否朝着任务目标前进？
2. **状态一致性** (0-1): 系统状态变化是否符合预期？
3. **副作用检查** (0-1): 是否产生了不期望的影响？
4. **进度评估** (0-1): 任务完成度是否有所提升？

返回JSON格式：
{
  "goalRelevance": 0.8,
  "stateConsistency": 0.9,
  "sideEffectCheck": 0.95,
  "progressAssessment": 0.7,
  "score": 0.84,
  "reason": "详细分析理由",
  "suggestions": "改进建议"
}`;
    }
    
    /**
     * 构建L3验证提示词
     */
    buildL3ValidationPrompt(plan, evidence, state) {
        return `# L3智能层验证 - 上下文完整性判断

## 任务全貌
- 目标: ${state.taskGoal}
- 当前周期: ${state.cycleCount}/${state.maxCycles}
- 总执行时间: ${state.stats?.totalExecutions || 0}次

## 执行历史脉络
${state.executionContext.evidenceHistory?.map((item, index) => 
    `周期${index + 1}: ${item.plan?.intent || '未知'} -> ${item.evidence?.success ? '成功' : '失败'} -> ${item.validation?.status || '未验证'}`
).join('\n') || '无历史记录'}

## 当前执行
- 意图: ${plan.intent}
- 结果: ${evidence.success ? '成功' : '失败'}
- 操作: ${evidence.executionResult?.action || '未知'}

## 智能分析要求
请基于完整的上下文进行深度分析：

1. **任务完成度评估** (0-1): 整体任务完成了多少？
2. **执行路径合理性** (0-1): 当前的执行路径是否最优？
3. **上下文连贯性** (0-1): 各步骤之间的逻辑是否连贯？
4. **完成可能性** (0-1): 按当前路径完成任务的可能性？

## 决策建议
请给出下一步的最佳建议：
- TASK_COMPLETED: 任务已完成
- CONTINUE: 继续当前策略
- STRATEGY_CHANGE: 需要改变策略
- USER_INTERVENTION_REQUIRED: 需要用户干预
- CRITICAL_ERROR: 发生严重错误

返回JSON格式：
{
  "completionAssessment": 0.7,
  "pathOptimality": 0.8,
  "contextCoherence": 0.9,
  "completionProbability": 0.85,
  "score": 0.81,
  "reason": "智能分析理由",
  "nextStepRecommendation": "CONTINUE",
  "strategicAdvice": "战略建议"
}`;
    }
    
    /**
     * 解析L2验证响应
     */
    parseL2ValidationResponse(response) {
        try {
            const cleaned = this.cleanJsonResponse(response);
            const result = JSON.parse(cleaned);
            
            return {
                goalRelevance: result.goalRelevance || 0.5,
                stateConsistency: result.stateConsistency || 0.5,
                sideEffectCheck: result.sideEffectCheck || 0.5,
                progressAssessment: result.progressAssessment || 0.5,
                score: result.score || 0.5,
                reason: result.reason || '无具体理由',
                suggestions: result.suggestions || '无建议'
            };
            
        } catch (error) {
            logger.error('解析L2验证响应失败:', error);
            
            return {
                goalRelevance: 0.5,
                stateConsistency: 0.5,
                sideEffectCheck: 0.5,
                progressAssessment: 0.5,
                score: 0.5,
                reason: '解析失败，使用默认值',
                suggestions: '建议检查LLM响应格式'
            };
        }
    }
    
    /**
     * 解析L3验证响应
     */
    parseL3ValidationResponse(response) {
        try {
            const cleaned = this.cleanJsonResponse(response);
            const result = JSON.parse(cleaned);
            
            return {
                completionAssessment: result.completionAssessment || 0.5,
                pathOptimality: result.pathOptimality || 0.5,
                contextCoherence: result.contextCoherence || 0.5,
                completionProbability: result.completionProbability || 0.5,
                score: result.score || 0.5,
                reason: result.reason || '无具体理由',
                nextStepRecommendation: result.nextStepRecommendation || 'CONTINUE',
                strategicAdvice: result.strategicAdvice || '无战略建议'
            };
            
        } catch (error) {
            logger.error('解析L3验证响应失败:', error);
            
            return {
                completionAssessment: 0.5,
                pathOptimality: 0.5,
                contextCoherence: 0.5,
                completionProbability: 0.5,
                score: 0.5,
                reason: '解析失败，使用默认值',
                nextStepRecommendation: 'CONTINUE',
                strategicAdvice: '建议检查LLM响应格式'
            };
        }
    }
    
    /**
     * 清理JSON响应
     */
    cleanJsonResponse(response) {
        let cleaned = response.trim();
        
        // 移除markdown标记
        if (cleaned.startsWith('```json')) {
            cleaned = cleaned.replace(/```json\n?/, '').replace(/\n?```$/, '');
        }
        
        return cleaned;
    }
    
    /**
     * 合并验证结果
     */
    combineValidationResults(l1Result, l2Result, l3Result) {
        const weights = this.validationConfig.WEIGHTS;
        
        // 计算加权分数
        const weightedScore = (
            l1Result.score * weights.L1_BASIC +
            l2Result.score * weights.L2_FUNCTIONAL +
            l3Result.score * weights.L3_INTELLIGENT
        );
        
        // 判断整体通过状态
        const overallPassed = l1Result.passed && l2Result.passed && l3Result.passed;
        
        // 决定下一步动作
        const nextAction = this.determineNextAction(l1Result, l2Result, l3Result);
        
        return {
            status: overallPassed ? 'SUCCESS' : 'NEEDS_IMPROVEMENT',
            score: weightedScore,
            confidence: this.calculateConfidence(l1Result, l2Result, l3Result),
            nextAction: nextAction,
            reason: this.generateCombinedReason(l1Result, l2Result, l3Result),
            layerResults: {
                l1: l1Result,
                l2: l2Result,
                l3: l3Result
            },
            suggestions: this.generateCombinedSuggestions(l1Result, l2Result, l3Result)
        };
    }
    
    /**
     * 计算置信度
     */
    calculateConfidence(l1Result, l2Result, l3Result) {
        const scores = [l1Result.score, l2Result.score, l3Result.score];
        const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        
        // 如果L1失败，置信度很低
        if (!l1Result.passed) {
            return Math.min(0.3, avgScore);
        }
        
        return avgScore;
    }
    
    /**
     * 决定下一步动作
     */
    determineNextAction(l1Result, l2Result, l3Result) {
        // L1失败，需要重试
        if (!l1Result.passed) {
            return 'CONTINUE';
        }
        
        // L3智能层建议
        if (l3Result.analysis && l3Result.analysis.nextStepRecommendation) {
            return l3Result.analysis.nextStepRecommendation;
        }
        
        // 默认继续
        return 'CONTINUE';
    }
    
    /**
     * 生成综合理由
     */
    generateCombinedReason(l1Result, l2Result, l3Result) {
        const reasons = [];
        
        if (l1Result.reason) reasons.push(`L1: ${l1Result.reason}`);
        if (l2Result.reason) reasons.push(`L2: ${l2Result.reason}`);
        if (l3Result.reason) reasons.push(`L3: ${l3Result.reason}`);
        
        return reasons.join('; ');
    }
    
    /**
     * 生成综合建议
     */
    generateCombinedSuggestions(l1Result, l2Result, l3Result) {
        const suggestions = [];
        
        if (l1Result.suggestions) suggestions.push(l1Result.suggestions);
        if (l2Result.suggestions) suggestions.push(l2Result.suggestions);
        if (l3Result.analysis && l3Result.analysis.strategicAdvice) {
            suggestions.push(l3Result.analysis.strategicAdvice);
        }
        
        return suggestions.join('; ');
    }
    
    /**
     * 更新验证统计
     */
    updateValidationStats(l1Result, l2Result, l3Result) {
        this.validationStats.totalValidations++;
        
        if (l1Result.passed) this.validationStats.l1PassRate++;
        if (l2Result.passed) this.validationStats.l2PassRate++;
        if (l3Result.passed) this.validationStats.l3PassRate++;
        
        // 计算通过率
        const total = this.validationStats.totalValidations;
        this.validationStats.l1PassRate = this.validationStats.l1PassRate / total;
        this.validationStats.l2PassRate = this.validationStats.l2PassRate / total;
        this.validationStats.l3PassRate = this.validationStats.l3PassRate / total;
    }
    
    /**
     * 获取验证统计
     */
    getValidationStats() {
        return { ...this.validationStats };
    }
}

module.exports = IntelligentValidationEngine;