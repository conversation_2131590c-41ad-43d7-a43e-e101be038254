/**
 * 页面状态监控器
 * 检测页面变化，支持登录后的状态重新评估
 */

const logger = require('../../utils/logger');

class PageStateMonitor {
    constructor(simpleExecutor) {
        this.executor = simpleExecutor;
        this.lastState = null;
        this.loginCheckInterval = 3000; // 3秒检查一次
        this.maxWaitTime = 30000; // 最多等待30秒
    }

    /**
     * 获取当前页面状态
     */
    async getCurrentPageState() {
        try {
            const snapshot = await this.executor.client.snapshot();
            const screenshot = await this.executor.client.screenshot();
            
            return {
                url: snapshot.url,
                title: snapshot.title,
                elements: snapshot.elements,
                timestamp: Date.now(),
                screenshot: screenshot.screenshot
            };
        } catch (error) {
            logger.error('获取页面状态失败:', error);
            return null;
        }
    }

    /**
     * 检测页面是否发生重大变化
     */
    hasPageChanged(oldState, newState) {
        if (!oldState || !newState) return true;
        
        // 检查URL变化
        if (oldState.url !== newState.url) {
            logger.info(`🔄 页面URL变化: ${oldState.url} -> ${newState.url}`);
            return true;
        }
        
        // 检查标题变化（登录后标题通常会变）
        if (oldState.title !== newState.title) {
            logger.info(`🔄 页面标题变化: ${oldState.title} -> ${newState.title}`);
            return true;
        }
        
        // 检查元素数量的显著变化
        const oldElementCount = this.countElements(oldState.elements);
        const newElementCount = this.countElements(newState.elements);
        
        if (Math.abs(oldElementCount - newElementCount) > 5) {
            logger.info(`🔄 页面元素数量变化: ${oldElementCount} -> ${newElementCount}`);
            return true;
        }
        
        return false;
    }
    
    /**
     * 计算页面元素总数
     */
    countElements(elements) {
        if (!elements) return 0;
        return Object.values(elements).reduce((total, category) => {
            return total + (category ? Object.keys(category).length : 0);
        }, 0);
    }

    /**
     * 等待用户登录并检测页面变化
     */
    async waitForUserLogin(maxWaitMs = 30000) {
        logger.info('⏳ 等待用户登录...');
        
        // 保存当前状态
        const initialState = await this.getCurrentPageState();
        const startTime = Date.now();
        
        return new Promise((resolve) => {
            const checkInterval = setInterval(async () => {
                const currentTime = Date.now();
                const elapsedTime = currentTime - startTime;
                
                // 检查是否超时
                if (elapsedTime > maxWaitMs) {
                    clearInterval(checkInterval);
                    logger.warn('⏱️ 等待登录超时');
                    resolve({
                        success: false,
                        reason: 'timeout',
                        elapsedTime
                    });
                    return;
                }
                
                // 获取当前状态
                const currentState = await this.getCurrentPageState();
                
                // 检测页面是否变化
                if (this.hasPageChanged(initialState, currentState)) {
                    clearInterval(checkInterval);
                    logger.info('✅ 检测到页面变化，可能已登录成功');
                    
                    // 等待页面稳定
                    await new Promise(r => setTimeout(r, 2000));
                    
                    resolve({
                        success: true,
                        reason: 'page_changed',
                        elapsedTime,
                        newState: currentState
                    });
                }
                
                logger.debug(`⏳ 等待登录中... (${Math.floor(elapsedTime/1000)}秒)`);
                
            }, this.loginCheckInterval);
        });
    }

    /**
     * 分析页面是否需要登录
     */
    async isLoginRequired(pageState) {
        if (!pageState) return false;
        
        // 检查URL
        if (pageState.url && pageState.url.includes('login')) {
            return true;
        }
        
        // 检查标题
        if (pageState.title && pageState.title.includes('登录')) {
            return true;
        }
        
        // 检查是否有登录相关的输入框
        const inputs = pageState.elements?.inputs || {};
        const hasPasswordInput = Object.values(inputs).some(input => 
            input.type === 'password' || 
            input.placeholder?.includes('密码') ||
            input.name?.includes('password')
        );
        
        const hasUsernameInput = Object.values(inputs).some(input => 
            input.placeholder?.includes('用户名') ||
            input.placeholder?.includes('账号') ||
            input.name?.includes('username')
        );
        
        return hasPasswordInput || hasUsernameInput;
    }
}

module.exports = PageStateMonitor;