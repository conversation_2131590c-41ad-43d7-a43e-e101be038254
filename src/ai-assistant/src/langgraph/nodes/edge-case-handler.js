/**
 * 边缘场景处理器
 * 专门处理RPA系统中的各种边缘情况和异常场景
 */

const logger = require('../../utils/logger');

class EdgeCaseHandler {
    constructor(dependencies = {}) {
        this.mcpClient = dependencies.mcpClient;
        this.llmClient = dependencies.llmClient;
        this.stateManager = dependencies.stateManager;
        
        // 动态等待配置
        this.dynamicWaitConfig = {
            MIN_WAIT: 500,           // 最小等待时间 0.5秒
            MAX_WAIT: 30000,         // 最大等待时间 30秒
            DEFAULT_WAIT: 3000,      // 默认等待时间 3秒
            STABILITY_CHECK_INTERVAL: 500, // 稳定性检查间隔
            MAX_STABILITY_CHECKS: 60 // 最大稳定性检查次数
        };
        
        // 错误恢复配置
        this.errorRecoveryConfig = {
            MAX_RETRY_ATTEMPTS: 3,   // 最大重试次数
            RETRY_DELAY_BASE: 1000,  // 基础重试延迟
            RETRY_DELAY_MULTIPLIER: 2, // 重试延迟倍数
            CRITICAL_ERROR_THRESHOLD: 5, // 严重错误阈值
            RECOVERY_TIMEOUT: 60000  // 恢复超时时间
        };
        
        // 边缘场景统计
        this.edgeStats = {
            dynamicWaits: 0,
            successfulRecoveries: 0,
            failedRecoveries: 0,
            networkErrors: 0,
            timeoutErrors: 0,
            permissionErrors: 0,
            dynamicContentErrors: 0
        };
    }
    
    /**
     * 智能动态等待机制
     * 根据页面状态动态调整等待时间
     */
    async performDynamicWait(context) {
        const waitId = `wait_${Date.now()}`;
        const startTime = Date.now();
        
        try {
            logger.info(`🕐 开始智能动态等待`, {
                waitId,
                context: context.type || 'unknown',
                expectedCondition: context.expectedCondition || 'page_ready'
            });
            
            // 根据上下文选择等待策略
            const strategy = this.selectWaitStrategy(context);
            
            // 执行动态等待
            const result = await this.executeDynamicWait(strategy, context);
            
            const duration = Date.now() - startTime;
            this.edgeStats.dynamicWaits++;
            
            logger.info(`✅ 智能动态等待完成`, {
                waitId,
                strategy: strategy.name,
                duration,
                satisfied: result.satisfied,
                attempts: result.attempts
            });
            
            return result;
            
        } catch (error) {
            logger.error('智能动态等待失败:', error);
            
            return {
                satisfied: false,
                error: error.message,
                duration: Date.now() - startTime,
                fallback: true
            };
        }
    }
    
    /**
     * 选择等待策略
     */
    selectWaitStrategy(context) {
        const strategies = {
            PAGE_LOAD: {
                name: 'page_load',
                condition: () => this.checkPageLoadComplete(),
                maxWait: 15000,
                checkInterval: 1000
            },
            ELEMENT_VISIBLE: {
                name: 'element_visible',
                condition: () => this.checkElementVisible(context.selector),
                maxWait: 10000,
                checkInterval: 500
            },
            ELEMENT_CLICKABLE: {
                name: 'element_clickable',
                condition: () => this.checkElementClickable(context.selector),
                maxWait: 8000,
                checkInterval: 500
            },
            DYNAMIC_CONTENT: {
                name: 'dynamic_content',
                condition: () => this.checkDynamicContentStable(context.selector),
                maxWait: 20000,
                checkInterval: 1000
            },
            NETWORK_IDLE: {
                name: 'network_idle',
                condition: () => this.checkNetworkIdle(),
                maxWait: 15000,
                checkInterval: 1000
            },
            CUSTOM_CONDITION: {
                name: 'custom_condition',
                condition: context.customCondition || (() => Promise.resolve(true)),
                maxWait: context.maxWait || this.dynamicWaitConfig.DEFAULT_WAIT,
                checkInterval: context.checkInterval || this.dynamicWaitConfig.STABILITY_CHECK_INTERVAL
            }
        };
        
        const strategyType = context.type || 'PAGE_LOAD';
        return strategies[strategyType] || strategies.PAGE_LOAD;
    }
    
    /**
     * 执行动态等待
     */
    async executeDynamicWait(strategy, context) {
        const startTime = Date.now();
        let attempts = 0;
        let satisfied = false;
        
        while (!satisfied && (Date.now() - startTime) < strategy.maxWait) {
            attempts++;
            
            try {
                // 检查条件是否满足
                satisfied = await strategy.condition();
                
                if (!satisfied) {
                    await this.sleep(strategy.checkInterval);
                }
                
            } catch (error) {
                logger.warn(`等待条件检查失败 (尝试 ${attempts})`, error);
                
                // 如果检查失败，等待一段时间后重试
                await this.sleep(strategy.checkInterval);
            }
        }
        
        return {
            satisfied,
            attempts,
            duration: Date.now() - startTime,
            strategy: strategy.name
        };
    }
    
    /**
     * 检查页面加载完成
     */
    async checkPageLoadComplete() {
        try {
            // 通过MCP客户端检查页面状态
            const snapshot = await this.mcpClient.callTool('browser_snapshot', {});
            
            // 简单的页面完整性检查
            return snapshot && snapshot.length > 100;
            
        } catch (error) {
            logger.warn('页面加载检查失败:', error);
            return false;
        }
    }
    
    /**
     * 检查元素可见性
     */
    async checkElementVisible(selector) {
        try {
            const snapshot = await this.mcpClient.callTool('browser_snapshot', {});
            
            // 简单的元素存在性检查
            return snapshot && snapshot.includes(selector);
            
        } catch (error) {
            logger.warn('元素可见性检查失败:', error);
            return false;
        }
    }
    
    /**
     * 检查元素可点击性
     */
    async checkElementClickable(selector) {
        try {
            // 首先检查元素是否可见
            const visible = await this.checkElementVisible(selector);
            
            if (!visible) {
                return false;
            }
            
            // 这里可以添加更多的可点击性检查
            // 比如检查元素是否被覆盖、是否被禁用等
            
            return true;
            
        } catch (error) {
            logger.warn('元素可点击性检查失败:', error);
            return false;
        }
    }
    
    /**
     * 检查动态内容稳定性
     */
    async checkDynamicContentStable(selector) {
        try {
            // 获取当前页面状态
            const currentSnapshot = await this.mcpClient.callTool('browser_snapshot', {});
            
            // 等待一段时间后再次检查
            await this.sleep(1000);
            
            const nextSnapshot = await this.mcpClient.callTool('browser_snapshot', {});
            
            // 比较两次快照是否一致
            return currentSnapshot === nextSnapshot;
            
        } catch (error) {
            logger.warn('动态内容稳定性检查失败:', error);
            return false;
        }
    }
    
    /**
     * 检查网络空闲状态
     */
    async checkNetworkIdle() {
        try {
            // 这里可以通过浏览器API检查网络请求状态
            // 目前简单地返回true
            return true;
            
        } catch (error) {
            logger.warn('网络空闲检查失败:', error);
            return false;
        }
    }
    
    /**
     * 智能错误恢复机制
     */
    async performErrorRecovery(error, context) {
        const recoveryId = `recovery_${Date.now()}`;
        const startTime = Date.now();
        
        try {
            logger.info(`🔧 开始智能错误恢复`, {
                recoveryId,
                errorType: error.type || 'unknown',
                errorMessage: error.message,
                context: context.currentNode || 'unknown'
            });
            
            // 错误分类
            const errorCategory = this.classifyError(error);
            
            // 选择恢复策略
            const strategy = this.selectRecoveryStrategy(errorCategory, context);
            
            // 执行恢复
            const result = await this.executeRecovery(strategy, error, context);
            
            const duration = Date.now() - startTime;
            
            if (result.success) {
                this.edgeStats.successfulRecoveries++;
                logger.info(`✅ 错误恢复成功`, {
                    recoveryId,
                    strategy: strategy.name,
                    duration,
                    attempts: result.attempts
                });
            } else {
                this.edgeStats.failedRecoveries++;
                logger.error(`❌ 错误恢复失败`, {
                    recoveryId,
                    strategy: strategy.name,
                    duration,
                    reason: result.reason
                });
            }
            
            return result;
            
        } catch (recoveryError) {
            logger.error('错误恢复过程中发生异常:', recoveryError);
            
            return {
                success: false,
                error: recoveryError.message,
                duration: Date.now() - startTime,
                fallback: true
            };
        }
    }
    
    /**
     * 错误分类
     */
    classifyError(error) {
        const errorMessage = error.message || error.toString();
        const errorType = error.type || 'unknown';
        
        // 网络错误
        if (errorMessage.includes('network') || errorMessage.includes('timeout') || 
            errorMessage.includes('connection') || errorType === 'NETWORK_ERROR') {
            this.edgeStats.networkErrors++;
            return 'NETWORK_ERROR';
        }
        
        // 权限错误
        if (errorMessage.includes('permission') || errorMessage.includes('unauthorized') || 
            errorMessage.includes('forbidden') || errorType === 'PERMISSION_ERROR') {
            this.edgeStats.permissionErrors++;
            return 'PERMISSION_ERROR';
        }
        
        // 超时错误
        if (errorMessage.includes('timeout') || errorType === 'TIMEOUT_ERROR') {
            this.edgeStats.timeoutErrors++;
            return 'TIMEOUT_ERROR';
        }
        
        // 动态内容错误
        if (errorMessage.includes('element not found') || errorMessage.includes('selector') || 
            errorType === 'ELEMENT_ERROR') {
            this.edgeStats.dynamicContentErrors++;
            return 'ELEMENT_ERROR';
        }
        
        // 页面结构变化错误
        if (errorMessage.includes('page changed') || errorMessage.includes('structure') || 
            errorType === 'STRUCTURE_ERROR') {
            return 'STRUCTURE_ERROR';
        }
        
        // 默认为通用错误
        return 'GENERIC_ERROR';
    }
    
    /**
     * 选择恢复策略
     */
    selectRecoveryStrategy(errorCategory, context) {
        const strategies = {
            NETWORK_ERROR: {
                name: 'network_recovery',
                maxRetries: 3,
                retryDelay: 2000,
                actions: ['wait', 'retry', 'fallback']
            },
            PERMISSION_ERROR: {
                name: 'permission_recovery',
                maxRetries: 1,
                retryDelay: 1000,
                actions: ['refresh', 'retry', 'user_intervention']
            },
            TIMEOUT_ERROR: {
                name: 'timeout_recovery',
                maxRetries: 2,
                retryDelay: 5000,
                actions: ['extended_wait', 'retry', 'fallback']
            },
            ELEMENT_ERROR: {
                name: 'element_recovery',
                maxRetries: 3,
                retryDelay: 1000,
                actions: ['wait', 'refresh', 'retry']
            },
            STRUCTURE_ERROR: {
                name: 'structure_recovery',
                maxRetries: 2,
                retryDelay: 3000,
                actions: ['page_analysis', 'retry', 'user_intervention']
            },
            GENERIC_ERROR: {
                name: 'generic_recovery',
                maxRetries: 2,
                retryDelay: 2000,
                actions: ['wait', 'retry', 'fallback']
            }
        };
        
        return strategies[errorCategory] || strategies.GENERIC_ERROR;
    }
    
    /**
     * 执行恢复策略
     */
    async executeRecovery(strategy, error, context) {
        let attempts = 0;
        let lastError = error;
        
        for (const action of strategy.actions) {
            attempts++;
            
            try {
                logger.info(`🔄 执行恢复动作: ${action}`, {
                    attempt: attempts,
                    strategy: strategy.name
                });
                
                const actionResult = await this.executeRecoveryAction(action, lastError, context);
                
                if (actionResult.success) {
                    return {
                        success: true,
                        action: action,
                        attempts: attempts,
                        result: actionResult
                    };
                }
                
                lastError = actionResult.error || lastError;
                
                // 如果不是最后一个动作，等待一段时间后继续
                if (attempts < strategy.actions.length) {
                    await this.sleep(strategy.retryDelay);
                }
                
            } catch (actionError) {
                logger.warn(`恢复动作失败: ${action}`, actionError);
                lastError = actionError;
            }
        }
        
        return {
            success: false,
            attempts: attempts,
            reason: '所有恢复动作都失败',
            lastError: lastError
        };
    }
    
    /**
     * 执行具体的恢复动作
     */
    async executeRecoveryAction(action, error, context) {
        switch (action) {
            case 'wait':
                return await this.performRecoveryWait(error, context);
                
            case 'retry':
                return await this.performRecoveryRetry(error, context);
                
            case 'refresh':
                return await this.performRecoveryRefresh(error, context);
                
            case 'extended_wait':
                return await this.performExtendedWait(error, context);
                
            case 'page_analysis':
                return await this.performPageAnalysis(error, context);
                
            case 'user_intervention':
                return await this.requestUserIntervention(error, context);
                
            case 'fallback':
                return await this.performFallback(error, context);
                
            default:
                return {
                    success: false,
                    error: new Error(`未知的恢复动作: ${action}`)
                };
        }
    }
    
    /**
     * 恢复等待
     */
    async performRecoveryWait(error, context) {
        const waitTime = Math.min(
            this.errorRecoveryConfig.RETRY_DELAY_BASE * 2,
            this.dynamicWaitConfig.MAX_WAIT
        );
        
        await this.sleep(waitTime);
        
        return {
            success: true,
            action: 'wait',
            duration: waitTime
        };
    }
    
    /**
     * 恢复重试
     */
    async performRecoveryRetry(error, context) {
        try {
            // 这里可以重新执行失败的操作
            // 目前简单返回成功
            return {
                success: true,
                action: 'retry'
            };
            
        } catch (retryError) {
            return {
                success: false,
                error: retryError,
                action: 'retry'
            };
        }
    }
    
    /**
     * 恢复刷新
     */
    async performRecoveryRefresh(error, context) {
        try {
            // 刷新页面
            await this.mcpClient.callTool('browser_navigate', { 
                url: context.currentUrl || 'current' 
            });
            
            // 等待页面加载
            await this.performDynamicWait({ 
                type: 'PAGE_LOAD',
                maxWait: 10000 
            });
            
            return {
                success: true,
                action: 'refresh'
            };
            
        } catch (refreshError) {
            return {
                success: false,
                error: refreshError,
                action: 'refresh'
            };
        }
    }
    
    /**
     * 扩展等待
     */
    async performExtendedWait(error, context) {
        const extendedWaitTime = Math.min(
            this.errorRecoveryConfig.RETRY_DELAY_BASE * 5,
            this.dynamicWaitConfig.MAX_WAIT
        );
        
        await this.sleep(extendedWaitTime);
        
        return {
            success: true,
            action: 'extended_wait',
            duration: extendedWaitTime
        };
    }
    
    /**
     * 页面分析
     */
    async performPageAnalysis(error, context) {
        try {
            // 获取页面快照
            const snapshot = await this.mcpClient.callTool('browser_snapshot', {});
            
            // 简单的页面分析
            const analysis = {
                hasContent: snapshot && snapshot.length > 0,
                contentLength: snapshot ? snapshot.length : 0,
                timestamp: new Date().toISOString()
            };
            
            return {
                success: true,
                action: 'page_analysis',
                analysis: analysis
            };
            
        } catch (analysisError) {
            return {
                success: false,
                error: analysisError,
                action: 'page_analysis'
            };
        }
    }
    
    /**
     * 请求用户干预
     */
    async requestUserIntervention(error, context) {
        logger.warn('🤝 请求用户干预', {
            error: error.message,
            context: context.currentNode,
            suggestion: '需要用户手动检查并处理当前状态'
        });
        
        return {
            success: false,
            action: 'user_intervention',
            requiresUserAction: true,
            message: '需要用户干预来解决当前问题'
        };
    }
    
    /**
     * 执行降级方案
     */
    async performFallback(error, context) {
        logger.warn('🔄 执行降级方案', {
            error: error.message,
            context: context.currentNode
        });
        
        return {
            success: false,
            action: 'fallback',
            fallbackExecuted: true,
            message: '执行降级方案，继续处理其他任务'
        };
    }
    
    /**
     * 获取边缘场景统计
     */
    getEdgeStats() {
        return {
            ...this.edgeStats,
            totalRecoveries: this.edgeStats.successfulRecoveries + this.edgeStats.failedRecoveries,
            recoverySuccessRate: this.edgeStats.successfulRecoveries / 
                (this.edgeStats.successfulRecoveries + this.edgeStats.failedRecoveries) || 0
        };
    }
    
    /**
     * 重置统计
     */
    resetStats() {
        this.edgeStats = {
            dynamicWaits: 0,
            successfulRecoveries: 0,
            failedRecoveries: 0,
            networkErrors: 0,
            timeoutErrors: 0,
            permissionErrors: 0,
            dynamicContentErrors: 0
        };
    }
    
    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = EdgeCaseHandler;