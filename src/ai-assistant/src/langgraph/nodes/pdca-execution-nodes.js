/**
 * PDCA执行节点实现
 * 将现有的PlannerAgent、ExecutorAgent、CheckerAgent逻辑迁移到LangGraph节点
 */

const { NodeResult } = require('../state/workflow-state');
const { IntentValidator } = require('../../pdca/intent-library');
const IntelligentValidationEngine = require('./intelligent-validation-engine');
const AdaptivePromptSystem = require('./adaptive-prompt-system');
const EdgeCaseHandler = require('./edge-case-handler');
const PageStateMonitor = require('./page-state-monitor');
const logger = require('../../utils/logger');

class PDCAExecutionNodes {
    constructor(dependencies = {}) {
        this.llmClient = dependencies.llmClient;
        this.simpleExecutor = dependencies.simpleExecutor;
        this.stateManager = dependencies.stateManager;
        this.ticketManager = dependencies.ticketManager;
        
        // 初始化智能验证引擎
        this.validationEngine = new IntelligentValidationEngine({
            llmClient: this.llmClient,
            simpleExecutor: this.simpleExecutor,
            stateManager: this.stateManager
        });
        
        // 初始化自适应提示词系统
        this.promptSystem = new AdaptivePromptSystem();
        
        // 初始化边缘场景处理器
        this.edgeHandler = new EdgeCaseHandler({
            simpleExecutor: this.simpleExecutor,
            llmClient: this.llmClient,
            stateManager: this.stateManager
        });
        
        // 初始化页面状态监控器
        this.pageMonitor = new PageStateMonitor(this.simpleExecutor);
        
        // 执行计数器
        this.planningId = 0;
        this.executionId = 0;
        this.checkingId = 0;
    }

    /**
     * 规划节点 - PDCA的Plan阶段 (使用自适应提示词)
     */
    async planningNode(state) {
        try {
            this.planningId++;
            logger.info(`🧠 PDCA智能规划阶段开始`, {
                planningId: this.planningId,
                cycle: `${state.cycleCount + 1}/${state.maxCycles}`,
                adaptivePrompt: 'enabled'
            });
            
            // 检查挂起状态
            const suspendCheck = await this.checkSuspendStatus(state);
            if (suspendCheck.shouldSuspend) {
                return new NodeResult(true, suspendCheck, 'suspend_node');
            }
            
            state.updateCurrentNode('planning_node');
            state.currentPhase = 'planning';
            state.updatePDCAPhase('plan');
            state.incrementCycle();
            
            // 检查循环次数限制
            const terminationCheck = state.shouldTerminate();
            if (terminationCheck.terminate) {
                logger.warn(`规划阶段终止: ${terminationCheck.reason}`);
                return this.handleTermination(state, terminationCheck.reason);
            }
            
            // 使用自适应提示词系统生成规划提示词
            const prompt = this.promptSystem.generatePlanningPrompt(state);
            
            // 调用LLM进行规划
            const llmResponse = await this.llmClient.chat(prompt);
            
            // 解析LLM响应
            const plan = this.parsePlanningResponse(llmResponse);
            
            // 验证计划
            this.validatePlan(plan);
            
            // 更新提示词系统的成功率
            const planningSuccess = plan.confidence >= 0.7;
            this.promptSystem.updateSuccessRate('planning', planningSuccess);
            
            // 保存计划到状态
            state.checkpointData.currentPlan = plan;
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                'planning_node'
            );
            
            logger.info(`✅ 智能规划完成`, {
                planningId: this.planningId,
                intent: plan.intent,
                confidence: plan.confidence,
                reasoning: plan.reasoning,
                adaptivePrompt: 'used',
                success: planningSuccess
            });
            
            return new NodeResult(true, plan, 'execution_node');
            
        } catch (error) {
            logger.error('智能规划节点失败:', error);
            
            // 更新提示词系统的失败率
            this.promptSystem.updateSuccessRate('planning', false);
            
            state.addError(error, 'planning_node');
            return new NodeResult(false, { error: error.message }, 'error_recovery_node');
        }
    }

    /**
     * 执行节点 - PDCA的Do阶段 (支持边缘场景处理)
     */
    async executionNode(state) {
        try {
            this.executionId++;
            const startTime = Date.now();
            
            logger.info('🔧 PDCA智能执行阶段开始', {
                executionId: this.executionId,
                edgeHandling: 'enabled'
            });
            
            // 检查挂起状态
            const suspendCheck = await this.checkSuspendStatus(state);
            if (suspendCheck.shouldSuspend) {
                return new NodeResult(true, suspendCheck, 'suspend_node');
            }
            
            state.updateCurrentNode('execution_node');
            state.currentPhase = 'executing';
            state.updatePDCAPhase('do');
            
            // 获取当前计划
            const plan = state.checkpointData.currentPlan;
            if (!plan) {
                throw new Error('没有找到执行计划');
            }
            
            logger.info(`🔧 ExecutorAgent开始执行意图`, {
                executionId: this.executionId,
                intent: plan.intent,
                parameters: plan.parameters,
                edgeHandling: 'enabled'
            });
            
            // 验证意图
            try {
                IntentValidator.validateIntent(plan.intent, plan.parameters);
            } catch (error) {
                const failureEvidence = this.createFailureEvidence(plan, null, error, startTime);
                return new NodeResult(true, failureEvidence, 'validation_node');
            }
            
            // 检查是否需要用户登录
            const currentPageState = await this.pageMonitor.getCurrentPageState();
            const needsLogin = await this.pageMonitor.isLoginRequired(currentPageState);
            
            if (needsLogin) {
                logger.info('🔐 检测到需要登录，等待用户完成登录...');
                
                // 等待用户登录
                const loginResult = await this.pageMonitor.waitForUserLogin();
                
                if (loginResult.success) {
                    logger.info('✅ 用户已完成登录，重新制定计划');
                    
                    // 清空之前的计划和证据
                    state.checkpointData.currentPlan = null;
                    state.checkpointData.currentEvidence = null;
                    
                    // 返回到计划节点重新开始
                    return new NodeResult(true, {
                        reason: '用户登录成功，需要重新评估任务',
                        loginResult
                    }, 'plan_node');
                } else {
                    logger.warn('⚠️ 用户登录超时或失败');
                    return new NodeResult(true, {
                        reason: '等待用户登录超时',
                        needsIntervention: true
                    }, 'cleanup_node');
                }
            }
            
            // 捕获执行前状态
            const beforeState = await this.captureState('before_execution');
            
            // 执行意图对应的原子操作（支持边缘场景处理）
            const executionResult = await this.executeIntentWithEdgeHandling(plan, state);
            
            // 捕获执行后状态
            const afterState = await this.captureState('after_execution');
            
            // 创建执行证据
            const evidence = this.createSuccessEvidence(
                plan, 
                beforeState, 
                afterState, 
                executionResult, 
                startTime
            );
            
            // 更新状态
            state.checkpointData.currentEvidence = evidence;
            state.updateStats(evidence.success, Date.now() - startTime);
            
            logger.info(`✅ 智能执行完成`, {
                executionId: this.executionId,
                intent: plan.intent,
                success: evidence.success,
                duration: Date.now() - startTime,
                edgeHandling: 'used'
            });
            
            return new NodeResult(true, evidence, 'validation_node');
            
        } catch (error) {
            logger.error('智能执行节点失败:', error);
            
            // 使用边缘场景处理器进行错误恢复
            const recoveryResult = await this.edgeHandler.performErrorRecovery(error, {
                currentNode: 'execution_node',
                ticketId: state.ticketId,
                cycleCount: state.cycleCount
            });
            
            if (recoveryResult.success) {
                logger.info('✅ 错误恢复成功，继续执行');
                // 如果恢复成功，可以选择重试或继续
                return new NodeResult(true, { 
                    recovered: true, 
                    originalError: error.message,
                    recoveryResult: recoveryResult
                }, 'validation_node');
            } else {
                logger.error('❌ 错误恢复失败');
                state.addError(error, 'execution_node');
                
                // 创建失败证据
                const plan = state.checkpointData.currentPlan || {};
                const failureEvidence = this.createFailureEvidence(plan, null, error, Date.now());
                
                return new NodeResult(true, failureEvidence, 'validation_node');
            }
        }
    }

    /**
     * 验证节点 - PDCA的Check阶段 (使用三层智能验证)
     */
    async validationNode(state) {
        try {
            this.checkingId++;
            logger.info('🔍 PDCA智能验证阶段开始', {
                checkingId: this.checkingId,
                cycleCount: state.cycleCount
            });
            
            state.updateCurrentNode('validation_node');
            state.currentPhase = 'validating';
            state.updatePDCAPhase('check');
            
            // 获取计划和证据
            const plan = state.checkpointData.currentPlan;
            const evidence = state.checkpointData.currentEvidence;
            
            if (!plan || !evidence) {
                throw new Error('没有找到计划或执行证据');
            }
            
            logger.info(`🔍 启动三层智能验证`, {
                checkingId: this.checkingId,
                intent: plan.intent,
                executionSuccess: evidence.success,
                validationEngine: 'IntelligentValidationEngine'
            });
            
            // 使用智能验证引擎进行三层验证
            const validationResult = await this.validationEngine.performThreeLayerValidation(
                plan, 
                evidence, 
                state
            );
            
            // 保存验证结果
            state.checkpointData.currentValidation = validationResult;
            
            // 记录验证统计
            const stats = this.validationEngine.getValidationStats();
            
            logger.info(`✅ 三层智能验证完成`, {
                checkingId: this.checkingId,
                status: validationResult.status,
                score: validationResult.score,
                confidence: validationResult.confidence,
                nextAction: validationResult.nextAction,
                l1Passed: validationResult.layerResults.l1.passed,
                l2Passed: validationResult.layerResults.l2.passed,
                l3Passed: validationResult.layerResults.l3.passed,
                validationStats: stats
            });
            
            return new NodeResult(true, validationResult, 'decision_node');
            
        } catch (error) {
            logger.error('智能验证节点失败:', error);
            state.addError(error, 'validation_node');
            
            // 返回保守的验证结果
            const fallbackResult = {
                status: 'VALIDATION_ERROR',
                reason: `智能验证失败: ${error.message}`,
                confidence: 0.2,
                nextAction: 'CONTINUE',
                score: 0.3,
                layerResults: {
                    l1: { passed: false, error: error.message },
                    l2: { passed: false, error: error.message },
                    l3: { passed: false, error: error.message }
                },
                suggestions: '验证引擎出错，建议继续执行并观察',
                fallback: true
            };
            
            return new NodeResult(true, fallbackResult, 'decision_node');
        }
    }

    /**
     * 决策节点 - 决定下一步行动 (支持三层验证结果)
     */
    async decisionNode(state) {
        try {
            logger.info('⚡ PDCA智能决策阶段');
            
            state.updateCurrentNode('decision_node');
            
            const validation = state.checkpointData.currentValidation;
            
            if (!validation) {
                return new NodeResult(true, { decision: 'error', error: '没有验证结果' });
            }
            
            // 根据三层验证结果决定下一步
            let decision = 'continue'; // 默认继续循环
            
            logger.info('🎯 分析三层验证结果', {
                validationScore: validation.score,
                validationStatus: validation.status,
                l1Passed: validation.layerResults?.l1?.passed,
                l2Passed: validation.layerResults?.l2?.passed,
                l3Passed: validation.layerResults?.l3?.passed,
                recommendedAction: validation.nextAction
            });
            
            // 基于验证引擎的推荐动作
            switch (validation.nextAction) {
                case 'TASK_COMPLETED':
                    decision = 'complete';
                    logger.info('✅ 决策：任务完成', {
                        reason: 'L3智能层确认任务已完成',
                        confidence: validation.confidence
                    });
                    break;
                    
                case 'USER_INTERVENTION_REQUIRED':
                    decision = 'intervention';
                    logger.info('🤝 决策：需要用户干预', {
                        reason: validation.reason,
                        confidence: validation.confidence
                    });
                    
                    // 触发用户干预请求
                    state.requestUserIntervention(
                        validation.reason,
                        validation.suggestions ? [validation.suggestions] : []
                    );
                    break;
                    
                case 'CRITICAL_ERROR':
                    decision = 'error';
                    logger.error('❌ 决策：发生严重错误', {
                        reason: validation.reason,
                        confidence: validation.confidence
                    });
                    break;
                    
                case 'STRATEGY_CHANGE':
                    // 对于策略变更，当前继续执行，但记录建议
                    decision = 'continue';
                    logger.info('🔄 决策：建议策略变更', {
                        reason: validation.reason,
                        suggestions: validation.suggestions,
                        confidence: validation.confidence
                    });
                    break;
                    
                case 'CONTINUE':
                default:
                    decision = 'continue';
                    logger.info('➡️ 决策：继续执行', {
                        reason: validation.reason,
                        confidence: validation.confidence,
                        validationScore: validation.score
                    });
                    break;
            }
            
            // 附加决策逻辑：基于验证分数调整决策
            if (validation.score < 0.3 && decision === 'continue') {
                decision = 'error';
                logger.warn('⚠️ 决策调整：验证分数过低，改为错误处理', {
                    originalDecision: 'continue',
                    validationScore: validation.score,
                    newDecision: 'error'
                });
            }
            
            // 记录决策统计
            const decisionResult = {
                decision: decision,
                validation: validation,
                validationScore: validation.score,
                confidence: validation.confidence,
                layerResults: {
                    l1: validation.layerResults?.l1?.passed || false,
                    l2: validation.layerResults?.l2?.passed || false,
                    l3: validation.layerResults?.l3?.passed || false
                },
                reasoning: validation.reason,
                suggestions: validation.suggestions
            };
            
            logger.info(`🎯 决策完成: ${decision}`, {
                validationScore: validation.score,
                confidence: validation.confidence,
                layersPassed: `L1:${decisionResult.layerResults.l1} L2:${decisionResult.layerResults.l2} L3:${decisionResult.layerResults.l3}`,
                nextAction: validation.nextAction
            });
            
            return new NodeResult(true, decisionResult);
            
        } catch (error) {
            logger.error('智能决策节点失败:', error);
            return new NodeResult(true, { 
                decision: 'error', 
                error: error.message,
                fallback: true
            });
        }
    }

    /**
     * 行动节点 - PDCA的Act阶段
     */
    async actionNode(state) {
        try {
            logger.info('⚡ PDCA行动阶段');
            
            state.updateCurrentNode('action_node');
            state.updatePDCAPhase('act');
            
            // 更新状态，准备下一轮循环
            this.updateStateForNextCycle(state);
            
            // 压缩历史记录
            state.compressHistory();
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                'action_node'
            );
            
            logger.info(`准备下一轮PDCA循环 (${state.cycleCount}/${state.maxCycles})`);
            
            return new NodeResult(true, { 
                cycleCompleted: true,
                nextCycle: state.cycleCount + 1
            }, 'planning_node');
            
        } catch (error) {
            logger.error('行动节点失败:', error);
            state.addError(error, 'action_node');
            return new NodeResult(false, { error: error.message }, 'error_recovery_node');
        }
    }

    // ===== 辅助方法 =====

    /**
     * 检查挂起状态
     */
    async checkSuspendStatus(state) {
        try {
            const ticket = await this.ticketManager.getTicket(state.ticketId);
            
            if (ticket && ticket.status === '已挂起') {
                logger.info(`工单已挂起: ${state.ticketId}`);
                return { shouldSuspend: true, reason: '工单已挂起' };
            }
            
            if (state.shouldSuspend) {
                return { shouldSuspend: true, reason: '内部请求挂起' };
            }
            
            return { shouldSuspend: false };
            
        } catch (error) {
            logger.error('检查挂起状态失败:', error);
            return { shouldSuspend: false };
        }
    }

    /**
     * 构建规划提示词
     */
    buildPlanningPrompt(state) {
        const context = {
            task_goal: state.taskGoal,
            cycle: state.cycleCount,
            execution_history: state.executionContext.evidenceHistory || [],
            current_state: state.executionContext.browserState,
            errors: state.errorHistory.slice(-3) // 最近的3个错误
        };

        return `# RPA任务规划器

## 当前任务目标
${context.task_goal}

## 当前状态
- 执行周期: ${context.cycle}
- 浏览器状态: ${JSON.stringify(context.current_state, null, 2)}

## 执行历史
${context.execution_history.slice(-3).map((item, index) => 
    `${index + 1}. ${item.intent || '未知操作'} - ${item.success ? '成功' : '失败'}`
).join('\n')}

## 最近错误
${context.errors.map(error => `- ${error.error}`).join('\n')}

请分析当前状态并规划下一步操作。返回JSON格式：
{
  "intent": "操作意图",
  "parameters": {},
  "reasoning": "规划理由",
  "confidence": 0.8
}

可用的操作意图：
- NAVIGATE: 导航到页面
- CLICK: 点击元素
- TYPE: 输入文本
- WAIT: 等待
- SCREENSHOT: 截图
- TASK_COMPLETED: 任务完成
- USER_INTERVENTION_REQUIRED: 需要用户干预`;
    }

    /**
     * 解析规划响应
     */
    parsePlanningResponse(response) {
        try {
            // 清理响应内容
            let cleanResponse = response.trim();
            
            // 移除markdown标记
            if (cleanResponse.startsWith('```json')) {
                cleanResponse = cleanResponse.replace(/```json\n?/, '').replace(/\n?```$/, '');
            }
            
            const plan = JSON.parse(cleanResponse);
            
            // 设置默认值
            return {
                intent: plan.intent || 'WAIT',
                parameters: plan.parameters || {},
                reasoning: plan.reasoning || '无具体原因',
                confidence: plan.confidence || 0.5
            };
            
        } catch (error) {
            logger.error('解析规划响应失败:', error);
            
            // 返回默认计划
            return {
                intent: 'WAIT',
                parameters: { duration: 3000 },
                reasoning: '解析失败，等待观察',
                confidence: 0.3
            };
        }
    }

    /**
     * 验证计划
     */
    validatePlan(plan) {
        const validIntents = [
            'NAVIGATE', 'CLICK', 'TYPE', 'WAIT', 'SCREENSHOT',
            'TASK_COMPLETED', 'USER_INTERVENTION_REQUIRED'
        ];
        
        if (!validIntents.includes(plan.intent)) {
            throw new Error(`无效的操作意图: ${plan.intent}`);
        }
        
        if (plan.confidence < 0 || plan.confidence > 1) {
            plan.confidence = 0.5;
        }
    }

    /**
     * 带边缘场景处理的意图执行
     */
    async executeIntentWithEdgeHandling(plan, state) {
        try {
            logger.info(`🔧 执行意图（支持边缘场景处理）`, {
                intent: plan.intent,
                parameters: plan.parameters
            });
            
            // 根据意图类型执行对应的操作
            switch (plan.intent) {
                case 'NAVIGATE':
                    return await this.executeNavigateWithEdgeHandling(plan.parameters);
                    
                case 'CLICK':
                    return await this.executeClickWithEdgeHandling(plan.parameters);
                    
                case 'TYPE':
                    return await this.executeTypeWithEdgeHandling(plan.parameters);
                    
                case 'WAIT':
                    return await this.executeWaitWithEdgeHandling(plan.parameters);
                    
                case 'SCREENSHOT':
                    return await this.executeScreenshotWithEdgeHandling(plan.parameters);
                    
                case 'TASK_COMPLETED':
                    return { success: true, completed: true };
                    
                case 'USER_INTERVENTION_REQUIRED':
                    return { success: true, needsIntervention: true };
                    
                default:
                    throw new Error(`不支持的操作意图: ${plan.intent}`);
            }
            
        } catch (error) {
            logger.error('意图执行失败:', error);
            
            // 使用边缘场景处理器进行错误恢复
            const recoveryResult = await this.edgeHandler.performErrorRecovery(error, {
                currentNode: 'execution_node',
                intent: plan.intent,
                parameters: plan.parameters
            });
            
            if (recoveryResult.success) {
                logger.info('✅ 意图执行错误恢复成功');
                return { 
                    success: true, 
                    recovered: true, 
                    originalError: error.message,
                    recoveryResult: recoveryResult
                };
            } else {
                logger.error('❌ 意图执行错误恢复失败');
                throw error;
            }
        }
    }
    
    /**
     * 带边缘场景处理的导航操作
     */
    async executeNavigateWithEdgeHandling(parameters) {
        const url = parameters.url || 'https://uat-merchant.aomiapp.com/#/bdlogin';
        
        try {
            logger.info(`🌐 导航到: ${url}`);
            
            // 使用SimpleExecutor的client执行导航
            const result = await this.simpleExecutor.client.navigate(url);
            
            // 使用动态等待确保页面加载完成
            const waitResult = await this.edgeHandler.performDynamicWait({
                type: 'PAGE_LOAD',
                maxWait: 15000
            });
            
            if (!waitResult.satisfied) {
                logger.warn('页面加载等待未完全满足，但继续执行');
            }
            
            return { 
                success: result.success, 
                action: 'navigate', 
                url: result.url,
                title: result.title,
                waitResult: waitResult
            };
            
        } catch (error) {
            logger.error(`导航失败: ${url}`, error);
            throw error;
        }
    }
    
    /**
     * 带边缘场景处理的点击操作
     */
    async executeClickWithEdgeHandling(parameters) {
        const { element, ref } = parameters;
        
        if (!ref) {
            throw new Error('点击操作缺少ref参数');
        }
        
        try {
            logger.info(`🖱️ 点击元素: ${element || ref}`);
            
            // 使用动态等待确保元素可点击
            const waitResult = await this.edgeHandler.performDynamicWait({
                type: 'ELEMENT_CLICKABLE',
                selector: ref,
                maxWait: 8000
            });
            
            if (!waitResult.satisfied) {
                logger.warn('元素可点击性等待未完全满足，尝试点击');
            }
            
            // 使用SimpleExecutor的client执行点击
            const result = await this.simpleExecutor.client.click(ref);
            
            // 点击后等待响应
            await this.edgeHandler.performDynamicWait({
                type: 'NETWORK_IDLE',
                maxWait: 5000
            });
            
            return { 
                success: true, 
                action: 'click', 
                element, 
                ref,
                waitResult: waitResult
            };
            
        } catch (error) {
            logger.error(`点击失败: ${element || ref}`, error);
            throw error;
        }
    }
    
    /**
     * 带边缘场景处理的输入操作
     */
    async executeTypeWithEdgeHandling(parameters) {
        const { element, ref, text } = parameters;
        
        if (!ref || !text) {
            throw new Error('输入操作缺少必要参数');
        }
        
        try {
            logger.info(`⌨️ 输入文本到: ${element || ref}`);
            
            // 使用动态等待确保元素可见
            const waitResult = await this.edgeHandler.performDynamicWait({
                type: 'ELEMENT_VISIBLE',
                selector: ref,
                maxWait: 6000
            });
            
            if (!waitResult.satisfied) {
                logger.warn('元素可见性等待未完全满足，尝试输入');
            }
            
            // 使用SimpleExecutor的client执行输入
            const result = await this.simpleExecutor.client.type(ref, text);
            
            // 输入后短暂等待
            await this.sleep(500);
            
            return { 
                success: true, 
                action: 'type', 
                element, 
                text: text.length > 50 ? text.substring(0, 50) + '...' : text,
                waitResult: waitResult
            };
            
        } catch (error) {
            logger.error(`输入失败: ${element || ref}`, error);
            throw error;
        }
    }
    
    /**
     * 带边缘场景处理的等待操作
     */
    async executeWaitWithEdgeHandling(parameters) {
        const duration = parameters.duration || 3000;
        const waitType = parameters.waitType || 'CUSTOM_CONDITION';
        
        try {
            logger.info(`⏳ 智能等待: ${duration}ms, 类型: ${waitType}`);
            
            // 使用动态等待而不是简单的sleep
            const waitResult = await this.edgeHandler.performDynamicWait({
                type: waitType,
                maxWait: duration,
                customCondition: parameters.customCondition
            });
            
            return { 
                success: true, 
                action: 'wait', 
                duration: waitResult.duration,
                satisfied: waitResult.satisfied,
                waitType: waitType
            };
            
        } catch (error) {
            logger.error(`等待失败: ${duration}ms`, error);
            throw error;
        }
    }
    
    /**
     * 带边缘场景处理的截图操作
     */
    async executeScreenshotWithEdgeHandling(parameters) {
        try {
            logger.info(`📸 截图操作`);
            
            // 截图前等待页面稳定
            await this.edgeHandler.performDynamicWait({
                type: 'DYNAMIC_CONTENT',
                maxWait: 3000
            });
            
            // 使用SimpleExecutor的client执行截图
            const result = await this.simpleExecutor.client.screenshot();
            
            return { 
                success: result.success, 
                action: 'screenshot', 
                screenshot: result.screenshot?.substring(0, 100) + '...' // 截取部分用于日志
            };
            
        } catch (error) {
            logger.error('截图失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取边缘场景处理统计
     */
    getEdgeStats() {
        return {
            edgeHandlerStats: this.edgeHandler.getEdgeStats(),
            validationStats: this.validationEngine.getValidationStats(),
            promptStats: this.promptSystem.getPromptStats()
        };
    }
    
    /**
     * 重置边缘场景统计
     */
    resetEdgeStats() {
        this.edgeHandler.resetStats();
    }

    /**
     * 执行意图
     */
    async executeIntent(plan, state) {
        switch (plan.intent) {
            case 'NAVIGATE':
                return await this.executeNavigate(plan.parameters);
                
            case 'CLICK':
                return await this.executeClick(plan.parameters);
                
            case 'TYPE':
                return await this.executeType(plan.parameters);
                
            case 'WAIT':
                return await this.executeWait(plan.parameters);
                
            case 'SCREENSHOT':
                return await this.executeScreenshot(plan.parameters);
                
            case 'TASK_COMPLETED':
                return { success: true, completed: true };
                
            case 'USER_INTERVENTION_REQUIRED':
                return { success: true, needsIntervention: true };
                
            default:
                throw new Error(`不支持的操作意图: ${plan.intent}`);
        }
    }

    /**
     * 执行导航操作
     */
    async executeNavigate(parameters) {
        const url = parameters.url || 'https://uat-merchant.aomiapp.com/#/bdlogin';
        
        await this.mcpClient.callTool('browser_navigate', { url });
        await this.sleep(3000); // 等待页面加载
        
        return { success: true, action: 'navigate', url: url };
    }

    /**
     * 执行点击操作
     */
    async executeClick(parameters) {
        const { element, ref } = parameters;
        
        if (!ref) {
            throw new Error('点击操作缺少ref参数');
        }
        
        await this.mcpClient.callTool('browser_click', { element, ref });
        await this.sleep(1000); // 等待响应
        
        return { success: true, action: 'click', element, ref };
    }

    /**
     * 执行输入操作
     */
    async executeType(parameters) {
        const { element, ref, text } = parameters;
        
        if (!ref || !text) {
            throw new Error('输入操作缺少必要参数');
        }
        
        await this.mcpClient.callTool('browser_type', { 
            element, 
            ref, 
            text, 
            submit: false 
        });
        
        return { success: true, action: 'type', element, text: text.length > 50 ? text.substring(0, 50) + '...' : text };
    }

    /**
     * 执行等待操作
     */
    async executeWait(parameters) {
        const duration = parameters.duration || 3000;
        
        await this.sleep(duration);
        
        return { success: true, action: 'wait', duration };
    }

    /**
     * 执行截图操作
     */
    async executeScreenshot(parameters) {
        const screenshot = await this.mcpClient.callTool('browser_screenshot', {});
        
        return { 
            success: true, 
            action: 'screenshot', 
            screenshot: screenshot?.substring(0, 100) + '...' // 截取部分用于日志
        };
    }

    /**
     * 捕获状态
     */
    async captureState(phase) {
        try {
            // 使用SimpleExecutor的client获取页面快照
            const snapshot = await this.simpleExecutor.client.snapshot();
            
            return {
                phase: phase,
                timestamp: new Date().toISOString(),
                snapshot: snapshot,
                url: snapshot?.url || 'current_page',
                title: snapshot?.title || '',
                elements: snapshot?.elements || {}
            };
            
        } catch (error) {
            logger.error('捕获状态失败:', error);
            return {
                phase: phase,
                timestamp: new Date().toISOString(),
                error: error.message
            };
        }
    }

    /**
     * 创建成功证据
     */
    createSuccessEvidence(plan, beforeState, afterState, executionResult, startTime) {
        return {
            success: true,
            plan: plan,
            beforeState: beforeState,
            afterState: afterState,
            executionResult: executionResult,
            duration: Date.now() - startTime,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 创建失败证据
     */
    createFailureEvidence(plan, beforeState, error, startTime) {
        return {
            success: false,
            plan: plan,
            beforeState: beforeState,
            error: {
                message: error.message || error,
                stack: error.stack
            },
            duration: Date.now() - startTime,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 执行基础检查
     */
    performBasicCheck(plan, evidence) {
        // 基础成功性检查
        if (!evidence.success) {
            return {
                status: 'FAILURE',
                reason: '执行失败',
                confidence: 0.9,
                nextAction: 'CONTINUE',
                suggestions: '重试执行'
            };
        }
        
        // 特殊意图检查
        if (plan.intent === 'TASK_COMPLETED') {
            return {
                status: 'SUCCESS',
                reason: '任务完成',
                confidence: 0.95,
                nextAction: 'TASK_COMPLETED'
            };
        }
        
        if (plan.intent === 'USER_INTERVENTION_REQUIRED') {
            return {
                status: 'SUCCESS',
                reason: '需要用户干预',
                confidence: 0.9,
                nextAction: 'USER_INTERVENTION_REQUIRED'
            };
        }
        
        return {
            status: 'SUCCESS',
            reason: '基础检查通过',
            confidence: 0.7,
            nextAction: 'CONTINUE'
        };
    }

    /**
     * 执行深度检查
     */
    async performDeepCheck(plan, evidence, state) {
        try {
            // 构建检查提示词
            const prompt = this.buildCheckingPrompt(plan, evidence, state);
            
            // 调用LLM进行深度分析
            const response = await this.llmClient.chat(prompt);
            
            // 解析检查结果
            return this.parseCheckingResponse(response);
            
        } catch (error) {
            logger.error('深度检查失败:', error);
            
            return {
                status: 'NEEDS_VERIFICATION',
                reason: '深度检查失败',
                confidence: 0.4,
                nextAction: 'CONTINUE'
            };
        }
    }

    /**
     * 构建检查提示词
     */
    buildCheckingPrompt(plan, evidence, state) {
        return `# RPA执行结果检查

## 执行计划
- 意图: ${plan.intent}
- 参数: ${JSON.stringify(plan.parameters)}
- 理由: ${plan.reasoning}

## 执行结果
- 成功: ${evidence.success}
- 操作: ${evidence.executionResult?.action || '未知'}
- 耗时: ${evidence.duration}ms

## 任务目标
${state.taskGoal}

## 当前周期
${state.cycleCount}/${state.maxCycles}

请分析执行结果是否符合预期，返回JSON格式：
{
  "status": "SUCCESS|FAILURE|NEEDS_VERIFICATION",
  "reason": "检查理由",
  "confidence": 0.8,
  "nextAction": "CONTINUE|TASK_COMPLETED|USER_INTERVENTION_REQUIRED|CRITICAL_ERROR",
  "suggestions": "改进建议"
}`;
    }

    /**
     * 解析检查响应
     */
    parseCheckingResponse(response) {
        try {
            let cleanResponse = response.trim();
            
            if (cleanResponse.startsWith('```json')) {
                cleanResponse = cleanResponse.replace(/```json\n?/, '').replace(/\n?```$/, '');
            }
            
            const result = JSON.parse(cleanResponse);
            
            return {
                status: result.status || 'NEEDS_VERIFICATION',
                reason: result.reason || '无具体原因',
                confidence: result.confidence || 0.5,
                nextAction: result.nextAction || 'CONTINUE',
                suggestions: result.suggestions || '无建议'
            };
            
        } catch (error) {
            logger.error('解析检查响应失败:', error);
            
            return {
                status: 'NEEDS_VERIFICATION',
                reason: '解析失败',
                confidence: 0.3,
                nextAction: 'CONTINUE'
            };
        }
    }

    /**
     * 合并检查结果
     */
    combineCheckResults(basicCheck, deepCheck) {
        // 如果基础检查失败，以基础检查为准
        if (basicCheck.status === 'FAILURE') {
            return basicCheck;
        }
        
        // 如果深度检查置信度更高，以深度检查为准
        if (deepCheck.confidence > basicCheck.confidence) {
            return deepCheck;
        }
        
        // 否则以基础检查为准，但合并信息
        return {
            ...basicCheck,
            deepCheckReason: deepCheck.reason,
            suggestions: deepCheck.suggestions
        };
    }

    /**
     * 处理终止情况
     */
    handleTermination(state, reason) {
        switch (reason) {
            case 'MAX_CYCLES_REACHED':
                return new NodeResult(true, { 
                    decision: 'error', 
                    error: '达到最大循环次数' 
                }, 'error_recovery_node');
                
            case 'MAX_CONSECUTIVE_FAILURES':
                return new NodeResult(true, { 
                    decision: 'error', 
                    error: '连续失败次数过多' 
                }, 'error_recovery_node');
                
            case 'SUSPENDED':
                return new NodeResult(true, { 
                    decision: 'suspend' 
                }, 'suspend_node');
                
            case 'USER_INTERVENTION_REQUIRED':
                return new NodeResult(true, { 
                    decision: 'intervention' 
                }, 'user_intervention_node');
                
            default:
                return new NodeResult(true, { 
                    decision: 'error', 
                    error: '未知终止原因' 
                }, 'error_recovery_node');
        }
    }

    /**
     * 更新状态为下一轮循环
     */
    updateStateForNextCycle(state) {
        // 重置连续失败计数（如果本轮成功）
        const validation = state.checkpointData.currentValidation;
        if (validation && validation.status === 'SUCCESS') {
            state.resetConsecutiveFailures();
        }
        
        // 添加执行记录到历史
        const executionRecord = {
            cycle: state.cycleCount,
            plan: state.checkpointData.currentPlan,
            evidence: state.checkpointData.currentEvidence,
            validation: validation,
            timestamp: new Date().toISOString()
        };
        
        state.executionContext.evidenceHistory.push(executionRecord);
        
        // 清除当前周期的临时数据
        delete state.checkpointData.currentPlan;
        delete state.checkpointData.currentEvidence;
        delete state.checkpointData.currentValidation;
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = PDCAExecutionNodes;