/**
 * 核心控制节点实现
 * 包含恢复检查、队列管理、挂起检查等控制逻辑
 */

const { NodeResult } = require('../state/workflow-state');
const logger = require('../../utils/logger');

class CoreControlNodes {
    constructor(dependencies = {}) {
        this.stateManager = dependencies.stateManager;
        this.ticketManager = dependencies.ticketManager;
        this.mcpClient = dependencies.mcpClient;
        this.messageQueue = dependencies.messageQueue;
    }

    /**
     * 恢复检查节点 - AI助手重启时的状态恢复
     */
    async recoveryCheckNode(state) {
        try {
            logger.info('🔄 执行恢复检查...');
            
            state.updateCurrentNode('recovery_check_node');
            
            // 1. 检查数据库中"处理中"状态的工单
            const processingTickets = await this.getProcessingTickets();
            logger.info(`发现 ${processingTickets.length} 个处理中的工单`);
            
            // 2. 检查当前工单是否有挂起的工作流状态
            const existingWorkflow = await this.stateManager.getWorkflowStateByTicketId(state.ticketId);
            
            if (existingWorkflow) {
                logger.info(`发现已存在的工作流: ${existingWorkflow.workflow_id}, 状态: ${existingWorkflow.status}`);
                
                if (existingWorkflow.status === 'suspended') {
                    // 恢复挂起的工作流
                    await this.resumeSuspendedWorkflow(state, existingWorkflow);
                } else if (existingWorkflow.status === 'active') {
                    // 可能是异常中断的工作流，决定是否恢复
                    await this.handleInterruptedWorkflow(state, existingWorkflow);
                }
            }
            
            // 3. 检查其他处理中的工单是否需要状态重置
            await this.cleanupOrphanedTickets(processingTickets, state.ticketId);
            
            // 4. 保存恢复检查结果
            state.checkpointData.recoveryInfo = {
                existingWorkflow: existingWorkflow ? existingWorkflow.workflow_id : null,
                processingTicketsCount: processingTickets.length,
                recoveryAction: existingWorkflow ? 'resumed' : 'new_start',
                timestamp: new Date().toISOString()
            };
            
            return new NodeResult(true, {
                existingWorkflow: existingWorkflow,
                recoveryAction: existingWorkflow ? 'resumed' : 'new_start'
            });
            
        } catch (error) {
            logger.error('恢复检查失败:', error);
            state.addError(error, 'recovery_check_node');
            return new NodeResult(false, { error: error.message });
        }
    }

    /**
     * 队列管理节点 - 处理工单队列逻辑
     */
    async queueManagementNode(state) {
        try {
            logger.info('📋 执行队列管理...');
            
            state.updateCurrentNode('queue_management_node');
            state.currentPhase = 'queue';
            
            // 1. 获取并验证工单信息
            const ticket = await this.ticketManager.getTicket(state.ticketId);
            if (!ticket) {
                throw new Error(`工单不存在: ${state.ticketId}`);
            }
            
            // 2. 检查工单状态是否适合处理
            const validStatuses = ['排队中', '待开始', '处理中'];
            if (!validStatuses.includes(ticket.status)) {
                logger.warn(`工单状态不适合处理: ${ticket.status}`);
                return new NodeResult(false, { 
                    error: `工单状态不适合处理: ${ticket.status}`,
                    action: 'skip'
                });
            }
            
            // 3. 更新工单状态为处理中
            if (ticket.status !== '处理中') {
                await this.ticketManager.updateTicketStatus(state.ticketId, '处理中', {
                    notes: 'LangGraph RPA系统开始处理工单',
                    processing_started_at: new Date().toISOString()
                });
                logger.info(`更新工单状态: ${state.ticketId} -> 处理中`);
            }
            
            // 4. 设置工单相关状态
            state.ticket = ticket;
            state.originalContent = ticket.content;
            state.taskGoal = `${ticket.title}: ${ticket.content}`;
            
            // 5. 初始化执行上下文
            this.initializeExecutionContext(state, ticket);
            
            // 6. 检查队列优先级和并发限制
            const queueInfo = await this.checkQueueConstraints();
            state.checkpointData.queueInfo = queueInfo;
            
            // 7. 保存队列管理状态
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                'queue_management_node'
            );
            
            logger.info(`✅ 队列管理完成: ${ticket.title}`);
            return new NodeResult(true, { 
                ticket: ticket,
                queueInfo: queueInfo
            });
            
        } catch (error) {
            logger.error('队列管理失败:', error);
            state.addError(error, 'queue_management_node');
            
            // 尝试回滚工单状态
            try {
                await this.ticketManager.updateTicketStatus(state.ticketId, '待开始', {
                    notes: `处理失败，回滚状态: ${error.message}`
                });
            } catch (rollbackError) {
                logger.error('回滚工单状态失败:', rollbackError);
            }
            
            return new NodeResult(false, { error: error.message });
        }
    }

    /**
     * 挂起检查节点 - 在每个关键节点检查挂起状态
     */
    async suspendCheckNode(state) {
        try {
            // 1. 检查工单状态是否变为"已挂起"
            const currentTicket = await this.ticketManager.getTicket(state.ticketId);
            
            if (currentTicket && currentTicket.status === '已挂起') {
                logger.info(`检测到工单挂起: ${state.ticketId}`);
                
                // 2. 设置挂起标志
                state.requestSuspend();
                
                // 3. 保存当前状态到检查点
                await this.stateManager.saveCheckpoint(
                    state.workflowId,
                    state.createCheckpoint(),
                    state.currentNode
                );
                
                return { shouldSuspend: true, reason: '工单已挂起' };
            }
            
            // 4. 检查内部挂起标志
            if (state.shouldSuspend) {
                logger.info(`检测到内部挂起请求: ${state.ticketId}`);
                return { shouldSuspend: true, reason: '内部请求挂起' };
            }
            
            // 5. 检查用户干预标志
            if (state.needsUserIntervention) {
                logger.info(`检测到用户干预需求: ${state.interventionReason}`);
                return { shouldSuspend: true, reason: '需要用户干预' };
            }
            
            return { shouldSuspend: false };
            
        } catch (error) {
            logger.error('挂起检查失败:', error);
            // 挂起检查失败时，为安全起见选择不挂起
            return { shouldSuspend: false, error: error.message };
        }
    }

    /**
     * 清理节点 - 清理资源和完成工作流
     */
    async cleanupNode(state) {
        try {
            logger.info('🧹 开始资源清理...');
            
            state.updateCurrentNode('cleanup_node');
            
            // 1. 清理浏览器资源
            await this.cleanupBrowserResources(state);
            
            // 2. 清理临时文件
            await this.cleanupTemporaryFiles(state);
            
            // 3. 更新最终统计信息
            const finalStats = this.calculateFinalStats(state);
            state.checkpointData.finalStats = finalStats;
            
            // 4. 生成执行摘要
            const executionSummary = this.generateExecutionSummary(state);
            
            // 5. 记录完成日志
            logger.info(`✅ 工作流清理完成: ${state.workflowId}`);
            logger.info(`执行摘要: ${executionSummary}`);
            
            return new NodeResult(true, {
                cleaned: true,
                finalStats: finalStats,
                executionSummary: executionSummary
            });
            
        } catch (error) {
            logger.error('资源清理失败:', error);
            // 清理失败不影响工作流完成
            return new NodeResult(true, { 
                cleaned: false, 
                error: error.message 
            });
        }
    }

    // ===== 辅助方法 =====

    /**
     * 获取处理中的工单列表
     */
    async getProcessingTickets() {
        try {
            const tickets = await this.ticketManager.getTickets({
                status: '处理中',
                pageSize: 100
            });
            return tickets.tickets || [];
        } catch (error) {
            logger.error('获取处理中工单失败:', error);
            return [];
        }
    }

    /**
     * 恢复挂起的工作流
     */
    async resumeSuspendedWorkflow(state, existingWorkflow) {
        logger.info(`恢复挂起的工作流: ${existingWorkflow.workflow_id}`);
        
        // 恢复工作流状态
        await this.stateManager.resumeWorkflow(existingWorkflow.workflow_id);
        
        // 合并状态数据
        if (existingWorkflow.checkpoint_data) {
            const savedState = JSON.parse(existingWorkflow.checkpoint_data);
            Object.assign(state, savedState.state || savedState);
        }
        
        state.workflowId = existingWorkflow.workflow_id;
        
        logger.info(`✅ 工作流恢复完成，从节点: ${existingWorkflow.current_node}`);
    }

    /**
     * 处理中断的工作流
     */
    async handleInterruptedWorkflow(state, existingWorkflow) {
        const lastActiveTime = new Date(existingWorkflow.last_active_at);
        const timeSinceActive = Date.now() - lastActiveTime.getTime();
        
        // 如果超过5分钟没有活动，认为是异常中断
        if (timeSinceActive > 5 * 60 * 1000) {
            logger.warn(`发现异常中断的工作流: ${existingWorkflow.workflow_id}`);
            
            // 标记为失败并重新开始
            await this.stateManager.failWorkflow(
                existingWorkflow.workflow_id, 
                new Error('工作流异常中断，重新开始处理')
            );
            
            // 创建新的工作流ID
            state.workflowId = null;
        } else {
            // 最近还在活动，可能是正常的恢复
            await this.resumeSuspendedWorkflow(state, existingWorkflow);
        }
    }

    /**
     * 清理孤儿工单
     */
    async cleanupOrphanedTickets(processingTickets, currentTicketId) {
        for (const ticket of processingTickets) {
            if (ticket.id === currentTicketId) continue;
            
            // 检查是否有对应的活跃工作流
            const workflow = await this.stateManager.getWorkflowStateByTicketId(ticket.id);
            
            if (!workflow || workflow.status === 'failed') {
                logger.warn(`发现孤儿工单，重置状态: ${ticket.id}`);
                
                try {
                    await this.ticketManager.updateTicketStatus(ticket.id, '待开始', {
                        notes: '系统重启时发现异常状态，自动重置'
                    });
                } catch (error) {
                    logger.error(`重置孤儿工单失败: ${ticket.id}`, error);
                }
            }
        }
    }

    /**
     * 初始化执行上下文
     */
    initializeExecutionContext(state, ticket) {
        state.executionContext = {
            ...state.executionContext,
            targetUrl: this.extractTargetUrl(ticket.content),
            ticketInfo: {
                id: ticket.id,
                title: ticket.title,
                priority: ticket.priority,
                created_at: ticket.created_at
            },
            browserState: {
                currentUrl: null,
                pageTitle: null,
                isLoggedIn: false,
                sessionCookies: []
            },
            sessionData: {},
            startTime: new Date().toISOString()
        };
    }

    /**
     * 检查队列约束
     */
    async checkQueueConstraints() {
        try {
            // 获取当前队列状态
            const queueStatus = this.messageQueue ? 
                await this.messageQueue.getQueueStatus() : 
                { queued: 0, processing: 1, total: 1 };
            
            return {
                currentQueueSize: queueStatus.total,
                maxConcurrent: 5, // 从配置获取
                canProcess: queueStatus.processing < 5,
                queuePosition: queueStatus.queued + 1
            };
        } catch (error) {
            logger.error('检查队列约束失败:', error);
            return { canProcess: true, queuePosition: 1 };
        }
    }

    /**
     * 清理浏览器资源
     */
    async cleanupBrowserResources(state) {
        try {
            if (this.mcpClient && state.executionContext.browserState) {
                // 关闭浏览器标签页或会话
                // 这里调用MCP客户端的清理方法
                logger.info('清理浏览器资源...');
            }
        } catch (error) {
            logger.error('清理浏览器资源失败:', error);
        }
    }

    /**
     * 清理临时文件
     */
    async cleanupTemporaryFiles(state) {
        try {
            // 清理截图文件
            if (state.executionContext.screenshots) {
                logger.info(`清理 ${state.executionContext.screenshots.length} 个临时截图`);
            }
            
            // 清理其他临时数据
            state.executionContext.clearTemporaryData();
            
        } catch (error) {
            logger.error('清理临时文件失败:', error);
        }
    }

    /**
     * 计算最终统计
     */
    calculateFinalStats(state) {
        const endTime = new Date().toISOString();
        const duration = new Date(endTime) - new Date(state.startTime);
        
        return {
            ...state.stats,
            totalDuration: duration,
            totalCycles: state.cycleCount,
            successRate: state.stats.totalExecutions > 0 ? 
                state.stats.successfulExecutions / state.stats.totalExecutions : 0,
            averageCycleTime: state.cycleCount > 0 ? duration / state.cycleCount : 0,
            endTime: endTime
        };
    }

    /**
     * 生成执行摘要
     */
    generateExecutionSummary(state) {
        const stats = state.checkpointData.finalStats || state.stats;
        
        return `执行完成: ${state.cycleCount}个周期, ` +
               `${stats.successfulExecutions}/${stats.totalExecutions}个操作成功, ` +
               `耗时${Math.round(stats.totalDuration / 1000)}秒`;
    }

    /**
     * 从工单内容提取目标URL
     */
    extractTargetUrl(content) {
        const urlMatch = content.match(/(https?:\/\/[^\s]+)/);
        return urlMatch ? urlMatch[1] : 'https://uat-merchant.aomiapp.com/#/bdlogin';
    }
}

module.exports = CoreControlNodes;