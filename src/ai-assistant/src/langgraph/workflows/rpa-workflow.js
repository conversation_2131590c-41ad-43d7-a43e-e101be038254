/**
 * LangGraph RPA工作流定义
 * 定义完整的工单处理工作流图
 */

const { StateGraph, END, Annotation } = require('@langchain/langgraph');
const { WorkflowState, NodeResult } = require('../state/workflow-state');
const WorkflowStateManager = require('../state/workflow-state-manager');
const logger = require('../../utils/logger');

class RPAWorkflow {
    constructor(dependencies = {}) {
        this.stateManager = new WorkflowStateManager();
        this.dependencies = {
            mcpClient: dependencies.mcpClient,
            llmClient: dependencies.llmClient,
            ticketManager: dependencies.ticketManager,
            ...dependencies
        };
        
        // 定义LangGraph状态注解
        this.StateAnnotation = Annotation.Root({
            ticketId: Annotation,
            workflowId: Annotation,
            currentNode: Annotation,
            currentPhase: Annotation,
            cycleCount: Annotation,
            maxCycles: Annotation,
            pdcaPhase: Annotation,
            shouldSuspend: Annotation,
            executionContext: Annotation,
            checkpointData: Annotation,
            errorHistory: Annotation,
            stats: Annotation,
            ticket: Annotation,
            taskGoal: Annotation,
            finalResult: Annotation
        });
        
        // 节点名称常量
        this.NODES = {
            START: 'start_node',
            RECOVERY_CHECK: 'recovery_check_node',
            QUEUE_MANAGEMENT: 'queue_management_node',
            AGENT_EXECUTION: 'agent_execution_node',
            HUMAN_INTERVENTION: 'human_intervention_node',
            TASK_COMPLETION: 'task_completion_node',
            RETRY_DECISION: 'retry_decision_node',
            FINAL_SUMMARY: 'final_summary_node',
            COMPLETION: 'completion_node',
            SUSPEND: 'suspend_node',
            ERROR_RECOVERY: 'error_recovery_node',
            CLEANUP: 'cleanup_node'
        };
        
        this.workflow = null;
        this.isInitialized = false;
    }

    /**
     * 初始化工作流
     */
    async initialize() {
        try {
            logger.info('🚀 初始化LangGraph RPA工作流...');
            
            // 创建状态图 - 使用真实的LangGraph StateGraph with Annotation
            this.workflow = new StateGraph(this.StateAnnotation);

            // 添加所有节点
            this.addNodes();
            
            // 定义节点间的连接
            this.addEdges();
            
            // 设置入口点
            this.workflow.setEntryPoint(this.NODES.START);
            
            // 编译工作流
            this.compiledWorkflow = this.workflow.compile();
            
            this.isInitialized = true;
            logger.info('✅ LangGraph RPA工作流初始化完成');
            
        } catch (error) {
            logger.error('❌ LangGraph工作流初始化失败:', error);
            throw error;
        }
    }

    /**
     * 添加所有节点
     */
    addNodes() {
        // 🔧 AI-First节点包装器：确保每个节点都接收到经过适配器处理的状态对象
        const wrapNodeWithAIFirstAdapter = (nodeMethod) => {
            return async (state) => {
                // 对每个节点调用都应用AI-First状态适配器
                const enhancedState = this.enhanceStateWithAIFirstMethods(state);
                return await nodeMethod.call(this, enhancedState);
            };
        };

        // 启动节点
        this.workflow.addNode(this.NODES.START, wrapNodeWithAIFirstAdapter(this.startNode));

        // 恢复检查节点
        this.workflow.addNode(this.NODES.RECOVERY_CHECK, wrapNodeWithAIFirstAdapter(this.recoveryCheckNode));

        // 核心节点
        this.workflow.addNode(this.NODES.QUEUE_MANAGEMENT, wrapNodeWithAIFirstAdapter(this.queueManagementNode));

        // 多Agent核心节点
        this.workflow.addNode(this.NODES.AGENT_EXECUTION, wrapNodeWithAIFirstAdapter(this.agentExecutionNode));
        this.workflow.addNode(this.NODES.HUMAN_INTERVENTION, wrapNodeWithAIFirstAdapter(this.humanInterventionNode));
        this.workflow.addNode(this.NODES.TASK_COMPLETION, wrapNodeWithAIFirstAdapter(this.taskCompletionNode));
        this.workflow.addNode(this.NODES.RETRY_DECISION, wrapNodeWithAIFirstAdapter(this.retryDecisionNode));
        this.workflow.addNode(this.NODES.FINAL_SUMMARY, wrapNodeWithAIFirstAdapter(this.finalSummaryNode));

        // 控制节点
        this.workflow.addNode(this.NODES.COMPLETION, wrapNodeWithAIFirstAdapter(this.completionNode));
        this.workflow.addNode(this.NODES.SUSPEND, wrapNodeWithAIFirstAdapter(this.suspendNode));
        this.workflow.addNode(this.NODES.ERROR_RECOVERY, wrapNodeWithAIFirstAdapter(this.errorRecoveryNode));
        this.workflow.addNode(this.NODES.CLEANUP, wrapNodeWithAIFirstAdapter(this.cleanupNode));
    }

    /**
     * 定义节点间的连接
     */
    addEdges() {
        // 主要流程路径
        this.workflow.addEdge(this.NODES.START, this.NODES.RECOVERY_CHECK);
        this.workflow.addEdge(this.NODES.RECOVERY_CHECK, this.NODES.QUEUE_MANAGEMENT);
        this.workflow.addEdge(this.NODES.QUEUE_MANAGEMENT, this.NODES.AGENT_EXECUTION);
        
        // 多Agent流程
        this.workflow.addConditionalEdges(
            this.NODES.AGENT_EXECUTION,
            this.routeAgentExecution.bind(this),
            {
                'human_intervention': this.NODES.HUMAN_INTERVENTION,
                'task_completion': this.NODES.TASK_COMPLETION,
                'retry': this.NODES.RETRY_DECISION,
                'error': this.NODES.ERROR_RECOVERY
            }
        );
        
        // TASK_COMPLETION 使用条件路由，不用直接边
        this.workflow.addConditionalEdges(
            this.NODES.TASK_COMPLETION,
            this.routeTaskCompletion.bind(this),
            {
                'completed': this.NODES.FINAL_SUMMARY,
                'continue': this.NODES.AGENT_EXECUTION
            }
        );
        this.workflow.addEdge(this.NODES.RETRY_DECISION, this.NODES.AGENT_EXECUTION);
        this.workflow.addEdge(this.NODES.HUMAN_INTERVENTION, this.NODES.SUSPEND);
        this.workflow.addEdge(this.NODES.FINAL_SUMMARY, this.NODES.COMPLETION);
        
        // 特殊情况处理
        this.workflow.addEdge(this.NODES.ERROR_RECOVERY, this.NODES.AGENT_EXECUTION);
        this.workflow.addEdge(this.NODES.SUSPEND, this.NODES.CLEANUP);
        this.workflow.addEdge(this.NODES.COMPLETION, this.NODES.CLEANUP);
        
        // 清理节点连接到END
        this.workflow.addEdge(this.NODES.CLEANUP, END);
        
        // 挂起检查（每个节点都检查）
        this.addSuspendChecks();
    }

    /**
     * 添加挂起检查
     */
    addSuspendChecks() {
        const nodesThatCheckSuspend = [
            this.NODES.AGENT_EXECUTION,
            this.NODES.TASK_COMPLETION,
            this.NODES.RETRY_DECISION
        ];

        nodesThatCheckSuspend.forEach(nodeName => {
            // 在每个节点执行前检查挂起状态
            // 这个检查会在节点内部实现
        });
    }

    /**
     * 启动节点 - 工作流入口点
     */
    async startNode(state) {
        try {
            logger.info(`🎯 启动工作流处理工单: ${state.ticketId}`);
            
            // 创建工作流状态记录
            if (!state.workflowId) {
                state.workflowId = await this.stateManager.createWorkflowState(
                    state.ticketId,
                    state.executionContext
                );
            }
            
            state.updateCurrentNode(this.NODES.START);
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                this.NODES.START
            );
            
            return new NodeResult(true, null, this.NODES.RECOVERY_CHECK);
            
        } catch (error) {
            logger.error('启动节点失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 恢复检查节点 - AI助手重启时的状态恢复
     */
    async recoveryCheckNode(state) {
        try {
            logger.info('🔄 执行恢复检查...');
            
            state.updateCurrentNode(this.NODES.RECOVERY_CHECK);
            
            // 检查是否有需要恢复的状态
            const existingState = await this.stateManager.getWorkflowStateByTicketId(state.ticketId);
            
            if (existingState && existingState.status === 'suspended') {
                logger.info(`发现挂起的工作流，恢复执行: ${existingState.workflow_id}`);
                
                // 恢复工作流状态
                await this.stateManager.resumeWorkflow(existingState.workflow_id);
                
                // 合并状态数据
                Object.assign(state, WorkflowState.fromSerializable(existingState.checkpoint_data));
                state.workflowId = existingState.workflow_id;
            }
            
            return new NodeResult(true, null, this.NODES.QUEUE_MANAGEMENT);
            
        } catch (error) {
            logger.error('恢复检查失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 队列管理节点 - 处理工单队列逻辑
     */
    async queueManagementNode(state) {
        try {
            logger.info('📋 执行队列管理...');
            
            state.updateCurrentNode(this.NODES.QUEUE_MANAGEMENT);
            state.currentPhase = 'queue';
            
            // 检查工单状态
            const ticket = await this.dependencies.ticketManager.getTicket(state.ticketId);
            if (!ticket) {
                throw new Error(`工单不存在: ${state.ticketId}`);
            }
            
            state.ticket = ticket;
            state.originalContent = ticket.content;
            state.taskGoal = `${ticket.title}: ${ticket.content}`;
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                this.NODES.QUEUE_MANAGEMENT
            );
            
            return new NodeResult(true, null, this.NODES.AGENT_EXECUTION);
            
        } catch (error) {
            logger.error('队列管理失败:', error);
            state.addError(error, this.NODES.QUEUE_MANAGEMENT);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * Agent执行节点 - 多Agent协同执行
     */
    async agentExecutionNode(state) {
        try {
            logger.info('🤖 多Agent协同执行阶段');
            
            state.updateCurrentNode(this.NODES.AGENT_EXECUTION);
            state.currentPhase = 'agent_executing';
            
            // 检查挂起状态
            const suspendCheck = await this.checkSuspendStatus(state);
            if (suspendCheck.shouldSuspend) {
                return new NodeResult(true, suspendCheck, this.NODES.SUSPEND);
            }
            
            // 这里会调用多Agent协同逻辑
            const agentResult = await this.executeAgentCoordination(state);
            
            state.checkpointData.lastAgentResult = agentResult;
            
            return new NodeResult(true, agentResult, this.NODES.TASK_COMPLETION);
            
        } catch (error) {
            logger.error('Agent执行节点失败:', error);
            state.addError(error, this.NODES.AGENT_EXECUTION);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 人工干预节点
     */
    async humanInterventionNode(state) {
        try {
            logger.info('🤝 人工干预节点');
            
            state.updateCurrentNode(this.NODES.HUMAN_INTERVENTION);
            
            // 请求人工干预
            await this.requestUserIntervention(state);
            
            return new NodeResult(true, null, this.NODES.SUSPEND);
            
        } catch (error) {
            logger.error('人工干预节点失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 任务完成检查节点
     */
    async taskCompletionNode(state) {
        try {
            logger.info('✅ 任务完成检查');
            
            state.updateCurrentNode(this.NODES.TASK_COMPLETION);
            
            // 检查任务是否真正完成
            const completionCheck = await this.validateTaskCompletion(state);
            
            // 保存检查结果到state，供路由使用
            state.checkpointData.lastTaskCompletion = completionCheck;
            
            // 不再直接返回节点，而是返回通用结果，让路由函数决定下一步
            return new NodeResult(true, completionCheck, null);
            
        } catch (error) {
            logger.error('任务完成检查失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 重试决策节点
     */
    async retryDecisionNode(state) {
        try {
            logger.info('🔄 重试决策节点');
            
            state.updateCurrentNode(this.NODES.RETRY_DECISION);
            
            // 分析是否应该重试
            const retryDecision = await this.makeRetryDecision(state);
            
            if (retryDecision.shouldRetry) {
                state.incrementCycle();
                return new NodeResult(true, retryDecision, this.NODES.AGENT_EXECUTION);
            } else {
                return new NodeResult(true, retryDecision, this.NODES.HUMAN_INTERVENTION);
            }
            
        } catch (error) {
            logger.error('重试决策失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 规划节点 - PDCA的Plan阶段
     */
    async planningNode(state) {
        try {
            logger.info(`🧠 PDCA规划阶段 - 循环 ${state.cycleCount + 1}`);
            
            // 检查挂起状态
            const suspendCheck = await this.checkSuspendStatus(state);
            if (suspendCheck.shouldSuspend) {
                return new NodeResult(true, suspendCheck, this.NODES.SUSPEND);
            }
            
            state.updateCurrentNode(this.NODES.AGENT_EXECUTION);
            state.currentPhase = 'planning';
            state.updatePDCAPhase('plan');
            state.incrementCycle();
            
            // 检查循环次数限制
            const terminationCheck = state.shouldTerminate();
            if (terminationCheck.terminate) {
                logger.warn(`规划阶段终止: ${terminationCheck.reason}`);
                return this.handleTermination(state, terminationCheck.reason);
            }
            
            // 调用规划逻辑（这里会调用原有的PlannerAgent逻辑）
            const planResult = await this.executePlanningLogic(state);
            
            state.checkpointData.lastPlan = planResult;
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                this.NODES.AGENT_EXECUTION
            );
            
            return new NodeResult(true, planResult, this.NODES.EXECUTION);
            
        } catch (error) {
            logger.error('规划节点失败:', error);
            state.addError(error, this.NODES.AGENT_EXECUTION);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 执行节点 - PDCA的Do阶段
     */
    async executionNode(state) {
        try {
            logger.info('🔧 PDCA执行阶段');
            
            // 检查挂起状态
            const suspendCheck = await this.checkSuspendStatus(state);
            if (suspendCheck.shouldSuspend) {
                return new NodeResult(true, suspendCheck, this.NODES.SUSPEND);
            }
            
            state.updateCurrentNode(this.NODES.EXECUTION);
            state.currentPhase = 'executing';
            state.updatePDCAPhase('do');
            
            // 执行计划（调用ExecutorAgent逻辑）
            const executionResult = await this.executeActionPlan(state);
            
            state.checkpointData.lastExecution = executionResult;
            state.updateStats(executionResult.success, executionResult.duration);
            
            return new NodeResult(true, executionResult, this.NODES.VALIDATION);
            
        } catch (error) {
            logger.error('执行节点失败:', error);
            state.addError(error, this.NODES.EXECUTION);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 验证节点 - PDCA的Check阶段
     */
    async validationNode(state) {
        try {
            logger.info('🔍 PDCA验证阶段');
            
            state.updateCurrentNode(this.NODES.VALIDATION);
            state.currentPhase = 'validating';
            state.updatePDCAPhase('check');
            
            // 验证执行结果（调用CheckerAgent逻辑）
            const validationResult = await this.validateExecution(state);
            
            state.checkpointData.lastValidation = validationResult;
            
            return new NodeResult(true, validationResult, this.NODES.DECISION);
            
        } catch (error) {
            logger.error('验证节点失败:', error);
            state.addError(error, this.NODES.VALIDATION);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 决策节点 - 决定下一步行动
     */
    async decisionNode(state) {
        try {
            logger.info('⚡ PDCA决策阶段');
            
            state.updateCurrentNode(this.NODES.DECISION);
            
            const lastValidation = state.checkpointData.lastValidation;
            
            // 根据验证结果决定下一步
            if (lastValidation.status === 'TASK_COMPLETED') {
                return new NodeResult(true, { decision: 'complete' });
            }
            
            if (lastValidation.status === 'USER_INTERVENTION_REQUIRED') {
                return new NodeResult(true, { 
                    decision: 'intervention',
                    reason: lastValidation.reason 
                });
            }
            
            if (lastValidation.status === 'CRITICAL_ERROR') {
                return new NodeResult(true, { 
                    decision: 'error',
                    error: lastValidation.error 
                });
            }
            
            // 默认继续循环
            return new NodeResult(true, { decision: 'continue' });
            
        } catch (error) {
            logger.error('决策节点失败:', error);
            return new NodeResult(true, { decision: 'error', error: error });
        }
    }

    /**
     * 行动节点 - PDCA的Act阶段
     */
    async actionNode(state) {
        try {
            logger.info('⚡ PDCA行动阶段');
            
            state.updateCurrentNode(this.NODES.ACTION);
            state.updatePDCAPhase('act');
            
            // 更新状态，准备下一轮循环
            state.compressHistory();
            
            // 保存检查点
            await this.stateManager.saveCheckpoint(
                state.workflowId,
                state.createCheckpoint(),
                this.NODES.ACTION
            );
            
            return new NodeResult(true, null, this.NODES.AGENT_EXECUTION);
            
        } catch (error) {
            logger.error('行动节点失败:', error);
            state.addError(error, this.NODES.ACTION);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 最终总结节点
     */
    async finalSummaryNode(state) {
        try {
            logger.info('📊 生成最终执行总结');
            
            state.updateCurrentNode(this.NODES.FINAL_SUMMARY);
            
            // 收集所有执行证据
            const completionEvidence = state.checkpointData.completionEvidence || [];
            const stats = state.stats || {};
            
            const finalReport = {
                success: true,
                completedAt: new Date().toISOString(),
                totalTasks: state.completedTasks.length,
                totalCycles: state.cycleCount,
                stats: stats,
                evidence: completionEvidence,
                summary: `成功完成所有任务，共执行${state.completedTasks.length}个任务，${state.cycleCount}次重试`
            };
            
            state.setFinalResult(finalReport);
            
            return new NodeResult(true, finalReport, this.NODES.COMPLETION);
            
        } catch (error) {
            logger.error('最终总结节点失败:', error);
            return new NodeResult(false, { error: error.message }, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 完成节点 - 任务完成处理
     */
    async completionNode(state) {
        try {
            logger.info('🎉 任务完成处理');
            
            state.updateCurrentNode(this.NODES.COMPLETION);
            state.currentPhase = 'completed';
            
            // 生成最终报告
            const finalReport = this.generateFinalReport(state);
            state.setFinalResult({ success: true }, finalReport);
            
            // 完成工作流
            await this.stateManager.completeWorkflow(state.workflowId, state.finalResult);
            
            return new NodeResult(true, state.finalResult, this.NODES.CLEANUP);
            
        } catch (error) {
            logger.error('完成节点失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 用户干预节点 - 处理需要人工干预的情况
     */
    async userInterventionNode(state) {
        try {
            logger.info('🤝 用户干预处理');
            
            state.updateCurrentNode(this.NODES.USER_INTERVENTION);
            
            // 更新工单状态，请求用户干预
            await this.requestUserIntervention(state);
            
            return new NodeResult(true, null, this.NODES.SUSPEND);
            
        } catch (error) {
            logger.error('用户干预节点失败:', error);
            return new NodeResult(false, null, this.NODES.ERROR_RECOVERY);
        }
    }

    /**
     * 挂起节点 - 处理工作流挂起
     */
    async suspendNode(state) {
        try {
            logger.info('⏸️ 工作流挂起处理');
            
            state.updateCurrentNode(this.NODES.SUSPEND);
            
            // 挂起工作流
            await this.stateManager.suspendWorkflow(state.workflowId, '用户操作或需要人工干预');
            
            return new NodeResult(true, null, this.NODES.CLEANUP);
            
        } catch (error) {
            logger.error('挂起节点失败:', error);
            return new NodeResult(false, null, this.NODES.CLEANUP);
        }
    }

    /**
     * 错误恢复节点 - 处理错误和重试
     */
    async errorRecoveryNode(state) {
        try {
            logger.info('🔧 错误恢复处理');
            
            state.updateCurrentNode(this.NODES.ERROR_RECOVERY);
            
            // 错误恢复逻辑
            const recovery = await this.handleErrorRecovery(state);
            
            if (recovery.shouldRetry) {
                logger.info('重试执行...');
                return new NodeResult(true, recovery, this.NODES.AGENT_EXECUTION);
            } else {
                logger.error('无法恢复，标记为失败');
                await this.stateManager.failWorkflow(state.workflowId, recovery.error);
                return new NodeResult(false, recovery, this.NODES.CLEANUP);
            }
            
        } catch (error) {
            logger.error('错误恢复失败:', error);
            return new NodeResult(false, null, this.NODES.CLEANUP);
        }
    }

    /**
     * 清理节点 - 清理资源和完成工作流
     */
    async cleanupNode(state) {
        try {
            logger.info('🧹 资源清理');
            
            state.updateCurrentNode(this.NODES.CLEANUP);
            
            // 清理浏览器资源
            await this.cleanupResources(state);
            
            // 工作流结束
            logger.info(`✅ 工作流完成: ${state.workflowId}`);
            
            return new NodeResult(true, { completed: true });
            
        } catch (error) {
            logger.error('清理节点失败:', error);
            return new NodeResult(false, null);
        }
    }

    /**
     * 决策路由函数
     */
    /**
     * 路由Agent执行结果
     */
    routeAgentExecution(state) {
        const result = state.checkpointData.lastExecutionResult || {};
        
        if (result.needsHumanHelp) {
            return 'human_intervention';
        } else if (result.success && state.queuedTasks.length === 0) {
            return 'task_completion';
        } else if (!result.success && state.cycleCount < 3) {
            return 'retry';
        } else {
            return 'error';
        }
    }

    /**
     * 路由决策节点
     */
    routeDecision(state) {
        const result = state.checkpointData.lastValidation || {};
        const decision = result.decision || 'continue';
        
        logger.info(`决策路由: ${decision}`);
        return decision;
    }

    /**
     * 检查挂起状态
     */
    async checkSuspendStatus(state) {
        try {
            // 检查工单状态是否为已挂起
            const ticket = await this.dependencies.ticketManager.getTicket(state.ticketId);
            
            if (ticket && ticket.status === '已挂起') {
                logger.info(`工单已挂起: ${state.ticketId}`);
                return { shouldSuspend: true, reason: '工单已挂起' };
            }
            
            // 检查内部挂起标志
            if (state.shouldSuspend) {
                return { shouldSuspend: true, reason: '内部请求挂起' };
            }
            
            return { shouldSuspend: false };
            
        } catch (error) {
            logger.error('检查挂起状态失败:', error);
            return { shouldSuspend: false };
        }
    }

    /**
     * 处理终止情况
     */
    handleTermination(state, reason) {
        switch (reason) {
            case 'MAX_CYCLES_REACHED':
                return new NodeResult(true, { decision: 'error', error: '达到最大循环次数' });
            case 'MAX_CONSECUTIVE_FAILURES':
                return new NodeResult(true, { decision: 'error', error: '连续失败次数过多' });
            case 'SUSPENDED':
                return new NodeResult(true, { decision: 'suspend' });
            case 'USER_INTERVENTION_REQUIRED':
                return new NodeResult(true, { decision: 'intervention' });
            default:
                return new NodeResult(true, { decision: 'error', error: '未知终止原因' });
        }
    }

    // 占位符方法 - 这些将在后续阶段实现
    async executePlanningLogic(state) { return { intent: 'navigate', confidence: 0.8 }; }
    async executeActionPlan(state) { return { success: true, duration: 1000 }; }
    async validateExecution(state) { return { status: 'SUCCESS' }; }
    async requestUserIntervention(state) { return true; }
    async handleErrorRecovery(state) { return { shouldRetry: false }; }
    async cleanupResources(state) { return true; }
    generateFinalReport(state) { return { summary: '任务完成' }; }
    
    // 新增的占位符方法
    async executeAgentCoordination(state) { 
        return { success: true, needsHumanHelp: false, iterations: 1 }; 
    }
    async validateTaskCompletion(state) { 
        return { completed: true, evidence: ['模拟完成证据'] }; 
    }
    async makeRetryDecision(state) { 
        return { shouldRetry: false, reason: '任务已完成' }; 
    }
    
    /**
     * 路由任务完成检查结果
     */
    routeTaskCompletion(state) {
        const completionCheck = state.checkpointData.lastTaskCompletion || {};
        
        if (completionCheck.completed) {
            logger.info('任务已完成，进入最终总结');
            return 'completed';
        } else {
            logger.info('任务未完成，继续Agent执行');
            return 'continue';
        }
    }

    /**
     * 执行工作流
     */
    async execute(initialState) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            logger.info(`🚀 开始执行LangGraph工作流: ${initialState.ticketId}`);

            // 🔧 AI-First状态适配器：解决LangGraph状态对象方法缺失问题
            // 原因：LangGraph将WorkflowState实例转换为普通对象，导致方法丢失
            // 解决：动态注入必要的方法，保持AI-First原则
            const enhancedState = this.enhanceStateWithAIFirstMethods(initialState);

            const result = await this.compiledWorkflow.invoke(enhancedState);

            logger.info(`✅ LangGraph工作流执行完成: ${initialState.ticketId}`);
            return result;

        } catch (error) {
            logger.error('LangGraph工作流执行失败:', error);
            throw error;
        }
    }

    /**
     * AI-First状态适配器
     *
     * 问题背景：
     * - WorkflowState类包含业务逻辑方法（addError, updateCurrentNode等）
     * - LangGraph内部将WorkflowState实例转换为基于StateAnnotation的普通对象
     * - 转换过程中方法丢失，导致节点函数调用失败
     *
     * 解决方案：
     * - 动态注入必要的方法到LangGraph状态对象
     * - 保持原有业务逻辑不变
     * - 符合AI-First原则：方法实现可以包含AI驱动的逻辑
     *
     * 设计原则：
     * - 最小侵入：不改变LangGraph核心架构
     * - 向后兼容：现有代码无需修改
     * - 可扩展：支持未来添加更多AI驱动的方法
     *
     * @param {Object} langGraphState - LangGraph状态对象
     * @returns {Object} 增强后的状态对象
     */
    enhanceStateWithAIFirstMethods(langGraphState) {
        logger.debug('🔧 应用AI-First状态适配器');

        // 确保必要的数组字段存在
        langGraphState.errorHistory = langGraphState.errorHistory || [];
        langGraphState.nodeHistory = langGraphState.nodeHistory || [];

        /**
         * 添加错误记录方法
         * 包含智能错误分析和分类逻辑
         */
        langGraphState.addError = (error, node = null) => {
            const errorRecord = {
                timestamp: new Date().toISOString(),
                error: error.message || error,
                stack: error.stack,
                node: node || langGraphState.currentNode,
                cycle: langGraphState.cycleCount || 0,
                // AI-First扩展：可以在这里添加AI驱动的错误分析
                errorType: this.classifyErrorWithAI ? this.classifyErrorWithAI(error) : 'unknown'
            };

            langGraphState.errorHistory.push(errorRecord);

            // 更新连续失败计数
            langGraphState.consecutiveFailures = (langGraphState.consecutiveFailures || 0) + 1;

            logger.debug(`📝 记录错误: ${error.message} 在节点 ${errorRecord.node}`);
        };

        /**
         * 更新当前节点方法
         * 包含智能节点转换验证逻辑
         */
        langGraphState.updateCurrentNode = (nodeName) => {
            const previousNode = langGraphState.currentNode;
            langGraphState.currentNode = nodeName;

            // 避免重复记录相同节点
            if (langGraphState.nodeHistory[langGraphState.nodeHistory.length - 1] !== nodeName) {
                langGraphState.nodeHistory.push(nodeName);
            }

            logger.debug(`🔄 节点转换: ${previousNode} → ${nodeName}`);
        };

        /**
         * 更新PDCA阶段方法
         */
        langGraphState.updatePDCAPhase = (phase) => {
            const validPhases = ['plan', 'do', 'check', 'act'];
            if (validPhases.includes(phase)) {
                langGraphState.pdcaPhase = phase;
                logger.debug(`📊 PDCA阶段: ${phase}`);
            }
        };

        /**
         * 增加循环计数方法
         */
        langGraphState.incrementCycle = () => {
            langGraphState.cycleCount = (langGraphState.cycleCount || 0) + 1;
            logger.debug(`🔄 PDCA循环: ${langGraphState.cycleCount}`);
        };

        /**
         * 重置连续失败计数方法
         */
        langGraphState.resetConsecutiveFailures = () => {
            langGraphState.consecutiveFailures = 0;
            logger.debug('✅ 重置连续失败计数');
        };

        /**
         * 创建检查点方法
         */
        langGraphState.createCheckpoint = () => {
            return {
                timestamp: new Date().toISOString(),
                currentNode: langGraphState.currentNode,
                currentPhase: langGraphState.currentPhase,
                cycleCount: langGraphState.cycleCount,
                pdcaPhase: langGraphState.pdcaPhase,
                errorHistory: [...(langGraphState.errorHistory || [])],
                nodeHistory: [...(langGraphState.nodeHistory || [])],
                executionContext: langGraphState.executionContext || {},
                checkpointData: langGraphState.checkpointData || {}
            };
        };

        /**
         * 检查是否应该终止方法
         */
        langGraphState.shouldTerminate = () => {
            const maxCycles = langGraphState.maxCycles || 50;
            const maxConsecutiveFailures = langGraphState.maxConsecutiveFailures || 3;

            // 检查最大循环次数
            if ((langGraphState.cycleCount || 0) >= maxCycles) {
                return { terminate: true, reason: 'MAX_CYCLES_REACHED' };
            }

            // 检查连续失败次数
            if ((langGraphState.consecutiveFailures || 0) >= maxConsecutiveFailures) {
                return { terminate: true, reason: 'MAX_CONSECUTIVE_FAILURES' };
            }

            // 检查挂起标志
            if (langGraphState.shouldSuspend) {
                return { terminate: true, reason: 'SUSPENDED' };
            }

            // 检查用户干预
            if (langGraphState.needsUserIntervention) {
                return { terminate: true, reason: 'USER_INTERVENTION_REQUIRED' };
            }

            return { terminate: false };
        };

        logger.debug('✅ AI-First状态适配器应用完成');
        return langGraphState;
    }
}

module.exports = RPAWorkflow;