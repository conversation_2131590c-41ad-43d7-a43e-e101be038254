/**
 * LangGraph任务处理器
 * 整合所有LangGraph组件，替换现有的任务处理器
 */

const { WorkflowState } = require('./state/workflow-state');
const WorkflowStateManager = require('./state/workflow-state-manager');
const RPAWorkflow = require('./workflows/rpa-workflow');
const CoreControlNodes = require('./nodes/core-control-nodes');
const PDCAExecutionNodes = require('./nodes/pdca-execution-nodes');

// 导入现有组件
const SimpleLLMClient = require('../pdca/simple-llm-client');
const TicketManager = require('../../../shared/core/ticket-manager');
const MessageQueue = require('../../../shared/core/message-queue');
const logger = require('../utils/logger');
const { EnhancedSimpleMCPClient } = require('../playwright-mcp/enhanced-simple-mcp-client');
const MultiAgentExecutionNodes = require('./nodes/multi-agent-execution-nodes');

class LangGraphTaskProcessor {
    constructor() {
        this.workorderApiUrl = process.env.WORKORDER_API_URL || 'http://localhost:3001';
        
        // 初始化依赖组件
        this.mcpClient = new EnhancedSimpleMCPClient();
        this.llmClient = new SimpleLLMClient();
        this.ticketManager = new TicketManager();
        this.messageQueue = new MessageQueue();
        this.stateManager = new WorkflowStateManager();
        
        // 创建依赖注入对象
        this.dependencies = {
            mcpClient: this.mcpClient,
            llmClient: this.llmClient,
            ticketManager: this.ticketManager,
            messageQueue: this.messageQueue,
            stateManager: this.stateManager
        };
        
        // 初始化节点处理器
        this.coreNodes = new CoreControlNodes(this.dependencies);
        this.multiAgentNodes = new MultiAgentExecutionNodes(this.dependencies);
        
        // 初始化工作流
        this.workflow = new RPAWorkflow({
            ...this.dependencies,
            coreNodes: this.coreNodes,
            multiAgentNodes: this.multiAgentNodes
        });
        
        // 处理状态
        this.isProcessing = false;
        this.currentTicket = null;
        this.processingQueue = [];
        this.isInitialized = false;
        
        // 统计信息
        this.stats = {
            processedCount: 0,
            successCount: 0,
            failureCount: 0,
            totalExecutionTime: 0
        };
    }

    /**
     * 初始化处理器
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        
        try {
            logger.info('🚀 初始化LangGraph任务处理器...');
            
            // 1. 初始化MCP客户端
            await this.mcpClient.initialize();
            logger.info('✅ MCP客户端初始化完成');
            
            // 2. 初始化工作流
            await this.workflow.initialize();
            logger.info('✅ LangGraph工作流初始化完成');
            
            // 3. 启动消息队列管理
            await this.messageQueue.start();
            logger.info('✅ 消息队列管理启动完成');
            
            // 4. 执行恢复检查
            await this.performRecoveryCheck();
            logger.info('✅ 恢复检查完成');
            
            this.isInitialized = true;
            logger.info('🎉 LangGraph任务处理器初始化完成');
            
        } catch (error) {
            logger.error('❌ LangGraph任务处理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 处理工单 - 主要入口点
     */
    async processTicket(ticket) {
        const startTime = Date.now();
        
        try {
            logger.info(`🎯 LangGraph开始处理工单: ${ticket.id} - ${ticket.title}`);
            
            // 确保处理器已初始化
            await this.initialize();
            
            // 检查是否已在处理中
            if (this.isProcessing) {
                logger.info('处理器忙碌中，工单加入队列:', ticket.id);
                this.processingQueue.push(ticket);
                return { success: true, queued: true, position: this.processingQueue.length };
            }
            
            this.isProcessing = true;
            this.currentTicket = ticket;
            
            // 创建工作流状态并生成工作流ID
            const workflowState = new WorkflowState(ticket.id);

            // 立即创建工作流状态记录，获取工作流ID
            if (!workflowState.workflowId) {
                workflowState.workflowId = await this.stateManager.createWorkflowState(
                    ticket.id,
                    workflowState.executionContext
                );
                logger.info(`✅ 创建工作流状态: ${workflowState.workflowId} for ticket ${ticket.id}`);
            }
            
            // 更新工单状态为处理中
            await this.updateTicketStatus(ticket.id, '处理中', {
                notes: 'LangGraph RPA系统开始处理工单',
                processor: 'LangGraph',
                started_at: new Date().toISOString()
            });
            
            // 执行LangGraph工作流
            const result = await this.executeWorkflow(workflowState);
            
            // 计算执行时间
            const executionTime = Date.now() - startTime;
            this.stats.totalExecutionTime += executionTime;
            
            // 处理执行结果
            const processResult = await this.handleExecutionResult(ticket, result, executionTime);
            
            // 更新统计信息
            this.updateStatistics(processResult.success);
            
            logger.info(`✅ LangGraph工单处理完成: ${ticket.id}, 耗时: ${(executionTime/1000).toFixed(2)}秒`);
            
            return processResult;
            
        } catch (error) {
            logger.error(`❌ LangGraph工单处理异常: ${ticket.id}`, error);
            
            // 更新工单状态为失败
            await this.updateTicketStatus(ticket.id, '处理失败', {
                notes: `LangGraph处理异常: ${error.message}`,
                error_details: error.stack,
                failed_at: new Date().toISOString()
            });
            
            this.updateStatistics(false);
            
            return {
                success: false,
                ticketId: ticket.id,
                error: error.message,
                executionTime: Date.now() - startTime
            };
            
        } finally {
            this.isProcessing = false;
            this.currentTicket = null;
            
            // 处理队列中的下一个工单
            await this.processNextInQueue();
        }
    }

    /**
     * 执行LangGraph工作流
     */
    async executeWorkflow(workflowState) {
        try {
            logger.info(`🔄 开始执行LangGraph工作流: ${workflowState.ticketId}`);
            
            // 重写工作流的节点方法，使其使用我们的节点处理器
            this.injectNodeHandlers();
            
            // 执行工作流
            const result = await this.workflow.execute(workflowState);
            
            logger.info(`✅ LangGraph工作流执行完成: ${workflowState.ticketId}`);
            return result;
            
        } catch (error) {
            logger.error('LangGraph工作流执行失败:', error);
            throw error;
        }
    }

    /**
     * 注入节点处理器
     */
    injectNodeHandlers() {
        // 将我们的节点处理器方法注入到工作流中
        this.workflow.recoveryCheckNode = this.coreNodes.recoveryCheckNode.bind(this.coreNodes);
        this.workflow.queueManagementNode = this.coreNodes.queueManagementNode.bind(this.coreNodes);
        this.workflow.cleanupNode = this.coreNodes.cleanupNode.bind(this.coreNodes);
        
        // 使用多Agent执行节点
        this.workflow.agentExecutionNode = this.multiAgentNodes.agentExecutionNode.bind(this.multiAgentNodes);
        this.workflow.humanInterventionNode = this.multiAgentNodes.humanInterventionNode.bind(this.multiAgentNodes);
        this.workflow.taskCompletionNode = this.multiAgentNodes.taskCompletionNode.bind(this.multiAgentNodes);
        this.workflow.retryDecisionNode = this.multiAgentNodes.retryDecisionNode.bind(this.multiAgentNodes);
        
        // 特殊场景处理
        this.workflow.userInterventionNode = this.handleUserIntervention.bind(this);
        this.workflow.suspendNode = this.handleSuspend.bind(this);
        this.workflow.errorRecoveryNode = this.handleErrorRecovery.bind(this);
        this.workflow.completionNode = this.handleCompletion.bind(this);
        
        // 挂起检查
        this.workflow.checkSuspendStatus = this.coreNodes.suspendCheckNode.bind(this.coreNodes);
    }

    /**
     * 处理执行结果
     */
    async handleExecutionResult(ticket, result, executionTime) {
        try {
            if (result && result.finalResult) {
                // 执行成功
                const report = this.generateExecutionReport(ticket, result, executionTime);
                
                await this.updateTicketStatus(ticket.id, '已完成', {
                    report: report.summary,
                    execution_details: JSON.stringify(report),
                    completed_at: new Date().toISOString()
                });
                
                return {
                    success: true,
                    ticketId: ticket.id,
                    executionTime: executionTime,
                    report: report
                };
            } else {
                // 执行失败或挂起
                const status = result?.suspended ? '已挂起' : '处理失败';
                const notes = result?.suspended ? 
                    '工作流已挂起，等待用户操作' : 
                    `处理失败: ${result?.error || '未知错误'}`;
                
                await this.updateTicketStatus(ticket.id, status, {
                    notes: notes,
                    execution_context: JSON.stringify(result || {}),
                    updated_at: new Date().toISOString()
                });
                
                return {
                    success: false,
                    ticketId: ticket.id,
                    error: result?.error || '处理失败',
                    suspended: result?.suspended || false,
                    executionTime: executionTime
                };
            }
            
        } catch (error) {
            logger.error('处理执行结果失败:', error);
            return {
                success: false,
                ticketId: ticket.id,
                error: error.message,
                executionTime: executionTime
            };
        }
    }

    /**
     * 生成执行报告
     */
    generateExecutionReport(ticket, result, executionTime) {
        const state = result.finalResult || {};
        const stats = state.stats || {};
        
        return {
            ticketId: ticket.id,
            ticketTitle: ticket.title,
            executionTime: executionTime,
            timestamp: new Date().toISOString(),
            
            // 执行统计
            cycles: state.cycleCount || 0,
            totalOperations: stats.totalExecutions || 0,
            successfulOperations: stats.successfulExecutions || 0,
            failedOperations: stats.failedExecutions || 0,
            
            // 技术信息
            architecture: 'LangGraph',
            processor: 'LangGraph RPA Workflow',
            
            // 摘要
            summary: `✅ LangGraph执行成功！完成${state.cycleCount || 0}个PDCA循环，` +
                    `执行${stats.totalExecutions || 0}个操作，` +
                    `耗时${(executionTime/1000).toFixed(2)}秒`,
            
            // 详细信息
            details: {
                workflowId: state.workflowId,
                finalPhase: state.currentPhase,
                lastNode: state.currentNode,
                nodeHistory: state.nodeHistory || [],
                errorCount: state.errorHistory?.length || 0
            }
        };
    }

    /**
     * 挂起工单处理
     */
    async suspendTicket(ticketId) {
        try {
            logger.info(`🛑 挂起工单处理: ${ticketId}`);
            
            // 检查是否是当前处理的工单
            if (this.currentTicket && this.currentTicket.id === ticketId) {
                logger.info(`⏹️ 挂起当前处理的工单: ${ticketId}`);
                
                // 标记挂起 - 工作流会在下一个检查点检测到
                // 这里不需要额外操作，因为工作流会自动检查工单状态
                
                return { success: true, message: '工单挂起请求已发送' };
            } else {
                logger.info(`ℹ️ 工单 ${ticketId} 未在处理中，直接更新状态`);
                
                // 直接更新工单状态
                await this.updateTicketStatus(ticketId, '已挂起', {
                    notes: '用户手动挂起工单',
                    suspended_at: new Date().toISOString()
                });
                
                return { success: true, message: '工单已挂起' };
            }
            
        } catch (error) {
            logger.error(`❌ 挂起工单失败 ${ticketId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 执行恢复检查
     */
    async performRecoveryCheck() {
        try {
            logger.info('🔄 执行系统恢复检查...');
            
            // 获取所有处理中的工单
            const processingTickets = await this.ticketManager.getTickets({
                status: '处理中',
                pageSize: 100
            });
            
            if (processingTickets.tickets.length === 0) {
                logger.info('没有发现处理中的工单');
                return;
            }
            
            logger.info(`发现 ${processingTickets.tickets.length} 个处理中的工单，开始恢复检查...`);
            
            for (const ticket of processingTickets.tickets) {
                await this.recoverTicket(ticket);
            }
            
        } catch (error) {
            logger.error('恢复检查失败:', error);
        }
    }

    /**
     * 恢复单个工单
     */
    async recoverTicket(ticket) {
        try {
            // 检查是否有对应的工作流状态
            const workflowState = await this.stateManager.getWorkflowStateByTicketId(ticket.id);
            
            if (workflowState) {
                const lastActiveTime = new Date(workflowState.last_active_at);
                const timeSinceActive = Date.now() - lastActiveTime.getTime();
                
                if (timeSinceActive > 5 * 60 * 1000) { // 5分钟未活动
                    logger.warn(`工单 ${ticket.id} 异常中断，重置状态`);
                    
                    // 标记工作流失败
                    await this.stateManager.failWorkflow(
                        workflowState.workflow_id,
                        new Error('系统重启，工作流异常中断')
                    );
                    
                    // 重置工单状态
                    await this.updateTicketStatus(ticket.id, '待开始', {
                        notes: '系统重启时发现异常状态，自动重置'
                    });
                } else {
                    logger.info(`工单 ${ticket.id} 最近还在活动，保持状态`);
                }
            } else {
                logger.warn(`工单 ${ticket.id} 没有对应的工作流状态，重置`);
                
                // 重置工单状态
                await this.updateTicketStatus(ticket.id, '待开始', {
                    notes: '系统重启时未找到工作流状态，自动重置'
                });
            }
            
        } catch (error) {
            logger.error(`恢复工单 ${ticket.id} 失败:`, error);
        }
    }

    /**
     * 处理队列中的下一个工单
     */
    async processNextInQueue() {
        if (this.processingQueue.length > 0) {
            const nextTicket = this.processingQueue.shift();
            logger.info(`📋 处理队列中的下一个工单: ${nextTicket.id}`);
            
            // 异步处理，避免阻塞
            setImmediate(() => {
                this.processTicket(nextTicket);
            });
        }
    }

    /**
     * 更新工单状态
     */
    async updateTicketStatus(ticketId, status, additionalData = {}) {
        try {
            const updateData = {
                status: status,
                ...additionalData
            };
            
            // 使用TicketManager更新状态
            await this.ticketManager.updateTicketStatus(ticketId, status, additionalData);
            
            logger.info(`✅ 工单状态更新: ${ticketId} -> ${status}`);
            
        } catch (error) {
            logger.error(`❌ 工单状态更新失败: ${ticketId}`, error);
            throw error;
        }
    }

    /**
     * 更新统计信息
     */
    updateStatistics(success) {
        this.stats.processedCount++;
        if (success) {
            this.stats.successCount++;
        } else {
            this.stats.failureCount++;
        }
    }

    // ===== 特殊场景处理方法 =====

    /**
     * 处理用户干预
     */
    async handleUserIntervention(state) {
        logger.info('🤝 处理用户干预场景');
        
        const reason = state.interventionReason || '需要用户协助';
        
        await this.updateTicketStatus(state.ticketId, '待补充信息', {
            intervention_reason: reason,
            instructions: state.missingInfo?.join(', ') || '请提供必要信息后继续',
            suspended_for_intervention: new Date().toISOString()
        });
        
        return { success: true, intervention: true, reason: reason };
    }

    /**
     * 处理挂起
     */
    async handleSuspend(state) {
        logger.info('⏸️ 处理工作流挂起');
        
        await this.stateManager.suspendWorkflow(state.workflowId, '用户请求挂起或需要人工干预');
        
        return { success: true, suspended: true };
    }

    /**
     * 处理错误恢复
     */
    async handleErrorRecovery(state) {
        logger.warn('🔧 处理错误恢复');
        
        const errorHistory = state.errorHistory || [];
        const canRetry = state.consecutiveFailures < state.maxConsecutiveFailures;
        
        if (canRetry && errorHistory.length < 10) {
            logger.info('尝试错误恢复，继续执行...');
            return { success: true, retry: true };
        } else {
            logger.error('错误过多，无法恢复');
            await this.stateManager.failWorkflow(state.workflowId, new Error('错误过多，无法恢复'));
            return { success: false, error: '错误过多，无法恢复' };
        }
    }

    /**
     * 处理完成
     */
    async handleCompletion(state) {
        logger.info('🎉 处理任务完成');
        
        const finalReport = {
            success: true,
            completedAt: new Date().toISOString(),
            totalCycles: state.cycleCount,
            stats: state.stats
        };
        
        state.setFinalResult(finalReport);
        
        await this.stateManager.completeWorkflow(state.workflowId, finalReport);
        
        return { success: true, completed: true, finalResult: finalReport };
    }

    // ===== 兼容性方法（保持与现有接口兼容） =====

    getQueueSize() {
        return this.processingQueue.length;
    }

    getProcessedCount() {
        return this.stats.processedCount;
    }

    getSuccessCount() {
        return this.stats.successCount;
    }

    getFailureCount() {
        return this.stats.failureCount;
    }

    getStatus() {
        return {
            isProcessing: this.isProcessing,
            currentTicket: this.currentTicket ? {
                id: this.currentTicket.id,
                title: this.currentTicket.title
            } : null,
            queueSize: this.processingQueue.length,
            architecture: 'LangGraph',
            processor: 'LangGraph RPA Workflow',
            stats: this.stats,
            initialized: this.isInitialized
        };
    }

    async stop() {
        logger.info('⏹️ 停止LangGraph任务处理器...');
        
        this.isProcessing = false;
        this.currentTicket = null;
        this.processingQueue = [];
        
        // 停止消息队列
        if (this.messageQueue) {
            this.messageQueue.stop();
        }
        
        logger.info('✅ LangGraph任务处理器已停止');
    }
}

module.exports = LangGraphTaskProcessor;