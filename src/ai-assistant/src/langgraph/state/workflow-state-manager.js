/**
 * LangGraph工作流状态管理器
 * 负责工作流状态的持久化、恢复和管理
 */

const { v4: uuidv4 } = require('uuid');
const dbConnection = require('../../../../shared/database/ensure-connected');
const logger = require('../../utils/logger');

class WorkflowStateManager {
    constructor() {
        this.db = dbConnection;
        
        // 状态定义
        this.WORKFLOW_STATUSES = {
            ACTIVE: 'active',
            SUSPENDED: 'suspended', 
            COMPLETED: 'completed',
            FAILED: 'failed'
        };
        
        this.PDCA_PHASES = {
            PLAN: 'plan',
            DO: 'do', 
            CHECK: 'check',
            ACT: 'act'
        };
    }

    /**
     * 创建新的工作流状态
     */
    async createWorkflowState(ticketId, initialContext = {}) {
        try {
            const workflowId = `workflow_${ticketId}_${uuidv4()}`;
            
            const workflowState = {
                workflow_id: workflowId,
                ticket_id: ticketId,
                status: this.WORKFLOW_STATUSES.ACTIVE,
                current_node: 'start_node',
                node_history: JSON.stringify(['start_node']),
                cycle_count: 0,
                pdca_phase: this.PDCA_PHASES.PLAN,
                execution_context: JSON.stringify(initialContext),
                checkpoint_data: JSON.stringify({}),
                error_history: JSON.stringify([])
            };

            await this.db.run(
                `INSERT INTO workflow_states 
                 (workflow_id, ticket_id, status, current_node, node_history, 
                  cycle_count, pdca_phase, execution_context, checkpoint_data, error_history)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    workflowState.workflow_id,
                    workflowState.ticket_id,
                    workflowState.status,
                    workflowState.current_node,
                    workflowState.node_history,
                    workflowState.cycle_count,
                    workflowState.pdca_phase,
                    workflowState.execution_context,
                    workflowState.checkpoint_data,
                    workflowState.error_history
                ]
            );

            logger.info(`✅ 创建工作流状态: ${workflowId} for ticket ${ticketId}`);
            return workflowId;
            
        } catch (error) {
            logger.error('创建工作流状态失败:', error);
            throw error;
        }
    }

    /**
     * 获取工作流状态
     */
    async getWorkflowState(workflowId) {
        try {
            const state = await this.db.get(
                'SELECT * FROM workflow_states WHERE workflow_id = ?',
                [workflowId]
            );
            
            if (state) {
                // 解析JSON字段
                state.node_history = this.parseJSON(state.node_history, []);
                state.execution_context = this.parseJSON(state.execution_context, {});
                state.checkpoint_data = this.parseJSON(state.checkpoint_data, {});
                state.error_history = this.parseJSON(state.error_history, []);
            }
            
            return state;
        } catch (error) {
            logger.error('获取工作流状态失败:', error);
            throw error;
        }
    }

    /**
     * 根据工单ID获取工作流状态
     */
    async getWorkflowStateByTicketId(ticketId) {
        try {
            const state = await this.db.get(
                'SELECT * FROM workflow_states WHERE ticket_id = ? AND status IN (?, ?) ORDER BY created_at DESC LIMIT 1',
                [ticketId, this.WORKFLOW_STATUSES.ACTIVE, this.WORKFLOW_STATUSES.SUSPENDED]
            );
            
            if (state) {
                // 解析JSON字段
                state.node_history = this.parseJSON(state.node_history, []);
                state.execution_context = this.parseJSON(state.execution_context, {});
                state.checkpoint_data = this.parseJSON(state.checkpoint_data, {});
                state.error_history = this.parseJSON(state.error_history, []);
            }
            
            return state;
        } catch (error) {
            logger.error('根据工单ID获取工作流状态失败:', error);
            throw error;
        }
    }

    /**
     * 更新工作流状态
     */
    async updateWorkflowState(workflowId, updates) {
        try {
            const updateFields = [];
            const updateValues = [];

            // 构建动态更新语句
            Object.keys(updates).forEach(key => {
                if (key === 'node_history' || key === 'execution_context' || 
                    key === 'checkpoint_data' || key === 'error_history') {
                    updateFields.push(`${key} = ?`);
                    updateValues.push(JSON.stringify(updates[key]));
                } else {
                    updateFields.push(`${key} = ?`);
                    updateValues.push(updates[key]);
                }
            });

            updateFields.push('updated_at = CURRENT_TIMESTAMP');
            updateFields.push('last_active_at = CURRENT_TIMESTAMP');
            updateValues.push(workflowId);

            await this.db.run(
                `UPDATE workflow_states SET ${updateFields.join(', ')} WHERE workflow_id = ?`,
                updateValues
            );

            logger.debug(`更新工作流状态: ${workflowId}`);
            return await this.getWorkflowState(workflowId);
            
        } catch (error) {
            logger.error('更新工作流状态失败:', error);
            throw error;
        }
    }

    /**
     * 保存检查点
     */
    async saveCheckpoint(workflowId, checkpointData, currentNode) {
        try {
            const state = await this.getWorkflowState(workflowId);
            if (!state) {
                throw new Error(`工作流状态不存在: ${workflowId}`);
            }

            // 更新节点历史
            const nodeHistory = [...state.node_history];
            if (nodeHistory[nodeHistory.length - 1] !== currentNode) {
                nodeHistory.push(currentNode);
            }

            await this.updateWorkflowState(workflowId, {
                current_node: currentNode,
                node_history: nodeHistory,
                checkpoint_data: checkpointData
            });

            logger.debug(`保存检查点: ${workflowId} -> ${currentNode}`);
            return true;
            
        } catch (error) {
            logger.error('保存检查点失败:', error);
            throw error;
        }
    }

    /**
     * 挂起工作流
     */
    async suspendWorkflow(workflowId, reason = '') {
        try {
            await this.updateWorkflowState(workflowId, {
                status: this.WORKFLOW_STATUSES.SUSPENDED,
                suspended_at: new Date().toISOString()
            });

            logger.info(`挂起工作流: ${workflowId}, 原因: ${reason}`);
            return true;
            
        } catch (error) {
            logger.error('挂起工作流失败:', error);
            throw error;
        }
    }

    /**
     * 恢复工作流
     */
    async resumeWorkflow(workflowId) {
        try {
            await this.updateWorkflowState(workflowId, {
                status: this.WORKFLOW_STATUSES.ACTIVE,
                suspended_at: null
            });

            logger.info(`恢复工作流: ${workflowId}`);
            return await this.getWorkflowState(workflowId);
            
        } catch (error) {
            logger.error('恢复工作流失败:', error);
            throw error;
        }
    }

    /**
     * 完成工作流
     */
    async completeWorkflow(workflowId, finalResult = {}) {
        try {
            await this.updateWorkflowState(workflowId, {
                status: this.WORKFLOW_STATUSES.COMPLETED,
                completed_at: new Date().toISOString(),
                checkpoint_data: finalResult
            });

            logger.info(`完成工作流: ${workflowId}`);
            return true;
            
        } catch (error) {
            logger.error('完成工作流失败:', error);
            throw error;
        }
    }

    /**
     * 标记工作流失败
     */
    async failWorkflow(workflowId, error) {
        try {
            const state = await this.getWorkflowState(workflowId);
            const errorHistory = state ? [...state.error_history] : [];
            
            errorHistory.push({
                timestamp: new Date().toISOString(),
                error: error.message || error,
                stack: error.stack,
                node: state?.current_node
            });

            await this.updateWorkflowState(workflowId, {
                status: this.WORKFLOW_STATUSES.FAILED,
                error_history: errorHistory
            });

            logger.error(`工作流失败: ${workflowId}`, error);
            return true;
            
        } catch (updateError) {
            logger.error('标记工作流失败时出错:', updateError);
            throw updateError;
        }
    }

    /**
     * 获取活跃的工作流列表
     */
    async getActiveWorkflows() {
        try {
            const workflows = await this.db.all(
                'SELECT * FROM workflow_states WHERE status = ? ORDER BY last_active_at DESC',
                [this.WORKFLOW_STATUSES.ACTIVE]
            );

            return workflows.map(workflow => {
                workflow.node_history = this.parseJSON(workflow.node_history, []);
                workflow.execution_context = this.parseJSON(workflow.execution_context, {});
                workflow.checkpoint_data = this.parseJSON(workflow.checkpoint_data, {});
                workflow.error_history = this.parseJSON(workflow.error_history, []);
                return workflow;
            });
            
        } catch (error) {
            logger.error('获取活跃工作流失败:', error);
            throw error;
        }
    }

    /**
     * 获取挂起的工作流列表
     */
    async getSuspendedWorkflows() {
        try {
            const workflows = await this.db.all(
                'SELECT * FROM workflow_states WHERE status = ? ORDER BY suspended_at DESC',
                [this.WORKFLOW_STATUSES.SUSPENDED]
            );

            return workflows.map(workflow => {
                workflow.node_history = this.parseJSON(workflow.node_history, []);
                workflow.execution_context = this.parseJSON(workflow.execution_context, {});
                workflow.checkpoint_data = this.parseJSON(workflow.checkpoint_data, {});
                workflow.error_history = this.parseJSON(workflow.error_history, []);
                return workflow;
            });
            
        } catch (error) {
            logger.error('获取挂起工作流失败:', error);
            throw error;
        }
    }

    /**
     * 清理过期的已完成工作流
     */
    async cleanupCompletedWorkflows(daysOld = 7) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);

            const result = await this.db.run(
                `DELETE FROM workflow_states 
                 WHERE status IN (?, ?) AND completed_at < ?`,
                [this.WORKFLOW_STATUSES.COMPLETED, this.WORKFLOW_STATUSES.FAILED, cutoffDate.toISOString()]
            );

            logger.info(`清理了 ${result.changes} 个过期的工作流状态`);
            return result.changes;
            
        } catch (error) {
            logger.error('清理过期工作流失败:', error);
            throw error;
        }
    }

    /**
     * 安全解析JSON字符串
     */
    parseJSON(jsonString, defaultValue = null) {
        try {
            return jsonString ? JSON.parse(jsonString) : defaultValue;
        } catch (error) {
            logger.warn('JSON解析失败:', error.message);
            return defaultValue;
        }
    }

    /**
     * 获取工作流统计信息
     */
    async getWorkflowStats() {
        try {
            const stats = await this.db.all(
                `SELECT status, COUNT(*) as count 
                 FROM workflow_states 
                 GROUP BY status`
            );

            const result = {
                active: 0,
                suspended: 0,
                completed: 0,
                failed: 0,
                total: 0
            };

            stats.forEach(stat => {
                result[stat.status] = stat.count;
                result.total += stat.count;
            });

            return result;
            
        } catch (error) {
            logger.error('获取工作流统计失败:', error);
            throw error;
        }
    }
}

module.exports = WorkflowStateManager;