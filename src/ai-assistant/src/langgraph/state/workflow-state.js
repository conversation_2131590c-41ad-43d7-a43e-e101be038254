/**
 * LangGraph工作流状态定义
 * 定义工作流执行过程中的状态结构
 */

/**
 * 工作流状态接口定义
 */
class WorkflowState {
    constructor(ticketId, workflowId = null) {
        // 基础信息
        this.ticketId = ticketId;
        this.workflowId = workflowId;
        this.startTime = new Date().toISOString();
        
        // 执行状态
        this.currentPhase = 'queue'; // queue, planning, executing, validating, completed
        this.currentNode = 'start_node';
        this.nodeHistory = ['start_node'];
        
        // PDCA循环状态
        this.cycleCount = 0;
        this.maxCycles = 50;
        this.pdcaPhase = 'plan'; // plan, do, check, act
        
        // 执行上下文
        this.executionContext = {
            browserState: null,
            currentPage: null,
            lastScreenshot: null,
            evidenceHistory: [],
            targetUrl: null,
            sessionData: {}
        };
        
        // 错误处理
        this.errorHistory = [];
        this.consecutiveFailures = 0;
        this.maxConsecutiveFailures = 3;
        
        // 控制标志
        this.shouldSuspend = false;
        this.needsUserIntervention = false;
        this.interventionReason = null;
        this.missingInfo = [];
        
        // 检查点数据
        this.checkpointData = {};
        
        // 统计信息
        this.stats = {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            totalDuration: 0
        };
        
        // 工单相关
        this.ticket = null;
        this.originalContent = null;
        this.taskGoal = null;
        
        // 结果数据
        this.finalResult = null;
        this.executionReport = null;
    }

    /**
     * 更新当前节点
     */
    updateCurrentNode(nodeName) {
        this.currentNode = nodeName;
        if (this.nodeHistory[this.nodeHistory.length - 1] !== nodeName) {
            this.nodeHistory.push(nodeName);
        }
    }

    /**
     * 增加循环计数
     */
    incrementCycle() {
        this.cycleCount++;
    }

    /**
     * 更新PDCA阶段
     */
    updatePDCAPhase(phase) {
        const validPhases = ['plan', 'do', 'check', 'act'];
        if (validPhases.includes(phase)) {
            this.pdcaPhase = phase;
        }
    }

    /**
     * 添加错误记录
     */
    addError(error, node = null) {
        const errorRecord = {
            timestamp: new Date().toISOString(),
            error: error.message || error,
            stack: error.stack,
            node: node || this.currentNode,
            cycle: this.cycleCount
        };
        
        this.errorHistory.push(errorRecord);
        this.consecutiveFailures++;
    }

    /**
     * 重置连续失败计数
     */
    resetConsecutiveFailures() {
        this.consecutiveFailures = 0;
    }

    /**
     * 检查是否应该终止
     */
    shouldTerminate() {
        // 检查最大循环次数
        if (this.cycleCount >= this.maxCycles) {
            return { terminate: true, reason: 'MAX_CYCLES_REACHED' };
        }
        
        // 检查连续失败次数
        if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
            return { terminate: true, reason: 'MAX_CONSECUTIVE_FAILURES' };
        }
        
        // 检查挂起标志
        if (this.shouldSuspend) {
            return { terminate: true, reason: 'SUSPENDED' };
        }
        
        // 检查用户干预
        if (this.needsUserIntervention) {
            return { terminate: true, reason: 'USER_INTERVENTION_REQUIRED' };
        }
        
        return { terminate: false };
    }

    /**
     * 请求用户干预
     */
    requestUserIntervention(reason, missingInfo = []) {
        this.needsUserIntervention = true;
        this.interventionReason = reason;
        this.missingInfo = missingInfo;
    }

    /**
     * 请求挂起
     */
    requestSuspend() {
        this.shouldSuspend = true;
    }

    /**
     * 更新执行统计
     */
    updateStats(success, duration = 0) {
        this.stats.totalExecutions++;
        this.stats.totalDuration += duration;
        
        if (success) {
            this.stats.successfulExecutions++;
            this.resetConsecutiveFailures();
        } else {
            this.stats.failedExecutions++;
        }
    }

    /**
     * 设置最终结果
     */
    setFinalResult(result, report = null) {
        this.finalResult = result;
        this.executionReport = report;
    }

    /**
     * 转换为可序列化的对象
     */
    toSerializable() {
        return {
            // 基础信息
            ticketId: this.ticketId,
            workflowId: this.workflowId,
            startTime: this.startTime,
            
            // 执行状态
            currentPhase: this.currentPhase,
            currentNode: this.currentNode,
            nodeHistory: [...this.nodeHistory],
            
            // PDCA状态
            cycleCount: this.cycleCount,
            maxCycles: this.maxCycles,
            pdcaPhase: this.pdcaPhase,
            
            // 执行上下文
            executionContext: { ...this.executionContext },
            
            // 错误处理
            errorHistory: [...this.errorHistory],
            consecutiveFailures: this.consecutiveFailures,
            
            // 控制标志
            shouldSuspend: this.shouldSuspend,
            needsUserIntervention: this.needsUserIntervention,
            interventionReason: this.interventionReason,
            missingInfo: [...this.missingInfo],
            
            // 检查点数据
            checkpointData: { ...this.checkpointData },
            
            // 统计信息
            stats: { ...this.stats },
            
            // 工单相关
            ticket: this.ticket,
            originalContent: this.originalContent,
            taskGoal: this.taskGoal,
            
            // 结果数据
            finalResult: this.finalResult,
            executionReport: this.executionReport
        };
    }

    /**
     * 从序列化对象恢复状态
     */
    static fromSerializable(data) {
        const state = new WorkflowState(data.ticketId, data.workflowId);
        
        // 恢复所有属性
        Object.assign(state, data);
        
        return state;
    }

    /**
     * 创建检查点
     */
    createCheckpoint() {
        return {
            timestamp: new Date().toISOString(),
            state: this.toSerializable()
        };
    }

    /**
     * 压缩历史记录（保持上下文可管理）
     */
    compressHistory() {
        // 保留最近的记录
        if (this.nodeHistory.length > 20) {
            this.nodeHistory = this.nodeHistory.slice(-10);
        }
        
        if (this.errorHistory.length > 10) {
            this.errorHistory = this.errorHistory.slice(-5);
        }
        
        if (this.executionContext.evidenceHistory && 
            this.executionContext.evidenceHistory.length > 10) {
            this.executionContext.evidenceHistory = 
                this.executionContext.evidenceHistory.slice(-5);
        }
    }
}

/**
 * 节点结果定义
 */
class NodeResult {
    constructor(success = true, data = null, nextNode = null) {
        this.success = success;
        this.data = data;
        this.nextNode = nextNode;
        this.timestamp = new Date().toISOString();
        this.error = null;
        this.shouldTerminate = false;
        this.terminationReason = null;
    }

    /**
     * 设置错误
     */
    setError(error) {
        this.success = false;
        this.error = {
            message: error.message || error,
            stack: error.stack,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 设置终止
     */
    setTerminate(reason) {
        this.shouldTerminate = true;
        this.terminationReason = reason;
    }

    /**
     * 设置下一个节点
     */
    setNextNode(nodeName) {
        this.nextNode = nodeName;
    }
}

/**
 * 执行上下文定义
 */
class ExecutionContext {
    constructor() {
        this.browserState = {
            currentUrl: null,
            pageTitle: null,
            isLoggedIn: false,
            sessionCookies: [],
            viewportSize: { width: 1920, height: 1080 }
        };
        
        this.pageState = {
            elements: [],
            forms: [],
            buttons: [],
            links: [],
            lastSnapshot: null
        };
        
        this.actionHistory = [];
        this.screenshots = [];
        this.evidenceData = [];
        
        this.temporaryData = {};
        this.persistentData = {};
    }

    /**
     * 添加操作记录
     */
    addAction(action, result) {
        this.actionHistory.push({
            timestamp: new Date().toISOString(),
            action: action,
            result: result
        });
    }

    /**
     * 添加截图
     */
    addScreenshot(screenshot, description = '') {
        this.screenshots.push({
            timestamp: new Date().toISOString(),
            data: screenshot,
            description: description,
            url: this.browserState.currentUrl
        });
    }

    /**
     * 添加证据数据
     */
    addEvidence(evidence) {
        this.evidenceData.push({
            timestamp: new Date().toISOString(),
            ...evidence
        });
    }

    /**
     * 清理临时数据
     */
    clearTemporaryData() {
        this.temporaryData = {};
    }
}

module.exports = {
    WorkflowState,
    NodeResult,
    ExecutionContext
};