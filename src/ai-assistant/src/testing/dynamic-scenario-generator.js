/**
 * 动态场景生成器
 * 
 * 🎯 目标：动态生成多样化的测试场景，避免固化测试用例
 * 🔄 原则：每次运行生成不同的场景组合，测试AI的泛化能力
 * 🚫 禁止：预设固定的测试场景和预期结果
 */

const logger = require('../utils/logger');

class DynamicScenarioGenerator {
    constructor() {
        // 业务实体库 - 用于动态组合，而非固定场景
        this.businessEntities = {
            merchants: [
                { name: '榮記豆腐麵食', location: '官也街', specialty: '招牌煲仔飯' },
                { name: '新記茶餐廳', location: '新馬路', specialty: '港式奶茶' },
                { name: '老友記粥麵', location: '十月初五街', specialty: '白粥配菜' },
                { name: '金記燒臘', location: '關前街', specialty: '叉燒飯' },
                { name: '美心餐廳', location: '議事亭前地', specialty: '粵式點心' }
            ],
            products: [
                { name: '菜遠牛肉飯', category: '主食', type: '熱食' },
                { name: '招牌煲仔飯', category: '主食', type: '熱食' },
                { name: '嫩滑豆腐花', category: '甜品', type: '冷食' },
                { name: '港式奶茶', category: '飲品', type: '熱飲' },
                { name: '蝦餃皇', category: '點心', type: '熱食' },
                { name: '叉燒包', category: '點心', type: '熱食' },
                { name: '楊枝甘露', category: '甜品', type: '冷食' }
            ],
            operations: [
                { action: '上架', complexity: 'medium', requirements: ['商品信息', '價格設定', '分類選擇'] },
                { action: '下架', complexity: 'low', requirements: ['商品識別', '下架原因'] },
                { action: '修改價格', complexity: 'medium', requirements: ['商品識別', '新價格', '生效時間'] },
                { action: '批量上架', complexity: 'high', requirements: ['多商品信息', '統一設定', '順序安排'] },
                { action: '促銷設定', complexity: 'high', requirements: ['商品選擇', '促銷規則', '時間限制'] },
                { action: '庫存更新', complexity: 'medium', requirements: ['商品識別', '庫存數量', '預警設定'] }
            ],
            constraints: [
                { type: 'time', description: '需要在營業時間內完成' },
                { type: 'permission', description: '需要相應的操作權限' },
                { type: 'system', description: '系統維護期間功能受限' },
                { type: 'business', description: '符合商家經營策略' },
                { type: 'regulation', description: '遵守平台規則和法規' },
                { type: 'resource', description: '考慮系統資源和性能' }
            ],
            complications: [
                { type: 'data_missing', description: '關鍵信息缺失或不完整' },
                { type: 'system_error', description: '系統異常或服務不可用' },
                { type: 'conflict', description: '操作衝突或資源競爭' },
                { type: 'validation_failure', description: '數據驗證失敗' },
                { type: 'permission_denied', description: '權限不足或被拒絕' },
                { type: 'timeout', description: '操作超時或響應緩慢' }
            ]
        };
    }

    /**
     * 生成基础业务场景
     */
    generateBaseScenario() {
        const merchant = this.randomSelect(this.businessEntities.merchants);
        const product = this.randomSelect(this.businessEntities.products);
        const operation = this.randomSelect(this.businessEntities.operations);
        
        const scenario = {
            id: this.generateScenarioId(),
            type: 'base',
            merchant,
            product,
            operation,
            description: this.constructScenarioDescription(merchant, product, operation),
            complexity: operation.complexity,
            timestamp: Date.now()
        };
        
        logger.info(`生成基础场景: ${scenario.description}`);
        return scenario;
    }

    /**
     * 生成复杂业务场景
     */
    generateComplexScenario() {
        const baseScenario = this.generateBaseScenario();
        const constraints = this.randomSelectMultiple(this.businessEntities.constraints, 2, 3);
        const complications = this.randomSelectMultiple(this.businessEntities.complications, 1, 2);
        
        const complexScenario = {
            ...baseScenario,
            type: 'complex',
            constraints,
            complications,
            description: this.constructComplexScenarioDescription(baseScenario, constraints, complications),
            complexity: 'high'
        };
        
        logger.info(`生成复杂场景: ${complexScenario.description}`);
        return complexScenario;
    }

    /**
     * 生成适应性挑战场景
     */
    generateAdaptationScenario(baseScenario) {
        const challenges = this.randomSelectMultiple(this.businessEntities.complications, 1, 2);
        const additionalConstraints = this.randomSelectMultiple(this.businessEntities.constraints, 1, 2);
        
        const adaptationScenario = {
            ...baseScenario,
            type: 'adaptation',
            originalScenario: baseScenario.description,
            challenges,
            additionalConstraints,
            description: this.constructAdaptationScenarioDescription(baseScenario, challenges, additionalConstraints)
        };
        
        logger.info(`生成适应性场景: ${adaptationScenario.description}`);
        return adaptationScenario;
    }

    /**
     * 生成学习场景序列
     */
    generateLearningSequence() {
        const baseScenario = this.generateBaseScenario();
        const iterations = [];
        
        // 第一次尝试
        iterations.push({
            iteration: 1,
            scenario: baseScenario,
            context: '首次执行，无先验经验'
        });
        
        // 第二次尝试（加入反馈）
        const feedback = this.generateRealisticFeedback(baseScenario);
        iterations.push({
            iteration: 2,
            scenario: baseScenario,
            context: `基于反馈改进执行`,
            feedback
        });
        
        // 第三次尝试（加入新挑战）
        const challenge = this.randomSelect(this.businessEntities.complications);
        iterations.push({
            iteration: 3,
            scenario: {
                ...baseScenario,
                newChallenge: challenge
            },
            context: `面对新挑战的适应性执行`,
            previousExperience: '已有两次执行经验'
        });
        
        logger.info(`生成学习序列: ${iterations.length}个迭代`);
        return iterations;
    }

    /**
     * 生成语义变体场景
     */
    generateSemanticVariants(baseScenario) {
        const variants = [];
        const styles = ['formal', 'casual', 'detailed', 'simplified', 'technical'];
        
        for (const style of styles) {
            const variant = {
                ...baseScenario,
                style,
                description: this.paraphraseDescription(baseScenario.description, style),
                originalDescription: baseScenario.description
            };
            variants.push(variant);
        }
        
        logger.info(`生成语义变体: ${variants.length}个版本`);
        return variants;
    }

    /**
     * 生成压力测试场景
     */
    generateStressScenario() {
        const baseScenario = this.generateBaseScenario();
        const stressFactors = {
            timeConstraint: '需要在5分钟内完成',
            highVolume: '需要同时处理50个类似请求',
            systemLoad: '系统当前负载95%',
            concurrentUsers: '200个用户同时在线操作',
            dataVolume: '涉及10000+商品记录'
        };
        
        const selectedStressors = this.randomSelectMultiple(Object.entries(stressFactors), 2, 3);
        
        const stressScenario = {
            ...baseScenario,
            type: 'stress',
            stressFactors: Object.fromEntries(selectedStressors),
            description: this.constructStressScenarioDescription(baseScenario, selectedStressors)
        };
        
        logger.info(`生成压力测试场景: ${stressScenario.description}`);
        return stressScenario;
    }

    /**
     * 构造场景描述
     */
    constructScenarioDescription(merchant, product, operation) {
        const templates = [
            `${operation.action}${merchant.name}的${product.name}`,
            `为${merchant.name}(${merchant.location})${operation.action}${product.category}商品：${product.name}`,
            `${merchant.location}的${merchant.name}需要${operation.action}${product.name}`,
            `协助${merchant.name}在系统中${operation.action}${product.name}(${product.category})`
        ];
        
        return this.randomSelect(templates);
    }

    /**
     * 构造复杂场景描述
     */
    constructComplexScenarioDescription(baseScenario, constraints, complications) {
        let description = baseScenario.description;
        
        if (constraints.length > 0) {
            const constraintDesc = constraints.map(c => c.description).join('，');
            description += `，约束条件：${constraintDesc}`;
        }
        
        if (complications.length > 0) {
            const complicationDesc = complications.map(c => c.description).join('，');
            description += `，面临挑战：${complicationDesc}`;
        }
        
        return description;
    }

    /**
     * 构造适应性场景描述
     */
    constructAdaptationScenarioDescription(baseScenario, challenges, additionalConstraints) {
        let description = `原始任务：${baseScenario.description}`;
        
        if (challenges.length > 0) {
            const challengeDesc = challenges.map(c => c.description).join('，');
            description += `\n新出现的挑战：${challengeDesc}`;
        }
        
        if (additionalConstraints.length > 0) {
            const constraintDesc = additionalConstraints.map(c => c.description).join('，');
            description += `\n额外约束：${constraintDesc}`;
        }
        
        return description;
    }

    /**
     * 构造压力测试场景描述
     */
    constructStressScenarioDescription(baseScenario, stressFactors) {
        let description = baseScenario.description;
        const stressDesc = stressFactors.map(([key, value]) => value).join('，');
        description += `，压力条件：${stressDesc}`;
        
        return description;
    }

    /**
     * 改写描述（不同风格）
     */
    paraphraseDescription(description, style) {
        const synonyms = {
            '上架': ['添加', '新增', '发布', '上线'],
            '下架': ['移除', '删除', '撤销', '下线'],
            '修改': ['调整', '更新', '变更', '编辑'],
            '商品': ['产品', '物品', '商品项目'],
            '需要': ['要求', '希望', '计划', '准备']
        };
        
        let paraphrased = description;
        
        switch (style) {
            case 'formal':
                paraphrased = `请执行以下业务操作：${description}`;
                break;
            case 'casual':
                paraphrased = `帮忙${description}`;
                break;
            case 'detailed':
                paraphrased = `详细任务说明：${description}，请确保操作准确无误`;
                break;
            case 'simplified':
                paraphrased = description.replace(/[（）()]/g, '').replace(/，.*$/, '');
                break;
            case 'technical':
                paraphrased = `系统操作请求：${description}`;
                break;
        }
        
        // 应用同义词替换
        for (const [original, alternatives] of Object.entries(synonyms)) {
            if (paraphrased.includes(original)) {
                const randomAlt = this.randomSelect(alternatives);
                paraphrased = paraphrased.replace(original, randomAlt);
            }
        }
        
        return paraphrased;
    }

    /**
     * 生成现实反馈
     */
    generateRealisticFeedback(scenario) {
        const feedbackTypes = [
            '执行效率可以提升',
            '需要更好的错误处理',
            '用户体验有改进空间',
            '数据验证不够严格',
            '操作流程可以优化',
            '需要考虑边界情况'
        ];
        
        return this.randomSelect(feedbackTypes);
    }

    /**
     * 生成场景ID
     */
    generateScenarioId() {
        return `scenario_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 随机选择单个元素
     */
    randomSelect(array) {
        return array[Math.floor(Math.random() * array.length)];
    }

    /**
     * 随机选择多个元素
     */
    randomSelectMultiple(array, min, max) {
        const count = Math.floor(Math.random() * (max - min + 1)) + min;
        const shuffled = [...array].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, Math.min(count, array.length));
    }

    /**
     * 生成测试套件
     */
    generateTestSuite(suiteType = 'comprehensive') {
        const suite = {
            id: this.generateScenarioId(),
            type: suiteType,
            scenarios: [],
            timestamp: Date.now()
        };

        switch (suiteType) {
            case 'basic':
                suite.scenarios = [
                    this.generateBaseScenario(),
                    this.generateBaseScenario(),
                    this.generateBaseScenario()
                ];
                break;
                
            case 'complex':
                suite.scenarios = [
                    this.generateComplexScenario(),
                    this.generateComplexScenario()
                ];
                break;
                
            case 'adaptation':
                const baseForAdaptation = this.generateBaseScenario();
                suite.scenarios = [
                    baseForAdaptation,
                    this.generateAdaptationScenario(baseForAdaptation),
                    this.generateAdaptationScenario(baseForAdaptation)
                ];
                break;
                
            case 'comprehensive':
                suite.scenarios = [
                    this.generateBaseScenario(),
                    this.generateComplexScenario(),
                    ...this.generateLearningSequence(),
                    this.generateStressScenario()
                ];
                break;
        }

        logger.info(`生成测试套件 (${suiteType}): ${suite.scenarios.length}个场景`);
        return suite;
    }
}

module.exports = DynamicScenarioGenerator;
