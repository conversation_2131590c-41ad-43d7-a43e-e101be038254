/**
 * AI智能特征评估器
 * 
 * 🎯 目标：量化评估AI的智能特征，而非验证预设输出
 * 📊 评估维度：理解力、推理力、适应力、创新力
 * 🚫 禁止：任何形式的模板匹配或预设答案验证
 */

const logger = require('../utils/logger');

class AIIntelligenceEvaluator {
    constructor() {
        this.evaluationCriteria = {
            understanding: {
                semanticAccuracy: 0.3,      // 语义准确性
                contextAwareness: 0.3,      // 上下文感知
                intentRecognition: 0.4      // 意图识别
            },
            reasoning: {
                logicalCoherence: 0.4,      // 逻辑连贯性
                causalThinking: 0.3,        // 因果思维
                constraintHandling: 0.3     // 约束处理
            },
            adaptation: {
                flexibilityScore: 0.4,      // 灵活性
                problemSolving: 0.3,        // 问题解决
                innovativeThinking: 0.3     // 创新思维
            },
            learning: {
                experienceUtilization: 0.5, // 经验利用
                errorCorrection: 0.5        // 错误纠正
            }
        };
    }

    /**
     * 评估AI的语义理解能力
     * 通过多种表达方式测试AI对相同意图的理解一致性
     */
    async evaluateUnderstanding(llmClient, baseScenario) {
        logger.info('🧠 评估AI语义理解能力...');
        
        // 生成语义等价的不同表达
        const semanticVariants = this.generateSemanticVariants(baseScenario);
        const responses = [];
        
        for (const variant of semanticVariants) {
            try {
                const response = await llmClient.chat(`
请分析以下业务指令的核心要素：
"${variant}"

请识别：
1. 主要操作类型
2. 关键业务实体
3. 操作目标和约束
4. 潜在的执行挑战

用自然语言详细描述你的分析过程和结论。
                `.trim());
                
                responses.push({
                    variant,
                    analysis: response,
                    timestamp: Date.now()
                });
                
            } catch (error) {
                logger.error(`语义理解测试失败: ${error.message}`);
                responses.push({
                    variant,
                    analysis: null,
                    error: error.message
                });
            }
        }
        
        return this.calculateUnderstandingScore(responses, baseScenario);
    }

    /**
     * 评估AI的推理能力
     * 测试AI在复杂约束下的逻辑推理和问题分解能力
     */
    async evaluateReasoning(llmClient, complexScenario) {
        logger.info('🎯 评估AI推理能力...');
        
        const reasoningPrompt = `
业务场景：${complexScenario}

请进行深度分析：
1. 分解这个任务的关键组成部分
2. 识别可能的执行路径和决策点
3. 分析潜在风险和应对策略
4. 推理出最优的执行方案

请详细展示你的推理过程，包括：
- 问题分析的逻辑链条
- 决策的依据和考量
- 方案的优劣比较
- 风险评估和缓解措施
        `.trim();

        try {
            const response = await llmClient.chat(reasoningPrompt);
            return this.calculateReasoningScore(response, complexScenario);
        } catch (error) {
            logger.error(`推理能力测试失败: ${error.message}`);
            return { score: 0, details: { error: error.message } };
        }
    }

    /**
     * 评估AI的适应能力
     * 测试AI面对异常情况和变化环境的应变能力
     */
    async evaluateAdaptation(llmClient, baseScenario) {
        logger.info('🔄 评估AI适应能力...');
        
        const adaptationScenarios = this.generateAdaptationChallenges(baseScenario);
        const adaptationResults = [];
        
        for (const challenge of adaptationScenarios) {
            try {
                const response = await llmClient.chat(`
原始任务：${baseScenario}
新的挑战：${challenge.description}

请分析：
1. 这个新挑战如何影响原始任务？
2. 需要调整哪些策略或方法？
3. 如何在新约束下仍然达成目标？
4. 有什么创新的解决思路？

请展示你的适应性思维过程。
                `.trim());
                
                adaptationResults.push({
                    challenge: challenge.type,
                    response,
                    adaptationQuality: this.assessAdaptationQuality(response, challenge)
                });
                
            } catch (error) {
                logger.error(`适应能力测试失败: ${error.message}`);
                adaptationResults.push({
                    challenge: challenge.type,
                    response: null,
                    error: error.message
                });
            }
        }
        
        return this.calculateAdaptationScore(adaptationResults);
    }

    /**
     * 评估AI的学习能力
     * 测试AI从经验中学习和改进的能力
     */
    async evaluateLearning(llmClient, scenario) {
        logger.info('📚 评估AI学习能力...');
        
        // 第一次执行
        const firstAttempt = await llmClient.chat(`
任务：${scenario}
这是你第一次处理这类任务，请制定执行方案。
        `.trim());
        
        // 模拟反馈和第二次执行
        const feedback = "第一次执行中发现了一些效率问题和潜在风险";
        const secondAttempt = await llmClient.chat(`
任务：${scenario}
之前的经验：${firstAttempt}
反馈：${feedback}

基于之前的经验和反馈，请制定改进的执行方案。
        `.trim());
        
        return this.calculateLearningScore(firstAttempt, secondAttempt, feedback);
    }

    /**
     * 生成语义等价的不同表达
     */
    generateSemanticVariants(baseScenario) {
        // 基于基础场景生成语义等价但表达不同的变体
        const variants = [
            baseScenario, // 原始表达
            this.paraphraseScenario(baseScenario, 'formal'),
            this.paraphraseScenario(baseScenario, 'casual'),
            this.paraphraseScenario(baseScenario, 'detailed'),
            this.paraphraseScenario(baseScenario, 'simplified')
        ];
        
        return variants.filter(v => v && v.length > 0);
    }

    /**
     * 改写场景表达方式
     */
    paraphraseScenario(scenario, style) {
        // 这里应该使用AI来生成改写，但为了避免循环依赖，使用规则改写
        const baseTerms = {
            '上架': ['添加', '新增', '发布', '上线'],
            '外卖商品': ['外卖产品', '配送商品', '外送餐品', '线上商品'],
            '需要': ['要求', '希望', '计划', '准备']
        };
        
        let paraphrased = scenario;
        for (const [original, alternatives] of Object.entries(baseTerms)) {
            if (paraphrased.includes(original)) {
                const randomAlt = alternatives[Math.floor(Math.random() * alternatives.length)];
                paraphrased = paraphrased.replace(original, randomAlt);
            }
        }
        
        return paraphrased;
    }

    /**
     * 生成适应性挑战场景
     */
    generateAdaptationChallenges(baseScenario) {
        return [
            {
                type: 'system_limitation',
                description: '系统当前处于维护模式，部分功能不可用'
            },
            {
                type: 'permission_constraint',
                description: '当前用户权限不足，无法直接执行某些操作'
            },
            {
                type: 'data_incomplete',
                description: '关键信息缺失，需要额外收集数据'
            },
            {
                type: 'time_pressure',
                description: '任务需要在极短时间内完成'
            },
            {
                type: 'resource_conflict',
                description: '多个任务竞争相同的系统资源'
            }
        ];
    }

    /**
     * 计算理解能力得分
     */
    calculateUnderstandingScore(responses, baseScenario) {
        const validResponses = responses.filter(r => r.analysis);
        if (validResponses.length === 0) {
            return { score: 0, details: { error: '无有效响应' } };
        }
        
        // 提取关键概念一致性
        const keyTerms = this.extractKeyTerms(baseScenario);
        let consistencyScore = 0;
        let contextScore = 0;
        let intentScore = 0;
        
        for (const response of validResponses) {
            // 语义一致性评估
            const termCoverage = this.calculateTermCoverage(response.analysis, keyTerms);
            consistencyScore += termCoverage;
            
            // 上下文感知评估
            const contextAwareness = this.assessContextAwareness(response.analysis);
            contextScore += contextAwareness;
            
            // 意图识别评估
            const intentRecognition = this.assessIntentRecognition(response.analysis, baseScenario);
            intentScore += intentRecognition;
        }
        
        const avgConsistency = consistencyScore / validResponses.length;
        const avgContext = contextScore / validResponses.length;
        const avgIntent = intentScore / validResponses.length;
        
        const totalScore = (
            avgConsistency * this.evaluationCriteria.understanding.semanticAccuracy +
            avgContext * this.evaluationCriteria.understanding.contextAwareness +
            avgIntent * this.evaluationCriteria.understanding.intentRecognition
        );
        
        return {
            score: totalScore,
            details: {
                semanticAccuracy: avgConsistency,
                contextAwareness: avgContext,
                intentRecognition: avgIntent,
                responseCount: validResponses.length
            }
        };
    }

    /**
     * 计算推理能力得分
     */
    calculateReasoningScore(response, scenario) {
        const logicalCoherence = this.assessLogicalCoherence(response);
        const causalThinking = this.assessCausalThinking(response);
        const constraintHandling = this.assessConstraintHandling(response, scenario);
        
        const totalScore = (
            logicalCoherence * this.evaluationCriteria.reasoning.logicalCoherence +
            causalThinking * this.evaluationCriteria.reasoning.causalThinking +
            constraintHandling * this.evaluationCriteria.reasoning.constraintHandling
        );
        
        return {
            score: totalScore,
            details: {
                logicalCoherence,
                causalThinking,
                constraintHandling
            }
        };
    }

    /**
     * 计算适应能力得分
     */
    calculateAdaptationScore(adaptationResults) {
        const validResults = adaptationResults.filter(r => r.response);
        if (validResults.length === 0) {
            return { score: 0, details: { error: '无有效适应性响应' } };
        }
        
        let totalFlexibility = 0;
        let totalProblemSolving = 0;
        let totalInnovation = 0;
        
        for (const result of validResults) {
            totalFlexibility += result.adaptationQuality.flexibility || 0;
            totalProblemSolving += result.adaptationQuality.problemSolving || 0;
            totalInnovation += result.adaptationQuality.innovation || 0;
        }
        
        const avgFlexibility = totalFlexibility / validResults.length;
        const avgProblemSolving = totalProblemSolving / validResults.length;
        const avgInnovation = totalInnovation / validResults.length;
        
        const totalScore = (
            avgFlexibility * this.evaluationCriteria.adaptation.flexibilityScore +
            avgProblemSolving * this.evaluationCriteria.adaptation.problemSolving +
            avgInnovation * this.evaluationCriteria.adaptation.innovativeThinking
        );
        
        return {
            score: totalScore,
            details: {
                flexibility: avgFlexibility,
                problemSolving: avgProblemSolving,
                innovation: avgInnovation,
                challengeCount: validResults.length
            }
        };
    }

    /**
     * 计算学习能力得分
     */
    calculateLearningScore(firstAttempt, secondAttempt, feedback) {
        const experienceUtilization = this.assessExperienceUtilization(firstAttempt, secondAttempt);
        const errorCorrection = this.assessErrorCorrection(firstAttempt, secondAttempt, feedback);
        
        const totalScore = (
            experienceUtilization * this.evaluationCriteria.learning.experienceUtilization +
            errorCorrection * this.evaluationCriteria.learning.errorCorrection
        );
        
        return {
            score: totalScore,
            details: {
                experienceUtilization,
                errorCorrection
            }
        };
    }

    // 辅助评估方法
    extractKeyTerms(text) {
        // 提取关键业务术语
        const terms = [];
        const patterns = [
            /[\u4e00-\u9fa5]+(?:商品|产品|服务)/g,
            /[\u4e00-\u9fa5]+(?:餐厅|店铺|商家)/g,
            /(?:上架|下架|添加|删除|修改)/g
        ];
        
        for (const pattern of patterns) {
            const matches = text.match(pattern);
            if (matches) terms.push(...matches);
        }
        
        return [...new Set(terms)];
    }

    calculateTermCoverage(response, keyTerms) {
        if (keyTerms.length === 0) return 1;
        
        let coveredTerms = 0;
        for (const term of keyTerms) {
            if (response.includes(term) || this.hasSemanticallyEquivalent(response, term)) {
                coveredTerms++;
            }
        }
        
        return coveredTerms / keyTerms.length;
    }

    hasSemanticallyEquivalent(text, term) {
        const equivalents = {
            '上架': ['添加', '新增', '发布', '上线'],
            '商品': ['产品', '物品', '商品'],
            '外卖': ['配送', '外送', '线上']
        };
        
        const alternatives = equivalents[term] || [];
        return alternatives.some(alt => text.includes(alt));
    }

    assessContextAwareness(response) {
        // 评估AI是否理解了业务上下文
        const contextIndicators = [
            '业务', '商家', '客户', '用户', '系统', '平台',
            '流程', '操作', '管理', '服务'
        ];
        
        let contextScore = 0;
        for (const indicator of contextIndicators) {
            if (response.includes(indicator)) {
                contextScore += 0.1;
            }
        }
        
        return Math.min(contextScore, 1.0);
    }

    assessIntentRecognition(response, scenario) {
        // 评估AI是否正确识别了操作意图
        const intentKeywords = ['目标', '目的', '意图', '要求', '需要'];
        let intentScore = 0;
        
        for (const keyword of intentKeywords) {
            if (response.includes(keyword)) {
                intentScore += 0.2;
            }
        }
        
        return Math.min(intentScore, 1.0);
    }

    assessLogicalCoherence(response) {
        // 评估逻辑连贯性
        const logicIndicators = [
            '首先', '然后', '接下来', '最后', '因此', '所以',
            '由于', '基于', '考虑到', '综合'
        ];
        
        let logicScore = 0;
        for (const indicator of logicIndicators) {
            if (response.includes(indicator)) {
                logicScore += 0.1;
            }
        }
        
        return Math.min(logicScore, 1.0);
    }

    assessCausalThinking(response) {
        // 评估因果思维
        const causalIndicators = [
            '导致', '引起', '造成', '影响', '结果', '后果',
            '原因', '因为', '由于'
        ];
        
        let causalScore = 0;
        for (const indicator of causalIndicators) {
            if (response.includes(indicator)) {
                causalScore += 0.1;
            }
        }
        
        return Math.min(causalScore, 1.0);
    }

    assessConstraintHandling(response, scenario) {
        // 评估约束处理能力
        const constraintIndicators = [
            '限制', '约束', '条件', '要求', '规则', '标准',
            '权限', '资源', '时间', '成本'
        ];
        
        let constraintScore = 0;
        for (const indicator of constraintIndicators) {
            if (response.includes(indicator)) {
                constraintScore += 0.1;
            }
        }
        
        return Math.min(constraintScore, 1.0);
    }

    assessAdaptationQuality(response, challenge) {
        return {
            flexibility: this.assessFlexibility(response, challenge),
            problemSolving: this.assessProblemSolving(response),
            innovation: this.assessInnovation(response)
        };
    }

    assessFlexibility(response, challenge) {
        // 评估灵活性
        const flexibilityIndicators = [
            '调整', '修改', '改变', '适应', '灵活', '变通'
        ];
        
        let flexScore = 0;
        for (const indicator of flexibilityIndicators) {
            if (response.includes(indicator)) {
                flexScore += 0.2;
            }
        }
        
        return Math.min(flexScore, 1.0);
    }

    assessProblemSolving(response) {
        // 评估问题解决能力
        const solvingIndicators = [
            '解决', '处理', '应对', '克服', '方案', '策略',
            '方法', '途径', '办法'
        ];
        
        let solvingScore = 0;
        for (const indicator of solvingIndicators) {
            if (response.includes(indicator)) {
                solvingScore += 0.1;
            }
        }
        
        return Math.min(solvingScore, 1.0);
    }

    assessInnovation(response) {
        // 评估创新思维
        const innovationIndicators = [
            '创新', '新颖', '独特', '创造', '另辟蹊径', '突破',
            '改进', '优化', '提升'
        ];
        
        let innovationScore = 0;
        for (const indicator of innovationIndicators) {
            if (response.includes(indicator)) {
                innovationScore += 0.1;
            }
        }
        
        return Math.min(innovationScore, 1.0);
    }

    assessExperienceUtilization(firstAttempt, secondAttempt) {
        // 评估经验利用能力
        const improvement = secondAttempt.length > firstAttempt.length ? 0.5 : 0;
        const refinement = secondAttempt.includes('改进') || secondAttempt.includes('优化') ? 0.5 : 0;
        
        return improvement + refinement;
    }

    assessErrorCorrection(firstAttempt, secondAttempt, feedback) {
        // 评估错误纠正能力
        const feedbackAddressed = secondAttempt.includes('效率') || secondAttempt.includes('风险') ? 0.5 : 0;
        const learningEvidence = secondAttempt.includes('基于') || secondAttempt.includes('经验') ? 0.5 : 0;
        
        return feedbackAddressed + learningEvidence;
    }
}

module.exports = AIIntelligenceEvaluator;
