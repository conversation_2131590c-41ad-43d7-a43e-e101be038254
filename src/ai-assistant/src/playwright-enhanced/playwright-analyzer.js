/**
 * Playwright增强分析器 - 充分利用Playwright的原生能力
 * 使用Playwright的内置方法进行页面分析和元素定位
 */

class PlaywrightAnalyzer {
    constructor(page, logger = console) {
        this.page = page;
        this.logger = logger;
    }

    /**
     * 使用Playwright原生能力分析页面
     */
    async analyzePage() {
        try {
            this.logger.info('🔍 使用Playwright原生能力分析页面...');

            const analysis = {
                url: this.page.url(),
                title: await this.page.title(),
                timestamp: Date.now(),
                
                // 使用Playwright的locator能力分析各种元素
                buttons: await this.analyzeButtons(),
                inputs: await this.analyzeInputs(),
                links: await this.analyzeLinks(),
                forms: await this.analyzeForms(),
                headings: await this.analyzeHeadings(),
                images: await this.analyzeImages(),
                
                // 页面状态信息
                pageState: await this.getPageState(),
                
                // 可访问性信息
                accessibility: await this.getAccessibilityInfo()
            };

            this.logger.info(`✅ 页面分析完成，发现 ${analysis.buttons.length} 个按钮，${analysis.inputs.length} 个输入框，${analysis.links.length} 个链接`);
            
            return analysis;
        } catch (error) {
            this.logger.error('❌ Playwright页面分析失败:', error);
            throw error;
        }
    }

    /**
     * 分析页面中的按钮
     */
    async analyzeButtons() {
        try {
            const buttons = [];
            
            // 使用Playwright的多种定位方式找到所有按钮
            const buttonSelectors = [
                'button',
                'input[type="button"]',
                'input[type="submit"]',
                '[role="button"]',
                '.btn',
                '.button'
            ];

            for (const selector of buttonSelectors) {
                const elements = await this.page.locator(selector).all();
                
                for (const element of elements) {
                    try {
                        const isVisible = await element.isVisible();
                        if (isVisible) {
                            const text = await element.textContent() || '';
                            const isEnabled = await element.isEnabled();
                            const boundingBox = await element.boundingBox();
                            
                            buttons.push({
                                type: 'button',
                                text: text.trim(),
                                selector: selector,
                                isEnabled: isEnabled,
                                boundingBox: boundingBox,
                                locator: element // 保存Playwright locator对象
                            });
                        }
                    } catch (e) {
                        // 忽略单个元素的错误
                    }
                }
            }

            return buttons;
        } catch (error) {
            this.logger.error('❌ 分析按钮失败:', error);
            return [];
        }
    }

    /**
     * 分析页面中的输入框
     */
    async analyzeInputs() {
        try {
            const inputs = [];
            
            // 使用Playwright的输入框定位
            const inputTypes = ['text', 'email', 'password', 'search', 'tel', 'url', 'number'];
            
            // 分析各种类型的输入框
            for (const type of inputTypes) {
                const elements = await this.page.locator(`input[type="${type}"]`).all();
                
                for (const element of elements) {
                    try {
                        const isVisible = await element.isVisible();
                        if (isVisible) {
                            const placeholder = await element.getAttribute('placeholder') || '';
                            const name = await element.getAttribute('name') || '';
                            const id = await element.getAttribute('id') || '';
                            const value = await element.inputValue() || '';
                            const isEnabled = await element.isEnabled();
                            const boundingBox = await element.boundingBox();
                            
                            inputs.push({
                                type: 'input',
                                inputType: type,
                                placeholder: placeholder,
                                name: name,
                                id: id,
                                value: value,
                                isEnabled: isEnabled,
                                boundingBox: boundingBox,
                                locator: element
                            });
                        }
                    } catch (e) {
                        // 忽略单个元素的错误
                    }
                }
            }

            // 分析textarea
            const textareas = await this.page.locator('textarea').all();
            for (const element of textareas) {
                try {
                    const isVisible = await element.isVisible();
                    if (isVisible) {
                        const placeholder = await element.getAttribute('placeholder') || '';
                        const name = await element.getAttribute('name') || '';
                        const value = await element.inputValue() || '';
                        const isEnabled = await element.isEnabled();
                        
                        inputs.push({
                            type: 'textarea',
                            placeholder: placeholder,
                            name: name,
                            value: value,
                            isEnabled: isEnabled,
                            locator: element
                        });
                    }
                } catch (e) {
                    // 忽略单个元素的错误
                }
            }

            return inputs;
        } catch (error) {
            this.logger.error('❌ 分析输入框失败:', error);
            return [];
        }
    }

    /**
     * 分析页面中的链接
     */
    async analyzeLinks() {
        try {
            const links = [];
            const elements = await this.page.locator('a[href]').all();
            
            for (const element of elements) {
                try {
                    const isVisible = await element.isVisible();
                    if (isVisible) {
                        const text = await element.textContent() || '';
                        const href = await element.getAttribute('href') || '';
                        const target = await element.getAttribute('target') || '';
                        const boundingBox = await element.boundingBox();
                        
                        links.push({
                            type: 'link',
                            text: text.trim(),
                            href: href,
                            target: target,
                            boundingBox: boundingBox,
                            locator: element
                        });
                    }
                } catch (e) {
                    // 忽略单个元素的错误
                }
            }

            return links;
        } catch (error) {
            this.logger.error('❌ 分析链接失败:', error);
            return [];
        }
    }

    /**
     * 分析页面中的表单
     */
    async analyzeForms() {
        try {
            const forms = [];
            const elements = await this.page.locator('form').all();
            
            for (const element of elements) {
                try {
                    const isVisible = await element.isVisible();
                    if (isVisible) {
                        const action = await element.getAttribute('action') || '';
                        const method = await element.getAttribute('method') || 'get';
                        const name = await element.getAttribute('name') || '';
                        
                        // 分析表单内的输入字段
                        const formInputs = await element.locator('input, textarea, select').all();
                        const fields = [];
                        
                        for (const input of formInputs) {
                            const inputName = await input.getAttribute('name') || '';
                            const inputType = await input.getAttribute('type') || 'text';
                            const placeholder = await input.getAttribute('placeholder') || '';
                            
                            fields.push({
                                name: inputName,
                                type: inputType,
                                placeholder: placeholder
                            });
                        }
                        
                        forms.push({
                            type: 'form',
                            action: action,
                            method: method,
                            name: name,
                            fields: fields,
                            locator: element
                        });
                    }
                } catch (e) {
                    // 忽略单个元素的错误
                }
            }

            return forms;
        } catch (error) {
            this.logger.error('❌ 分析表单失败:', error);
            return [];
        }
    }

    /**
     * 分析页面标题
     */
    async analyzeHeadings() {
        try {
            const headings = [];
            const headingTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
            
            for (const tag of headingTags) {
                const elements = await this.page.locator(tag).all();
                
                for (const element of elements) {
                    try {
                        const isVisible = await element.isVisible();
                        if (isVisible) {
                            const text = await element.textContent() || '';
                            
                            headings.push({
                                type: 'heading',
                                level: tag,
                                text: text.trim(),
                                locator: element
                            });
                        }
                    } catch (e) {
                        // 忽略单个元素的错误
                    }
                }
            }

            return headings;
        } catch (error) {
            this.logger.error('❌ 分析标题失败:', error);
            return [];
        }
    }

    /**
     * 分析页面图片
     */
    async analyzeImages() {
        try {
            const images = [];
            const elements = await this.page.locator('img').all();
            
            for (const element of elements) {
                try {
                    const isVisible = await element.isVisible();
                    if (isVisible) {
                        const src = await element.getAttribute('src') || '';
                        const alt = await element.getAttribute('alt') || '';
                        const title = await element.getAttribute('title') || '';
                        
                        images.push({
                            type: 'image',
                            src: src,
                            alt: alt,
                            title: title,
                            locator: element
                        });
                    }
                } catch (e) {
                    // 忽略单个元素的错误
                }
            }

            return images;
        } catch (error) {
            this.logger.error('❌ 分析图片失败:', error);
            return [];
        }
    }

    /**
     * 获取页面状态信息
     */
    async getPageState() {
        try {
            return {
                url: this.page.url(),
                title: await this.page.title(),
                isLoading: await this.page.locator('[aria-busy="true"], .loading, .spinner').count() > 0,
                hasErrors: await this.page.locator('.error, .alert-danger, [role="alert"]').count() > 0,
                hasModals: await this.page.locator('[role="dialog"], .modal').count() > 0,
                viewportSize: this.page.viewportSize(),
                cookies: await this.page.context().cookies(),
                localStorage: await this.page.evaluate(() => {
                    const storage = {};
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        storage[key] = localStorage.getItem(key);
                    }
                    return storage;
                })
            };
        } catch (error) {
            this.logger.error('❌ 获取页面状态失败:', error);
            return {};
        }
    }

    /**
     * 获取可访问性信息
     */
    async getAccessibilityInfo() {
        try {
            // 使用Playwright的可访问性快照功能
            const snapshot = await this.page.accessibility.snapshot();
            
            return {
                hasAccessibilityTree: !!snapshot,
                focusableElements: await this.page.locator('[tabindex], button, input, select, textarea, a[href]').count(),
                ariaLabels: await this.page.locator('[aria-label]').count(),
                headingStructure: await this.getHeadingStructure()
            };
        } catch (error) {
            this.logger.error('❌ 获取可访问性信息失败:', error);
            return {};
        }
    }

    /**
     * 获取标题结构
     */
    async getHeadingStructure() {
        try {
            const headings = [];
            const headingTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
            
            for (const tag of headingTags) {
                const count = await this.page.locator(tag).count();
                if (count > 0) {
                    headings.push({ level: tag, count: count });
                }
            }
            
            return headings;
        } catch (error) {
            return [];
        }
    }

    /**
     * 智能元素定位 - 使用Playwright的多种定位策略
     */
    async smartLocate(description) {
        try {
            this.logger.info(`🎯 使用Playwright智能定位: ${description}`);

            // 策略1: 通过文本内容定位
            let locator = this.page.getByText(description);
            if (await locator.count() > 0 && await locator.first().isVisible()) {
                return { locator: locator.first(), strategy: 'byText', confidence: 0.9 };
            }

            // 策略2: 通过角色定位
            const roleMapping = {
                '按钮': 'button',
                '链接': 'link',
                '输入框': 'textbox',
                '搜索框': 'searchbox'
            };

            for (const [keyword, role] of Object.entries(roleMapping)) {
                if (description.includes(keyword)) {
                    locator = this.page.getByRole(role);
                    if (await locator.count() > 0 && await locator.first().isVisible()) {
                        return { locator: locator.first(), strategy: 'byRole', confidence: 0.8 };
                    }
                }
            }

            // 策略3: 通过占位符定位
            locator = this.page.getByPlaceholder(description);
            if (await locator.count() > 0 && await locator.first().isVisible()) {
                return { locator: locator.first(), strategy: 'byPlaceholder', confidence: 0.8 };
            }

            // 策略4: 通过标签定位
            locator = this.page.getByLabel(description);
            if (await locator.count() > 0 && await locator.first().isVisible()) {
                return { locator: locator.first(), strategy: 'byLabel', confidence: 0.8 };
            }

            // 策略5: 通过测试ID定位
            locator = this.page.getByTestId(description);
            if (await locator.count() > 0 && await locator.first().isVisible()) {
                return { locator: locator.first(), strategy: 'byTestId', confidence: 0.9 };
            }

            // 策略6: 通过标题定位
            locator = this.page.getByTitle(description);
            if (await locator.count() > 0 && await locator.first().isVisible()) {
                return { locator: locator.first(), strategy: 'byTitle', confidence: 0.7 };
            }

            return null;
        } catch (error) {
            this.logger.error('❌ Playwright智能定位失败:', error);
            return null;
        }
    }

    /**
     * 等待页面稳定
     */
    async waitForPageStable() {
        try {
            // 等待网络空闲
            await this.page.waitForLoadState('networkidle');
            
            // 等待DOM内容加载完成
            await this.page.waitForLoadState('domcontentloaded');
            
            // 额外等待确保动态内容加载完成
            await this.page.waitForTimeout(1000);
            
            return true;
        } catch (error) {
            this.logger.warn('⚠️ 等待页面稳定失败:', error);
            return false;
        }
    }

    /**
     * 截图并返回base64
     */
    async captureScreenshot(options = {}) {
        try {
            const screenshot = await this.page.screenshot({
                fullPage: options.fullPage || false,
                type: 'png',
                ...options
            });
            
            return {
                data: screenshot.toString('base64'),
                timestamp: new Date().toISOString(),
                url: this.page.url(),
                title: await this.page.title()
            };
        } catch (error) {
            this.logger.error('❌ 截图失败:', error);
            return null;
        }
    }
}

module.exports = PlaywrightAnalyzer;
