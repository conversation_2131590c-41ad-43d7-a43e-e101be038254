/**
 * Playwright增强执行器 - 充分利用Playwright的原生操作能力
 * 使用Playwright的内置方法进行页面操作和自动化
 */

const PlaywrightAnalyzer = require('./playwright-analyzer');

class PlaywrightExecutor {
    constructor(page, logger = console) {
        this.page = page;
        this.logger = logger;
        this.analyzer = new PlaywrightAnalyzer(page, logger);
    }

    /**
     * 执行操作 - 使用Playwright原生能力
     */
    async executeAction(action) {
        try {
            this.logger.info(`🎬 执行Playwright原生操作: ${action.action_type}`);
            
            let result = {
                action: action,
                success: false,
                message: '',
                timestamp: new Date().toISOString(),
                playwrightMethod: ''
            };

            switch (action.action_type) {
                case 'navigate':
                    result = await this.executeNavigate(action, result);
                    break;
                
                case 'click':
                    result = await this.executeClick(action, result);
                    break;
                
                case 'type':
                    result = await this.executeType(action, result);
                    break;
                
                case 'fill':
                    result = await this.executeFill(action, result);
                    break;
                
                case 'select':
                    result = await this.executeSelect(action, result);
                    break;
                
                case 'hover':
                    result = await this.executeHover(action, result);
                    break;
                
                case 'scroll':
                    result = await this.executeScroll(action, result);
                    break;
                
                case 'wait':
                    result = await this.executeWait(action, result);
                    break;
                
                case 'screenshot':
                    result = await this.executeScreenshot(action, result);
                    break;
                
                case 'press_key':
                    result = await this.executePressKey(action, result);
                    break;
                
                case 'drag_and_drop':
                    result = await this.executeDragAndDrop(action, result);
                    break;
                
                default:
                    throw new Error(`未支持的操作类型: ${action.action_type}`);
            }

            return result;
        } catch (error) {
            this.logger.error(`❌ Playwright操作执行失败: ${action.action_type}`, error);
            return {
                action: action,
                success: false,
                message: error.message,
                timestamp: new Date().toISOString(),
                error: error
            };
        }
    }

    /**
     * 执行导航操作
     */
    async executeNavigate(action, result) {
        const url = action.action_data.url;
        
        await this.page.goto(url, {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });
        
        // 等待页面稳定
        await this.analyzer.waitForPageStable();
        
        result.success = true;
        result.message = `成功导航到: ${url}`;
        result.playwrightMethod = 'page.goto()';
        
        return result;
    }

    /**
     * 执行点击操作
     */
    async executeClick(action, result) {
        const locateResult = await this.locateElement(action.target_element);
        
        if (!locateResult) {
            throw new Error(`无法定位元素: ${action.target_element.description}`);
        }

        const locator = locateResult.locator;
        
        // 确保元素可见和可点击
        await locator.waitFor({ state: 'visible', timeout: 10000 });
        await locator.scrollIntoViewIfNeeded();
        
        // 执行点击
        await locator.click({
            timeout: 10000,
            force: action.action_data.force || false
        });
        
        result.success = true;
        result.message = `成功点击: ${action.target_element.description}`;
        result.playwrightMethod = 'locator.click()';
        result.locateStrategy = locateResult.strategy;
        
        return result;
    }

    /**
     * 执行输入操作
     */
    async executeType(action, result) {
        const locateResult = await this.locateElement(action.target_element);
        
        if (!locateResult) {
            throw new Error(`无法定位输入元素: ${action.target_element.description}`);
        }

        const locator = locateResult.locator;
        const text = action.action_data.text;
        
        // 确保元素可见和可编辑
        await locator.waitFor({ state: 'visible', timeout: 10000 });
        await locator.scrollIntoViewIfNeeded();
        
        // 清空并输入文本
        await locator.clear();
        await locator.type(text, {
            delay: action.action_data.delay || 100 // 模拟真实输入速度
        });
        
        result.success = true;
        result.message = `成功输入文本: ${text}`;
        result.playwrightMethod = 'locator.type()';
        result.locateStrategy = locateResult.strategy;
        
        return result;
    }

    /**
     * 执行填充操作（更快的输入方式）
     */
    async executeFill(action, result) {
        const locateResult = await this.locateElement(action.target_element);
        
        if (!locateResult) {
            throw new Error(`无法定位输入元素: ${action.target_element.description}`);
        }

        const locator = locateResult.locator;
        const text = action.action_data.text;
        
        // 确保元素可见
        await locator.waitFor({ state: 'visible', timeout: 10000 });
        await locator.scrollIntoViewIfNeeded();
        
        // 快速填充文本
        await locator.fill(text);
        
        result.success = true;
        result.message = `成功填充文本: ${text}`;
        result.playwrightMethod = 'locator.fill()';
        result.locateStrategy = locateResult.strategy;
        
        return result;
    }

    /**
     * 执行选择操作
     */
    async executeSelect(action, result) {
        const locateResult = await this.locateElement(action.target_element);
        
        if (!locateResult) {
            throw new Error(`无法定位选择元素: ${action.target_element.description}`);
        }

        const locator = locateResult.locator;
        const value = action.action_data.value;
        
        await locator.waitFor({ state: 'visible', timeout: 10000 });
        await locator.selectOption(value);
        
        result.success = true;
        result.message = `成功选择选项: ${value}`;
        result.playwrightMethod = 'locator.selectOption()';
        result.locateStrategy = locateResult.strategy;
        
        return result;
    }

    /**
     * 执行悬停操作
     */
    async executeHover(action, result) {
        const locateResult = await this.locateElement(action.target_element);
        
        if (!locateResult) {
            throw new Error(`无法定位悬停元素: ${action.target_element.description}`);
        }

        const locator = locateResult.locator;
        
        await locator.waitFor({ state: 'visible', timeout: 10000 });
        await locator.hover();
        
        result.success = true;
        result.message = `成功悬停: ${action.target_element.description}`;
        result.playwrightMethod = 'locator.hover()';
        result.locateStrategy = locateResult.strategy;
        
        return result;
    }

    /**
     * 执行滚动操作
     */
    async executeScroll(action, result) {
        const scrollData = action.action_data;
        
        if (scrollData.element) {
            // 滚动到特定元素
            const locateResult = await this.locateElement(scrollData.element);
            if (locateResult) {
                await locateResult.locator.scrollIntoViewIfNeeded();
                result.message = `成功滚动到元素: ${scrollData.element.description}`;
            }
        } else {
            // 页面滚动
            const x = scrollData.x || 0;
            const y = scrollData.y || 300;
            
            await this.page.mouse.wheel(x, y);
            result.message = `成功滚动页面: x=${x}, y=${y}`;
        }
        
        result.success = true;
        result.playwrightMethod = 'page.mouse.wheel() / locator.scrollIntoViewIfNeeded()';
        
        return result;
    }

    /**
     * 执行等待操作
     */
    async executeWait(action, result) {
        const waitData = action.action_data;
        
        if (waitData.element) {
            // 等待元素
            const locateResult = await this.locateElement(waitData.element);
            if (locateResult) {
                await locateResult.locator.waitFor({
                    state: waitData.state || 'visible',
                    timeout: waitData.timeout || 10000
                });
                result.message = `成功等待元素: ${waitData.element.description}`;
            }
        } else if (waitData.url) {
            // 等待URL变化
            await this.page.waitForURL(waitData.url, {
                timeout: waitData.timeout || 10000
            });
            result.message = `成功等待URL: ${waitData.url}`;
        } else if (waitData.selector) {
            // 等待选择器
            await this.page.waitForSelector(waitData.selector, {
                state: waitData.state || 'visible',
                timeout: waitData.timeout || 10000
            });
            result.message = `成功等待选择器: ${waitData.selector}`;
        } else {
            // 简单等待时间
            const timeout = waitData.timeout || 3000;
            await this.page.waitForTimeout(timeout);
            result.message = `等待完成: ${timeout}ms`;
        }
        
        result.success = true;
        result.playwrightMethod = 'page.waitFor*()';
        
        return result;
    }

    /**
     * 执行截图操作
     */
    async executeScreenshot(action, result) {
        const screenshotData = action.action_data;
        
        const screenshot = await this.analyzer.captureScreenshot({
            fullPage: screenshotData.fullPage || false,
            clip: screenshotData.clip
        });
        
        result.success = true;
        result.message = '截图完成';
        result.playwrightMethod = 'page.screenshot()';
        result.screenshot = screenshot;
        
        return result;
    }

    /**
     * 执行按键操作
     */
    async executePressKey(action, result) {
        const key = action.action_data.key;
        
        if (action.target_element) {
            // 在特定元素上按键
            const locateResult = await this.locateElement(action.target_element);
            if (locateResult) {
                await locateResult.locator.press(key);
                result.message = `在元素上按键: ${key}`;
            }
        } else {
            // 全局按键
            await this.page.keyboard.press(key);
            result.message = `全局按键: ${key}`;
        }
        
        result.success = true;
        result.playwrightMethod = 'locator.press() / page.keyboard.press()';
        
        return result;
    }

    /**
     * 执行拖拽操作
     */
    async executeDragAndDrop(action, result) {
        const sourceResult = await this.locateElement(action.action_data.source);
        const targetResult = await this.locateElement(action.action_data.target);
        
        if (!sourceResult || !targetResult) {
            throw new Error('无法定位拖拽的源元素或目标元素');
        }
        
        await sourceResult.locator.dragTo(targetResult.locator);
        
        result.success = true;
        result.message = `成功拖拽: ${action.action_data.source.description} -> ${action.action_data.target.description}`;
        result.playwrightMethod = 'locator.dragTo()';
        
        return result;
    }

    /**
     * 智能元素定位
     */
    async locateElement(targetElement) {
        try {
            // 如果有具体的选择器，优先使用
            if (targetElement.selector) {
                const locator = this.page.locator(targetElement.selector);
                if (await locator.count() > 0 && await locator.first().isVisible()) {
                    return {
                        locator: locator.first(),
                        strategy: 'selector',
                        confidence: 0.9
                    };
                }
            }

            // 使用智能定位
            const smartResult = await this.analyzer.smartLocate(
                targetElement.description || targetElement.text || targetElement.placeholder
            );
            
            if (smartResult) {
                return smartResult;
            }

            return null;
        } catch (error) {
            this.logger.error('❌ 元素定位失败:', error);
            return null;
        }
    }

    /**
     * 验证操作结果
     */
    async verifyAction(action, result) {
        try {
            // 等待页面稳定
            await this.page.waitForTimeout(1000);

            const verification = {
                isValid: result.success,
                hasError: !result.success,
                confidence: result.success ? 0.8 : 0.2,
                message: result.success ? '操作验证通过' : '操作执行失败'
            };

            // 根据操作类型进行特定验证
            switch (action.action_type) {
                case 'navigate':
                    const currentUrl = this.page.url();
                    const expectedUrl = action.action_data.url;
                    verification.isValid = currentUrl.includes(expectedUrl) || expectedUrl.includes(currentUrl);
                    verification.confidence = verification.isValid ? 0.9 : 0.1;
                    break;
                
                case 'type':
                case 'fill':
                    // 验证输入是否成功
                    if (result.success && action.target_element) {
                        try {
                            const locateResult = await this.locateElement(action.target_element);
                            if (locateResult) {
                                const currentValue = await locateResult.locator.inputValue();
                                verification.isValid = currentValue === action.action_data.text;
                                verification.confidence = verification.isValid ? 0.9 : 0.5;
                            }
                        } catch (e) {
                            // 如果无法验证，保持原有结果
                        }
                    }
                    break;
            }

            return verification;
        } catch (error) {
            return {
                isValid: false,
                hasError: true,
                error: error.message,
                confidence: 0.1
            };
        }
    }

    /**
     * 获取页面分析结果
     */
    async getPageAnalysis() {
        return await this.analyzer.analyzePage();
    }

    /**
     * 等待页面稳定
     */
    async waitForPageStable() {
        return await this.analyzer.waitForPageStable();
    }
}

module.exports = PlaywrightExecutor;
