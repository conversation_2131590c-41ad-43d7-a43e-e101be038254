/**
 * 页面分析器 - AI-First RPA架构的感知层
 * 负责页面状态感知、截图分析、DOM解析
 */

class PageAnalyzer {
    constructor(page, logger = console) {
        this.page = page;
        this.logger = logger;
    }

    /**
     * 分析页面状态
     */
    async analyzePageState(pageState) {
        try {
            this.logger.info('🔍 开始分析页面状态...');

            const analysis = {
                url: pageState.url,
                title: pageState.title,
                timestamp: pageState.timestamp,
                summary: '',
                interactive: [],
                forms: [],
                navigation: [],
                content: [],
                confidence: 0.8
            };

            // 分析可交互元素
            analysis.interactive = await this.analyzeInteractiveElements();
            
            // 分析表单元素
            analysis.forms = await this.analyzeForms();
            
            // 分析导航元素
            analysis.navigation = await this.analyzeNavigation();
            
            // 分析页面内容
            analysis.content = await this.analyzeContent();
            
            // 生成页面摘要
            analysis.summary = this.generatePageSummary(analysis);
            
            this.logger.info(`✅ 页面分析完成，发现 ${analysis.interactive.length} 个可交互元素`);
            
            return analysis;
        } catch (error) {
            this.logger.error('❌ 页面分析失败:', error);
            return {
                url: pageState.url || 'unknown',
                title: pageState.title || 'unknown',
                summary: '页面分析失败',
                interactive: [],
                forms: [],
                navigation: [],
                content: [],
                confidence: 0.1,
                error: error.message
            };
        }
    }

    /**
     * 分析可交互元素
     */
    async analyzeInteractiveElements() {
        try {
            const elements = [];

            // 分析按钮
            const buttons = await this.page.$$('button, input[type="button"], input[type="submit"], .btn, [role="button"]');
            for (const button of buttons) {
                try {
                    const isVisible = await button.isVisible();
                    if (isVisible) {
                        const text = await button.textContent();
                        const tagName = await button.evaluate(el => el.tagName.toLowerCase());
                        const className = await button.getAttribute('class');
                        
                        elements.push({
                            type: 'button',
                            text: text?.trim() || '',
                            tagName,
                            className,
                            selector: await this.generateSelector(button)
                        });
                    }
                } catch (e) {
                    // 忽略单个元素的错误
                }
            }

            // 分析链接
            const links = await this.page.$$('a[href]');
            for (const link of links) {
                try {
                    const isVisible = await link.isVisible();
                    if (isVisible) {
                        const text = await link.textContent();
                        const href = await link.getAttribute('href');
                        
                        elements.push({
                            type: 'link',
                            text: text?.trim() || '',
                            href,
                            selector: await this.generateSelector(link)
                        });
                    }
                } catch (e) {
                    // 忽略单个元素的错误
                }
            }

            // 分析输入框
            const inputs = await this.page.$$('input, textarea, select');
            for (const input of inputs) {
                try {
                    const isVisible = await input.isVisible();
                    if (isVisible) {
                        const type = await input.getAttribute('type') || 'text';
                        const placeholder = await input.getAttribute('placeholder');
                        const name = await input.getAttribute('name');
                        
                        elements.push({
                            type: 'input',
                            inputType: type,
                            placeholder: placeholder || '',
                            name: name || '',
                            selector: await this.generateSelector(input)
                        });
                    }
                } catch (e) {
                    // 忽略单个元素的错误
                }
            }

            return elements.slice(0, 20); // 限制返回数量
        } catch (error) {
            this.logger.error('❌ 分析可交互元素失败:', error);
            return [];
        }
    }

    /**
     * 分析表单
     */
    async analyzeForms() {
        try {
            const forms = [];
            const formElements = await this.page.$$('form');

            for (const form of formElements) {
                try {
                    const isVisible = await form.isVisible();
                    if (isVisible) {
                        const action = await form.getAttribute('action');
                        const method = await form.getAttribute('method');
                        const inputs = await form.$$('input, textarea, select');
                        
                        const formData = {
                            action: action || '',
                            method: method || 'get',
                            inputs: [],
                            selector: await this.generateSelector(form)
                        };

                        for (const input of inputs) {
                            const type = await input.getAttribute('type') || 'text';
                            const name = await input.getAttribute('name');
                            const placeholder = await input.getAttribute('placeholder');
                            
                            formData.inputs.push({
                                type,
                                name: name || '',
                                placeholder: placeholder || ''
                            });
                        }

                        forms.push(formData);
                    }
                } catch (e) {
                    // 忽略单个表单的错误
                }
            }

            return forms;
        } catch (error) {
            this.logger.error('❌ 分析表单失败:', error);
            return [];
        }
    }

    /**
     * 分析导航元素
     */
    async analyzeNavigation() {
        try {
            const navigation = [];

            // 分析导航菜单
            const navElements = await this.page.$$('nav, .nav, .navbar, .menu, [role="navigation"]');
            for (const nav of navElements) {
                try {
                    const isVisible = await nav.isVisible();
                    if (isVisible) {
                        const links = await nav.$$('a');
                        const navItems = [];

                        for (const link of links) {
                            const text = await link.textContent();
                            const href = await link.getAttribute('href');
                            
                            navItems.push({
                                text: text?.trim() || '',
                                href: href || ''
                            });
                        }

                        navigation.push({
                            type: 'navigation',
                            items: navItems,
                            selector: await this.generateSelector(nav)
                        });
                    }
                } catch (e) {
                    // 忽略单个导航的错误
                }
            }

            return navigation;
        } catch (error) {
            this.logger.error('❌ 分析导航失败:', error);
            return [];
        }
    }

    /**
     * 分析页面内容
     */
    async analyzeContent() {
        try {
            const content = [];

            // 分析标题
            const headings = await this.page.$$('h1, h2, h3, h4, h5, h6');
            for (const heading of headings) {
                try {
                    const isVisible = await heading.isVisible();
                    if (isVisible) {
                        const text = await heading.textContent();
                        const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
                        
                        content.push({
                            type: 'heading',
                            level: tagName,
                            text: text?.trim() || ''
                        });
                    }
                } catch (e) {
                    // 忽略单个标题的错误
                }
            }

            // 分析重要文本
            const textElements = await this.page.$$('p, .text, .content, .description');
            for (const element of textElements.slice(0, 10)) { // 限制数量
                try {
                    const isVisible = await element.isVisible();
                    if (isVisible) {
                        const text = await element.textContent();
                        if (text && text.trim().length > 10) {
                            content.push({
                                type: 'text',
                                text: text.trim().substring(0, 200) // 限制长度
                            });
                        }
                    }
                } catch (e) {
                    // 忽略单个文本的错误
                }
            }

            return content;
        } catch (error) {
            this.logger.error('❌ 分析页面内容失败:', error);
            return [];
        }
    }

    /**
     * 生成元素选择器
     */
    async generateSelector(element) {
        try {
            // 尝试获取ID
            const id = await element.getAttribute('id');
            if (id) {
                return `#${id}`;
            }

            // 尝试获取唯一的class组合
            const className = await element.getAttribute('class');
            if (className) {
                const classes = className.split(' ').filter(c => c.trim());
                if (classes.length > 0) {
                    return `.${classes.join('.')}`;
                }
            }

            // 尝试获取name属性
            const name = await element.getAttribute('name');
            if (name) {
                return `[name="${name}"]`;
            }

            // 使用标签名作为后备
            const tagName = await element.evaluate(el => el.tagName.toLowerCase());
            return tagName;
        } catch (error) {
            return 'unknown';
        }
    }

    /**
     * 生成页面摘要
     */
    generatePageSummary(analysis) {
        const parts = [];
        
        parts.push(`页面标题: ${analysis.title}`);
        parts.push(`URL: ${analysis.url}`);
        
        if (analysis.interactive.length > 0) {
            const buttons = analysis.interactive.filter(el => el.type === 'button');
            const links = analysis.interactive.filter(el => el.type === 'link');
            const inputs = analysis.interactive.filter(el => el.type === 'input');
            
            if (buttons.length > 0) {
                parts.push(`发现 ${buttons.length} 个按钮`);
            }
            if (links.length > 0) {
                parts.push(`发现 ${links.length} 个链接`);
            }
            if (inputs.length > 0) {
                parts.push(`发现 ${inputs.length} 个输入框`);
            }
        }
        
        if (analysis.forms.length > 0) {
            parts.push(`发现 ${analysis.forms.length} 个表单`);
        }
        
        if (analysis.navigation.length > 0) {
            parts.push(`发现 ${analysis.navigation.length} 个导航菜单`);
        }

        return parts.join(', ');
    }

    /**
     * 获取页面截图
     */
    async captureScreenshot(options = {}) {
        try {
            const screenshot = await this.page.screenshot({
                fullPage: options.fullPage || false,
                type: options.type || 'png',
                quality: options.quality || 80
            });
            
            return {
                data: screenshot.toString('base64'),
                timestamp: new Date().toISOString(),
                url: this.page.url(),
                title: await this.page.title()
            };
        } catch (error) {
            this.logger.error('❌ 截图失败:', error);
            return null;
        }
    }

    /**
     * 等待页面稳定
     */
    async waitForPageStable(timeout = 5000) {
        try {
            // 等待网络空闲
            await this.page.waitForLoadState('networkidle', { timeout });
            
            // 额外等待确保页面完全稳定
            await this.page.waitForTimeout(1000);
            
            return true;
        } catch (error) {
            this.logger.warn('⚠️ 等待页面稳定超时:', error.message);
            return false;
        }
    }
}

module.exports = PageAnalyzer;
