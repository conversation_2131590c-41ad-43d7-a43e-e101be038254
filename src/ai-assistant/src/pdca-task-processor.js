/**
 * PDCA任务处理器 - 替换现有的复杂验证系统
 * 使用Plan-Do-Check-Act循环处理RPA任务
 */

const PDCACycleManager = require('./pdca/pdca-cycle-manager');
const { MCPClientV2 } = require('./playwright-mcp/mcp-client-v2');
const SimpleLLMClient = require('./pdca/simple-llm-client');
const logger = require('./utils/logger');

class PDCATaskProcessor {
    constructor() {
        this.mcpClient = new MCPClientV2();
        this.llmClient = new SimpleLLMClient();
        this.pdcaManager = new PDCACycleManager(this.llmClient, this.mcpClient);
        
        this.isProcessing = false;
        this.currentTicket = null;
        this.processingQueue = [];
    }

    /**
     * 处理工单
     */
    async processTicket(ticket) {
        if (this.isProcessing) {
            logger.info('PDCA处理器忙碌中，工单加入队列:', ticket.id);
            this.processingQueue.push(ticket);
            return;
        }

        this.isProcessing = true;
        this.currentTicket = ticket;

        logger.info(`🔄 PDCA处理器开始处理工单: ${ticket.id} - ${ticket.title}`);

        try {
            // 更新工单状态为处理中
            await this.updateTicketStatus(ticket.id, '处理中');

            // 初始化浏览器环境
            await this.initializeBrowser();

            // 执行PDCA循环
            const result = await this.pdcaManager.executePDCACycle(
                this.buildTaskGoal(ticket),
                this.buildInitialContext(ticket)
            );

            // 处理执行结果
            await this.handleExecutionResult(ticket, result);

        } catch (error) {
            logger.error(`❌ PDCA处理失败: ${ticket.id}`, error);
            await this.handleProcessingError(ticket, error);
        } finally {
            this.isProcessing = false;
            this.currentTicket = null;
            
            // 处理队列中的下一个工单
            await this.processNextInQueue();
        }
    }

    /**
     * 构建任务目标
     */
    buildTaskGoal(ticket) {
        return `${ticket.title}\n\n详细要求：${ticket.content}`;
    }

    /**
     * 构建初始上下文
     */
    buildInitialContext(ticket) {
        return {
            ticket_id: ticket.id,
            ticket_number: ticket.ticket_number,
            priority: ticket.priority,
            type: ticket.type,
            created_at: ticket.created_at,
            current_state: null // 将在第一次页面快照时设置
        };
    }

    /**
     * 初始化浏览器环境
     */
    async initializeBrowser() {
        try {
            logger.info('🌐 初始化浏览器环境...');
            
            // 确保MCP连接正常
            await this.mcpClient.ensureConnection();
            
            // 导航到起始页面
            await this.mcpClient.callTool('browser_navigate', {
                url: 'https://uat-merchant.aomiapp.com/#/bdlogin'
            });
            
            // 等待页面加载
            await this.sleep(3000);
            
            logger.info('✅ 浏览器环境初始化完成');
            
        } catch (error) {
            logger.error('❌ 浏览器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 处理执行结果
     */
    async handleExecutionResult(ticket, result) {
        logger.info(`📊 处理执行结果: ${result.status}`, {
            ticketId: ticket.id,
            status: result.status
        });

        switch (result.status) {
            case 'COMPLETED':
                await this.handleTaskCompletion(ticket, result);
                break;
                
            case 'USER_INTERVENTION_REQUIRED':
                await this.handleUserIntervention(ticket, result);
                break;
                
            case 'ESCALATED':
                await this.handleEscalation(ticket, result);
                break;
                
            default:
                await this.handleUnknownResult(ticket, result);
        }
    }

    /**
     * 处理任务完成
     */
    async handleTaskCompletion(ticket, result) {
        logger.info(`🎉 任务完成: ${ticket.id}`);
        
        // 生成执行摘要
        const summary = this.generateExecutionSummary(result);
        
        // 更新工单状态
        await this.updateTicketStatus(ticket.id, '已完成', {
            summary: summary,
            execution_details: {
                total_cycles: result.summary?.totalCycles,
                duration: result.summary?.duration,
                stats: result.summary?.stats
            },
            completed_at: new Date().toISOString()
        });
        
        logger.info(`✅ 工单 ${ticket.id} 处理完成`);
    }

    /**
     * 处理用户干预请求
     */
    async handleUserIntervention(ticket, result) {
        logger.info(`🤝 需要用户干预: ${ticket.id}`);
        
        // 更新工单状态为等待补充信息
        await this.updateTicketStatus(ticket.id, '等待补充信息', {
            intervention_reason: result.reason,
            instructions: result.instructions,
            current_context: result.context,
            intervention_requested_at: new Date().toISOString()
        });
        
        // 发送用户通知（如果有WebSocket连接）
        this.notifyUserIntervention(ticket, result);
    }

    /**
     * 处理升级情况
     */
    async handleEscalation(ticket, result) {
        logger.warn(`⚠️ 任务升级: ${ticket.id}`);
        
        // 更新工单状态为处理失败
        await this.updateTicketStatus(ticket.id, '处理失败', {
            escalation_reason: result.reason,
            recommendation: result.recommendation,
            execution_context: result.context,
            escalated_at: new Date().toISOString()
        });
    }

    /**
     * 处理未知结果
     */
    async handleUnknownResult(ticket, result) {
        logger.warn(`❓ 未知执行结果: ${ticket.id}`, result);
        
        await this.updateTicketStatus(ticket.id, '处理失败', {
            error: '未知的执行结果',
            result: result,
            failed_at: new Date().toISOString()
        });
    }

    /**
     * 处理处理错误
     */
    async handleProcessingError(ticket, error) {
        logger.error(`💥 处理错误: ${ticket.id}`, error);
        
        await this.updateTicketStatus(ticket.id, '处理失败', {
            error: error.message,
            stack: error.stack,
            failed_at: new Date().toISOString()
        });
    }

    /**
     * 生成执行摘要
     */
    generateExecutionSummary(result) {
        const summary = result.summary || {};
        const context = result.context || {};
        
        const parts = [];
        parts.push(`任务目标: ${summary.taskGoal || '未知'}`);
        parts.push(`执行周期: ${summary.totalCycles || 0}次`);
        parts.push(`执行时长: ${this.formatDuration(summary.duration || 0)}`);
        
        if (summary.stats) {
            parts.push(`成功操作: ${summary.stats.successfulExecutions || 0}次`);
            parts.push(`失败操作: ${summary.stats.failedExecutions || 0}次`);
        }
        
        // 添加关键执行步骤
        if (context.execution_history && context.execution_history.length > 0) {
            parts.push('\n关键执行步骤:');
            context.execution_history.slice(-5).forEach((record, index) => {
                const intent = record.plan?.intent || '未知操作';
                const status = record.check_result?.status || '未知状态';
                parts.push(`${index + 1}. ${intent} - ${status}`);
            });
        }
        
        return parts.join('\n');
    }

    /**
     * 格式化持续时间
     */
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        
        if (minutes > 0) {
            return `${minutes}分${seconds % 60}秒`;
        }
        return `${seconds}秒`;
    }

    /**
     * 更新工单状态
     */
    async updateTicketStatus(ticketId, status, additionalData = {}) {
        try {
            const axios = require('axios');
            await axios.patch(`http://localhost:3001/api/tickets/${ticketId}/status`, {
                status: status,
                ...additionalData
            });
            
            logger.info(`✅ 工单状态更新: ${ticketId} -> ${status}`);
        } catch (error) {
            logger.error(`❌ 工单状态更新失败: ${ticketId}`, error);
        }
    }

    /**
     * 通知用户干预
     */
    notifyUserIntervention(ticket, result) {
        // 这里可以集成WebSocket通知或其他通知机制
        logger.info(`📢 用户干预通知: ${ticket.id} - ${result.reason}`);
    }

    /**
     * 处理队列中的下一个工单
     */
    async processNextInQueue() {
        if (this.processingQueue.length > 0) {
            const nextTicket = this.processingQueue.shift();
            logger.info(`📋 处理队列中的下一个工单: ${nextTicket.id}`);
            
            // 异步处理，避免阻塞
            setImmediate(() => {
                this.processTicket(nextTicket);
            });
        }
    }

    /**
     * 获取处理器状态
     */
    getStatus() {
        return {
            isProcessing: this.isProcessing,
            currentTicket: this.currentTicket ? {
                id: this.currentTicket.id,
                title: this.currentTicket.title
            } : null,
            queueSize: this.processingQueue.length,
            architecture: 'PDCA',
            agents: ['PlannerAgent', 'ExecutorAgent', 'CheckerAgent']
        };
    }

    /**
     * 获取队列大小
     */
    getQueueSize() {
        return this.processingQueue.length;
    }

    /**
     * 停止处理（用于挂起功能）
     */
    async stop() {
        if (this.isProcessing && this.currentTicket) {
            logger.info(`⏸️ 停止PDCA处理器，当前工单: ${this.currentTicket.id}`);
            
            // 这里可以实现更优雅的停止逻辑
            // 比如等待当前PDCA循环完成
            
            this.isProcessing = false;
            this.currentTicket = null;
        }
    }

    /**
     * 兼容性方法 - 为了与现有server.js接口兼容
     */
    getProcessedCount() {
        return 0; // PDCA架构暂时不统计处理数量
    }

    getSuccessCount() {
        return 0;
    }

    getFailureCount() {
        return 0;
    }

    /**
     * 辅助方法
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = PDCATaskProcessor;
