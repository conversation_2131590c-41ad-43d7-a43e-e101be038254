const fs = require('fs');
const path = require('path');

const LOG_LEVELS = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3
};

const currentLevel = LOG_LEVELS[process.env.LOG_LEVEL] || LOG_LEVELS.info;
const logDirectory = process.env.LOG_DIR || path.join(__dirname, '../../data/logs');
const logFilePath = path.join(logDirectory, 'ai-assistant.log');

// 确保日志目录存在
if (!fs.existsSync(logDirectory)) {
    fs.mkdirSync(logDirectory, { recursive: true });
}

// 创建可写流，追加模式
const logStream = fs.createWriteStream(logFilePath, { flags: 'a' });

function formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ') : '';
    
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${formattedArgs}`;
}

function writeToLog(level, message, ...args) {
    const formatted = formatMessage(level, message, ...args);
    console.log(formatted); // 仍然输出到控制台
    logStream.write(formatted + '\n'); // 写入文件
}

const logger = {
    error: (message, ...args) => {
        if (currentLevel >= LOG_LEVELS.error) {
            writeToLog('error', message, ...args);
        }
    },
    
    warn: (message, ...args) => {
        if (currentLevel >= LOG_LEVELS.warn) {
            writeToLog('warn', message, ...args);
        }
    },
    
    info: (message, ...args) => {
        if (currentLevel >= LOG_LEVELS.info) {
            writeToLog('info', message, ...args);
        }
    },
    
    debug: (message, ...args) => {
        if (currentLevel >= LOG_LEVELS.debug) {
            writeToLog('debug', message, ...args);
        }
    }
};

module.exports = logger;
