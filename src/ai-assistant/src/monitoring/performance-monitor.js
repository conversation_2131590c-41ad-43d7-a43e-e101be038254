/**
 * 性能监控器
 * 负责监控系统性能、Agent调用统计、操作成功率等
 */

const logger = require('../utils/logger');
const EventEmitter = require('events');

class PerformanceMonitor extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置
        this.config = {
            sampleInterval: options.sampleInterval || 1000,      // 采样间隔（毫秒）
            metricsRetention: options.metricsRetention || 3600,  // 指标保留时间（秒）
            enableSystemMetrics: options.enableSystemMetrics !== false,
            enableReporting: options.enableReporting !== false
        };
        
        // Agent性能指标
        this.agentMetrics = {
            orchestrator: this.createAgentMetric('OrchestratorAgent'),
            observer: this.createAgentMetric('ObserverAgent'),
            decision: this.createAgentMetric('DecisionAgent'),
            executor: this.createAgentMetric('ExecutorAgent'),
            validator: this.createAgentMetric('ValidatorAgent')
        };
        
        // 操作性能指标
        this.operationMetrics = new Map();
        
        // 系统性能指标
        this.systemMetrics = {
            cpu: [],
            memory: [],
            timestamp: []
        };
        
        // 实时统计
        this.realtimeStats = {
            activeOperations: 0,
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            peakResponseTime: 0,
            startTime: Date.now()
        };
        
        // 性能警告阈值
        this.alertThresholds = {
            responseTime: 5000,       // 响应时间超过5秒
            errorRate: 0.2,          // 错误率超过20%
            memoryUsage: 0.8,        // 内存使用超过80%
            queueSize: 100           // 队列大小超过100
        };
        
        // 性能报告定时器
        this.reportingInterval = null;
        
        // 启动监控
        if (this.config.enableReporting) {
            this.startMonitoring();
        }
    }

    /**
     * 创建Agent性能指标对象
     */
    createAgentMetric(agentName) {
        return {
            name: agentName,
            totalCalls: 0,
            successfulCalls: 0,
            failedCalls: 0,
            totalDuration: 0,
            averageDuration: 0,
            maxDuration: 0,
            minDuration: Infinity,
            lastCallTime: null,
            errorTypes: {},
            methodStats: {}
        };
    }

    /**
     * 记录Agent调用开始
     */
    recordAgentCallStart(agentName, method) {
        const callId = `${agentName}_${method}_${Date.now()}`;
        const startTime = Date.now();
        
        // 记录活跃操作
        this.realtimeStats.activeOperations++;
        this.realtimeStats.totalRequests++;
        
        // 创建操作记录
        this.operationMetrics.set(callId, {
            agentName,
            method,
            startTime,
            status: 'running'
        });
        
        return callId;
    }

    /**
     * 记录Agent调用结束
     */
    recordAgentCallEnd(callId, success = true, error = null) {
        const operation = this.operationMetrics.get(callId);
        if (!operation) {
            logger.warn(`未找到操作记录: ${callId}`);
            return;
        }
        
        const endTime = Date.now();
        const duration = endTime - operation.startTime;
        
        // 更新操作记录
        operation.endTime = endTime;
        operation.duration = duration;
        operation.success = success;
        operation.error = error;
        
        // 更新Agent指标
        const agentMetric = this.agentMetrics[operation.agentName.toLowerCase()];
        if (agentMetric) {
            this.updateAgentMetrics(agentMetric, operation.method, duration, success, error);
        }
        
        // 更新实时统计
        this.realtimeStats.activeOperations--;
        if (success) {
            this.realtimeStats.successfulRequests++;
        } else {
            this.realtimeStats.failedRequests++;
        }
        
        // 更新平均响应时间
        this.updateAverageResponseTime(duration);
        
        // 检查性能警告
        this.checkPerformanceAlerts(operation);
        
        // 清理旧的操作记录（防止内存泄漏）
        if (this.operationMetrics.size > 1000) {
            const oldestKey = this.operationMetrics.keys().next().value;
            this.operationMetrics.delete(oldestKey);
        }
    }

    /**
     * 更新Agent性能指标
     */
    updateAgentMetrics(agentMetric, method, duration, success, error) {
        agentMetric.totalCalls++;
        agentMetric.totalDuration += duration;
        agentMetric.lastCallTime = Date.now();
        
        if (success) {
            agentMetric.successfulCalls++;
        } else {
            agentMetric.failedCalls++;
            
            // 记录错误类型
            const errorType = error?.name || 'Unknown';
            agentMetric.errorTypes[errorType] = (agentMetric.errorTypes[errorType] || 0) + 1;
        }
        
        // 更新方法统计
        if (!agentMetric.methodStats[method]) {
            agentMetric.methodStats[method] = {
                calls: 0,
                totalDuration: 0,
                failures: 0
            };
        }
        
        agentMetric.methodStats[method].calls++;
        agentMetric.methodStats[method].totalDuration += duration;
        if (!success) {
            agentMetric.methodStats[method].failures++;
        }
        
        // 更新最大/最小/平均持续时间
        agentMetric.maxDuration = Math.max(agentMetric.maxDuration, duration);
        agentMetric.minDuration = Math.min(agentMetric.minDuration, duration);
        agentMetric.averageDuration = agentMetric.totalDuration / agentMetric.totalCalls;
    }

    /**
     * 更新平均响应时间
     */
    updateAverageResponseTime(duration) {
        const totalOps = this.realtimeStats.successfulRequests + this.realtimeStats.failedRequests;
        if (totalOps > 0) {
            this.realtimeStats.averageResponseTime = 
                (this.realtimeStats.averageResponseTime * (totalOps - 1) + duration) / totalOps;
        }
        
        this.realtimeStats.peakResponseTime = Math.max(
            this.realtimeStats.peakResponseTime, 
            duration
        );
    }

    /**
     * 检查性能警告
     */
    checkPerformanceAlerts(operation) {
        // 检查响应时间
        if (operation.duration > this.alertThresholds.responseTime) {
            this.emit('performance:alert', {
                type: 'SLOW_RESPONSE',
                agent: operation.agentName,
                method: operation.method,
                duration: operation.duration,
                threshold: this.alertThresholds.responseTime
            });
        }
        
        // 检查错误率
        const errorRate = this.getErrorRate();
        if (errorRate > this.alertThresholds.errorRate) {
            this.emit('performance:alert', {
                type: 'HIGH_ERROR_RATE',
                errorRate: errorRate,
                threshold: this.alertThresholds.errorRate
            });
        }
    }

    /**
     * 启动监控
     */
    startMonitoring() {
        // 定期收集系统指标
        if (this.config.enableSystemMetrics) {
            this.systemMetricsInterval = setInterval(() => {
                this.collectSystemMetrics();
            }, this.config.sampleInterval);
        }
        
        // 定期生成报告
        this.reportingInterval = setInterval(() => {
            this.generatePerformanceReport();
        }, 60000); // 每分钟生成一次报告
        
        logger.info('性能监控已启动');
    }

    /**
     * 收集系统指标
     */
    collectSystemMetrics() {
        const memoryUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        const timestamp = Date.now();
        
        // 记录内存使用
        this.systemMetrics.memory.push({
            timestamp,
            heapUsed: memoryUsage.heapUsed,
            heapTotal: memoryUsage.heapTotal,
            rss: memoryUsage.rss,
            external: memoryUsage.external
        });
        
        // 记录CPU使用
        this.systemMetrics.cpu.push({
            timestamp,
            user: cpuUsage.user,
            system: cpuUsage.system
        });
        
        // 清理旧数据
        this.cleanupOldMetrics();
        
        // 检查内存警告
        const heapUsageRatio = memoryUsage.heapUsed / memoryUsage.heapTotal;
        if (heapUsageRatio > this.alertThresholds.memoryUsage) {
            this.emit('performance:alert', {
                type: 'HIGH_MEMORY_USAGE',
                usage: heapUsageRatio,
                threshold: this.alertThresholds.memoryUsage
            });
        }
    }

    /**
     * 清理旧的指标数据
     */
    cleanupOldMetrics() {
        const cutoffTime = Date.now() - (this.config.metricsRetention * 1000);
        
        // 清理系统指标
        this.systemMetrics.memory = this.systemMetrics.memory.filter(
            m => m.timestamp > cutoffTime
        );
        this.systemMetrics.cpu = this.systemMetrics.cpu.filter(
            m => m.timestamp > cutoffTime
        );
    }

    /**
     * 生成性能报告
     */
    generatePerformanceReport() {
        const report = {
            timestamp: new Date().toISOString(),
            uptime: Date.now() - this.realtimeStats.startTime,
            summary: {
                totalRequests: this.realtimeStats.totalRequests,
                successfulRequests: this.realtimeStats.successfulRequests,
                failedRequests: this.realtimeStats.failedRequests,
                successRate: this.getSuccessRate(),
                errorRate: this.getErrorRate(),
                averageResponseTime: Math.round(this.realtimeStats.averageResponseTime),
                peakResponseTime: this.realtimeStats.peakResponseTime,
                activeOperations: this.realtimeStats.activeOperations
            },
            agentPerformance: this.getAgentPerformanceSummary(),
            topErrors: this.getTopErrors(),
            recommendations: this.generateRecommendations()
        };
        
        // 发出报告事件
        this.emit('performance:report', report);
        
        // 记录到日志
        logger.info('性能报告', report);
        
        return report;
    }

    /**
     * 获取成功率
     */
    getSuccessRate() {
        const total = this.realtimeStats.successfulRequests + this.realtimeStats.failedRequests;
        return total > 0 ? this.realtimeStats.successfulRequests / total : 0;
    }

    /**
     * 获取错误率
     */
    getErrorRate() {
        const total = this.realtimeStats.successfulRequests + this.realtimeStats.failedRequests;
        return total > 0 ? this.realtimeStats.failedRequests / total : 0;
    }

    /**
     * 获取Agent性能摘要
     */
    getAgentPerformanceSummary() {
        const summary = {};
        
        for (const [agentKey, metric] of Object.entries(this.agentMetrics)) {
            if (metric.totalCalls > 0) {
                summary[agentKey] = {
                    totalCalls: metric.totalCalls,
                    successRate: metric.successfulCalls / metric.totalCalls,
                    averageDuration: Math.round(metric.averageDuration),
                    maxDuration: metric.maxDuration,
                    topMethods: this.getTopMethods(metric.methodStats)
                };
            }
        }
        
        return summary;
    }

    /**
     * 获取使用最多的方法
     */
    getTopMethods(methodStats) {
        return Object.entries(methodStats)
            .sort((a, b) => b[1].calls - a[1].calls)
            .slice(0, 3)
            .map(([method, stats]) => ({
                method,
                calls: stats.calls,
                averageDuration: Math.round(stats.totalDuration / stats.calls),
                failureRate: stats.failures / stats.calls
            }));
    }

    /**
     * 获取最常见的错误
     */
    getTopErrors() {
        const allErrors = {};
        
        for (const metric of Object.values(this.agentMetrics)) {
            for (const [errorType, count] of Object.entries(metric.errorTypes)) {
                allErrors[errorType] = (allErrors[errorType] || 0) + count;
            }
        }
        
        return Object.entries(allErrors)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([type, count]) => ({ type, count }));
    }

    /**
     * 生成优化建议
     */
    generateRecommendations() {
        const recommendations = [];
        
        // 检查响应时间
        if (this.realtimeStats.averageResponseTime > 3000) {
            recommendations.push({
                type: 'OPTIMIZE_RESPONSE_TIME',
                message: '平均响应时间较高，建议优化Agent处理逻辑'
            });
        }
        
        // 检查错误率
        const errorRate = this.getErrorRate();
        if (errorRate > 0.1) {
            recommendations.push({
                type: 'REDUCE_ERROR_RATE',
                message: '错误率偏高，建议增强错误处理机制'
            });
        }
        
        // 检查特定Agent性能
        for (const [agentKey, metric] of Object.entries(this.agentMetrics)) {
            if (metric.averageDuration > 5000) {
                recommendations.push({
                    type: 'OPTIMIZE_AGENT',
                    agent: agentKey,
                    message: `${metric.name}平均执行时间过长`
                });
            }
        }
        
        return recommendations;
    }

    /**
     * 获取实时仪表板数据
     */
    getDashboardData() {
        return {
            realtime: this.realtimeStats,
            agents: this.getAgentPerformanceSummary(),
            system: this.getLatestSystemMetrics(),
            alerts: this.getActiveAlerts()
        };
    }

    /**
     * 获取最新系统指标
     */
    getLatestSystemMetrics() {
        const latestMemory = this.systemMetrics.memory[this.systemMetrics.memory.length - 1];
        const latestCpu = this.systemMetrics.cpu[this.systemMetrics.cpu.length - 1];
        
        return {
            memory: latestMemory ? {
                heapUsed: Math.round(latestMemory.heapUsed / 1024 / 1024), // MB
                heapTotal: Math.round(latestMemory.heapTotal / 1024 / 1024),
                usage: latestMemory.heapUsed / latestMemory.heapTotal
            } : null,
            cpu: latestCpu
        };
    }

    /**
     * 获取活跃警告
     */
    getActiveAlerts() {
        const alerts = [];
        
        // 这里可以维护一个活跃警告列表
        // 目前返回空数组，实际使用时可以扩展
        
        return alerts;
    }

    /**
     * 停止监控
     */
    stopMonitoring() {
        if (this.systemMetricsInterval) {
            clearInterval(this.systemMetricsInterval);
        }
        
        if (this.reportingInterval) {
            clearInterval(this.reportingInterval);
        }
        
        logger.info('性能监控已停止');
    }

    /**
     * 重置所有指标
     */
    reset() {
        // 重置Agent指标
        for (const metric of Object.values(this.agentMetrics)) {
            Object.assign(metric, this.createAgentMetric(metric.name));
        }
        
        // 清空操作指标
        this.operationMetrics.clear();
        
        // 重置系统指标
        this.systemMetrics = {
            cpu: [],
            memory: [],
            timestamp: []
        };
        
        // 重置实时统计
        this.realtimeStats = {
            activeOperations: 0,
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            peakResponseTime: 0,
            startTime: Date.now()
        };
        
        logger.info('性能监控指标已重置');
    }
}

module.exports = PerformanceMonitor;