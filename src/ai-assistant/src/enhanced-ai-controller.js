/**
 * 增强的AI助手主控制器
 * 实现完整的工单处理闭环，包括人工辅助登录
 */

const { MCPClientV2 } = require('./playwright-mcp/mcp-client-v2');
const RealMCPExecutor = require('./execution/real-mcp-executor');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

class EnhancedAIController extends EventEmitter {
    constructor(options = {}) {
        super();
        this.workOrderSystemUrl = options.workOrderSystemUrl || 'http://localhost:3001';
        this.mcpClient = null;
        this.mcpExecutor = null;

        // 配置 - 必须在createLogger之前初始化
        this.config = {
            pollInterval: 5000, // 5秒轮询一次
            maxRetries: 3,
            humanAssistanceTimeout: 300000, // 5分钟人工辅助超时
            screenshotPath: './screenshots',
            reportPath: './reports',
            logPath: './logs'
        };

        this.logger = this.createLogger();
        this.isProcessing = false;
        this.currentWorkOrder = null;
        this.humanAssistanceRequired = false;
        this.suspendedTickets = new Set(); // 挂起的工单集合
        this.systemGuide = this.loadSystemGuide();

        // 状态管理
        this.status = 'idle'; // idle, processing, waiting_for_human, error
        this.lastHeartbeat = Date.now();
        this.metrics = this.initializeMetrics();

        this.initializeDirectories();
    }

    /**
     * 创建日志器
     */
    createLogger() {
        // 确保配置已初始化
        if (!this.config) {
            this.config = {
                logPath: './logs'
            };
        }

        const logFile = path.join(this.config.logPath, `ai-controller-${new Date().toISOString().split('T')[0]}.log`);
        
        const writeLog = (level, msg, data) => {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] [${level}] ${msg} ${data ? JSON.stringify(data) : ''}\n`;
            
            // 控制台输出
            console.log(`[AI-Controller] ${timestamp} ${level}: ${msg}`, data || '');
            
            // 文件输出
            try {
                fs.appendFileSync(logFile, logEntry);
            } catch (error) {
                console.error('写入日志文件失败:', error.message);
            }
        };

        return {
            info: (msg, data) => writeLog('INFO', msg, data),
            warn: (msg, data) => writeLog('WARN', msg, data),
            error: (msg, data) => writeLog('ERROR', msg, data),
            debug: (msg, data) => writeLog('DEBUG', msg, data)
        };
    }

    /**
     * 初始化监控指标
     */
    initializeMetrics() {
        return {
            workOrdersProcessed: 0,
            workOrdersSuccessful: 0,
            workOrdersFailed: 0,
            mcpConnectionFailures: 0,
            aiCallCount: 0,
            aiCallTotalTime: 0,
            browserOperationsCount: 0,
            browserOperationsSuccessful: 0,
            humanAssistanceRequests: 0,
            startTime: Date.now()
        };
    }

    /**
     * 加载系统操作指引
     */
    loadSystemGuide() {
        try {
            const guidePath = path.join(__dirname, '../../../system_guide.md');
            return fs.readFileSync(guidePath, 'utf8');
        } catch (error) {
            this.logger.error('无法加载系统指引文件', error.message);
            return '';
        }
    }

    /**
     * 初始化目录
     */
    initializeDirectories() {
        [this.config.screenshotPath, this.config.reportPath, this.config.logPath].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }

    /**
     * 启动AI助手
     */
    async start() {
        this.logger.info('🚀 启动增强AI助手控制器');
        
        try {
            // 初始化MCP客户端
            await this.initializeMCP();
            
            // 初始化MCP执行器
            this.mcpExecutor = new RealMCPExecutor(this.mcpClient);
            
            // 启动工单轮询
            this.startWorkOrderPolling();
            
            // 启动心跳监控
            this.startHeartbeat();
            
            // 启动监控指标报告
            this.startMetricsReporting();
            
            this.logger.info('✅ AI助手启动成功');
            this.emit('started');
            return true;
        } catch (error) {
            this.logger.error('❌ AI助手启动失败', error.message);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * 初始化MCP客户端
     */
    async initializeMCP() {
        this.logger.info('🔌 初始化MCP客户端...');
        
        try {
            this.mcpClient = new MCPClientV2(this.logger);
            const success = await this.mcpClient.initialize();
            
            if (!success) {
                this.metrics.mcpConnectionFailures++;
                throw new Error('MCP客户端初始化失败');
            }
            
            this.logger.info('✅ MCP客户端初始化成功');
        } catch (error) {
            this.metrics.mcpConnectionFailures++;
            throw error;
        }
    }

    /**
     * 启动工单轮询
     */
    startWorkOrderPolling() {
        this.logger.info('📋 启动工单轮询...');
        
        setInterval(async () => {
            if (!this.isProcessing && this.status === 'idle') {
                await this.checkForNewWorkOrders();
            }
        }, this.config.pollInterval);
    }

    /**
     * 启动心跳监控
     */
    startHeartbeat() {
        setInterval(() => {
            this.lastHeartbeat = Date.now();
            this.reportStatus();
        }, 30000); // 30秒心跳
    }

    /**
     * 启动监控指标报告
     */
    startMetricsReporting() {
        setInterval(() => {
            this.reportMetrics();
        }, 60000); // 1分钟报告一次指标
    }

    /**
     * 检查新工单
     */
    async checkForNewWorkOrders() {
        try {
            const response = await axios.get(`${this.workOrderSystemUrl}/api/workorders/pending`);
            const pendingOrders = response.data;
            
            if (pendingOrders.length > 0) {
                const workOrder = pendingOrders[0]; // 取第一个待处理工单
                this.logger.info(`📋 发现新工单: ${workOrder.id} - ${workOrder.title}`);
                await this.processWorkOrder(workOrder);
            }
        } catch (error) {
            this.logger.error('检查工单失败', error.message);
        }
    }

    /**
     * 处理工单 - 核心方法
     */
    async processWorkOrder(workOrder) {
        this.isProcessing = true;
        this.status = 'processing';
        this.currentWorkOrder = workOrder;
        this.metrics.workOrdersProcessed++;
        
        const startTime = Date.now();
        this.logger.info(`🔄 开始处理工单: ${workOrder.id}`);
        
        try {
            // 1. 更新工单状态为处理中
            await this.updateWorkOrderStatus(workOrder.id, '处理中', '开始AI自动化处理');
            
            // 2. 分析工单内容
            const analysisResult = await this.analyzeWorkOrder(workOrder);
            
            // 3. 检查是否需要人工辅助登录
            const needsLogin = await this.checkLoginRequirement(workOrder);
            if (needsLogin) {
                await this.requestHumanAssistance(workOrder, 'login');
            }
            
            // 4. 执行RPA任务
            const executionResult = await this.executeRPATask(workOrder);
            
            // 5. 生成详细报告
            const report = await this.generateDetailedReport(workOrder, executionResult);
            
            // 6. 更新工单状态为完成
            const finalStatus = executionResult.success ? '已完成' : '部分完成';
            await this.updateWorkOrderStatus(workOrder.id, finalStatus, report.summary, report);
            
            if (executionResult.success) {
                this.metrics.workOrdersSuccessful++;
            } else {
                this.metrics.workOrdersFailed++;
            }
            
            const processingTime = Date.now() - startTime;
            this.logger.info(`✅ 工单处理完成: ${workOrder.id}, 耗时: ${processingTime}ms`);
            
        } catch (error) {
            this.metrics.workOrdersFailed++;
            this.logger.error(`❌ 工单处理失败: ${workOrder.id}`, error.message);
            
            await this.updateWorkOrderStatus(
                workOrder.id,
                '处理失败',
                `处理过程中发生错误: ${error.message}`,
                { error: error.message, stack: error.stack }
            );
        } finally {
            this.isProcessing = false;
            this.status = 'idle';
            this.currentWorkOrder = null;
            this.humanAssistanceRequired = false;
        }
    }

    /**
     * 分析工单内容
     */
    async analyzeWorkOrder(workOrder) {
        this.logger.info(`🔍 分析工单内容: ${workOrder.id}`);
        
        // 基于工单内容和系统指引进行分析
        const analysis = {
            type: this.detectWorkOrderType(workOrder),
            complexity: this.assessComplexity(workOrder),
            requiredSteps: this.extractRequiredSteps(workOrder),
            targetSystem: this.identifyTargetSystem(workOrder)
        };
        
        this.logger.info('📊 工单分析结果', analysis);
        return analysis;
    }

    /**
     * 检查是否需要登录
     */
    async checkLoginRequirement(workOrder) {
        // 检查工单内容是否涉及需要登录的系统
        const needsLoginKeywords = ['商户后台', 'BD后台', '管理后台', '登录'];
        const content = workOrder.content.toLowerCase();
        
        return needsLoginKeywords.some(keyword => content.includes(keyword));
    }

    /**
     * 请求人工辅助
     */
    async requestHumanAssistance(workOrder, type) {
        this.humanAssistanceRequired = true;
        this.status = 'waiting_for_human';
        this.metrics.humanAssistanceRequests++;

        this.logger.warn(`🙋 请求人工辅助: ${type} for 工单 ${workOrder.id}`);

        // 发送人工辅助请求到工单系统
        await this.updateWorkOrderStatus(
            workOrder.id,
            '待人工辅助',
            `需要人工辅助进行${type === 'login' ? '登录操作' : type}`
        );

        // 等待人工完成或超时
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.logger.error('人工辅助超时');
                reject(new Error('人工辅助超时'));
            }, this.config.humanAssistanceTimeout);

            // 监听人工辅助完成事件
            this.once('human_assistance_completed', () => {
                clearTimeout(timeout);
                this.humanAssistanceRequired = false;
                this.status = 'processing';
                resolve();
            });
        });
    }

    /**
     * 执行RPA任务
     */
    async executeRPATask(workOrder) {
        this.logger.info(`🤖 执行RPA任务: ${workOrder.id}`);

        const startTime = Date.now();
        this.metrics.aiCallCount++;

        try {
            // 使用真正的MCP执行器
            const result = await this.mcpExecutor.executeRealRPATask(workOrder);

            const executionTime = Date.now() - startTime;
            this.metrics.aiCallTotalTime += executionTime;

            // 更新浏览器操作统计
            if (result.toolResults) {
                this.metrics.browserOperationsCount += result.toolResults.length;
                this.metrics.browserOperationsSuccessful += result.toolResults.filter(r => r.success).length;
            }

            this.logger.info(`✅ RPA任务执行完成: ${workOrder.id}, 耗时: ${executionTime}ms`);
            return result;

        } catch (error) {
            const executionTime = Date.now() - startTime;
            this.metrics.aiCallTotalTime += executionTime;

            this.logger.error(`❌ RPA任务执行失败: ${workOrder.id}`, error.message);
            return {
                success: false,
                error: error.message,
                ticketId: workOrder.id
            };
        }
    }

    /**
     * 生成详细报告
     */
    async generateDetailedReport(workOrder, executionResult) {
        this.logger.info(`📄 生成详细报告: ${workOrder.id}`);

        const report = {
            workOrderId: workOrder.id,
            title: workOrder.title,
            status: executionResult.success ? 'completed' : 'failed',
            executionTime: new Date().toISOString(),
            summary: this.generateSummary(workOrder, executionResult),
            details: {
                workOrder: workOrder,
                executionResult: executionResult,
                metrics: this.getCurrentMetrics()
            },
            screenshots: this.collectScreenshots(workOrder.id),
            logs: this.collectLogs(workOrder.id)
        };

        // 保存报告到文件
        const reportPath = path.join(this.config.reportPath, `report-${workOrder.id}-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

        this.logger.info(`📄 报告已保存: ${reportPath}`);
        return report;
    }

    /**
     * 生成摘要
     */
    generateSummary(workOrder, executionResult) {
        if (executionResult.success) {
            return `✅ 工单 ${workOrder.id} 执行成功。${executionResult.finalMessage || '任务已完成'}`;
        } else {
            return `❌ 工单 ${workOrder.id} 执行失败。错误: ${executionResult.error || '未知错误'}`;
        }
    }

    /**
     * 更新工单状态
     */
    async updateWorkOrderStatus(workOrderId, status, message, details = null) {
        try {
            const updateData = {
                status: status,
                message: message,
                updatedAt: new Date().toISOString(),
                details: details
            };

            await axios.put(`${this.workOrderSystemUrl}/api/workorders/${workOrderId}/status`, updateData);
            this.logger.info(`📝 工单状态已更新: ${workOrderId} -> ${status}`);

        } catch (error) {
            this.logger.error(`❌ 更新工单状态失败: ${workOrderId}`, error.message);
        }
    }

    /**
     * 工单类型检测
     */
    detectWorkOrderType(workOrder) {
        const content = workOrder.content.toLowerCase();

        if (content.includes('下架') && content.includes('商品')) {
            return 'product_delisting';
        } else if (content.includes('上架') && content.includes('商品')) {
            return 'product_listing';
        } else if (content.includes('门店') && content.includes('管理')) {
            return 'store_management';
        } else {
            return 'general';
        }
    }

    /**
     * 评估复杂度
     */
    assessComplexity(workOrder) {
        const content = workOrder.content;
        const steps = content.split(/[。\n]/).filter(s => s.trim().length > 0);

        if (steps.length <= 3) return 'low';
        if (steps.length <= 6) return 'medium';
        return 'high';
    }

    /**
     * 提取所需步骤
     */
    extractRequiredSteps(workOrder) {
        const content = workOrder.content;
        return content.split(/[。\n]/).filter(s => s.trim().length > 0);
    }

    /**
     * 识别目标系统
     */
    identifyTargetSystem(workOrder) {
        const content = workOrder.content.toLowerCase();

        if (content.includes('商户后台') || content.includes('bd后台')) {
            return 'merchant_backend';
        } else if (content.includes('管理后台')) {
            return 'admin_backend';
        } else {
            return 'unknown';
        }
    }

    /**
     * 收集截图
     */
    collectScreenshots(workOrderId) {
        try {
            const screenshots = fs.readdirSync(this.config.screenshotPath)
                .filter(file => file.includes(workOrderId))
                .map(file => path.join(this.config.screenshotPath, file));
            return screenshots;
        } catch (error) {
            this.logger.warn('收集截图失败', error.message);
            return [];
        }
    }

    /**
     * 收集日志
     */
    collectLogs(workOrderId) {
        try {
            const logFile = path.join(this.config.logPath, `ai-controller-${new Date().toISOString().split('T')[0]}.log`);
            const logs = fs.readFileSync(logFile, 'utf8')
                .split('\n')
                .filter(line => line.includes(workOrderId));
            return logs;
        } catch (error) {
            this.logger.warn('收集日志失败', error.message);
            return [];
        }
    }

    /**
     * 获取当前指标
     */
    getCurrentMetrics() {
        const uptime = Date.now() - this.metrics.startTime;
        const avgAICallTime = this.metrics.aiCallCount > 0 ?
            this.metrics.aiCallTotalTime / this.metrics.aiCallCount : 0;

        return {
            ...this.metrics,
            uptime: uptime,
            avgAICallTime: avgAICallTime,
            successRate: this.metrics.workOrdersProcessed > 0 ?
                (this.metrics.workOrdersSuccessful / this.metrics.workOrdersProcessed * 100).toFixed(2) + '%' : '0%'
        };
    }

    /**
     * 报告状态
     */
    reportStatus() {
        const status = {
            status: this.status,
            isProcessing: this.isProcessing,
            currentWorkOrder: this.currentWorkOrder?.id || null,
            humanAssistanceRequired: this.humanAssistanceRequired,
            lastHeartbeat: this.lastHeartbeat,
            metrics: this.getCurrentMetrics()
        };

        this.emit('status_update', status);
    }

    /**
     * 报告指标
     */
    reportMetrics() {
        const metrics = this.getCurrentMetrics();
        this.logger.info('📊 性能指标报告', metrics);
        this.emit('metrics_update', metrics);
    }

    /**
     * 停止AI助手
     */
    async stop() {
        this.logger.info('🛑 停止AI助手...');

        if (this.mcpClient) {
            await this.mcpClient.close();
        }

        this.emit('stopped');
        this.logger.info('✅ AI助手已停止');
    }

    /**
     * 挂起工单
     */
    async suspendTicket(ticketId) {
        try {
            this.logger.info(`🛑 挂起工单: ${ticketId}`);

            // 添加到挂起集合
            this.suspendedTickets.add(ticketId);

            // 如果当前正在处理该工单，停止处理
            if (this.currentWorkOrder && this.currentWorkOrder.id === ticketId) {
                this.logger.info(`⏹️ 停止当前正在处理的工单: ${ticketId}`);
                this.isProcessing = false;
                this.status = 'suspended';

                // 发出挂起事件
                this.emit('ticket_suspended', ticketId);
            }

            this.logger.info(`✅ 工单 ${ticketId} 已挂起`);

        } catch (error) {
            this.logger.error(`❌ 挂起工单失败 ${ticketId}:`, error);
            throw error;
        }
    }

    /**
     * 恢复工单
     */
    async resumeTicket(ticketId) {
        try {
            this.logger.info(`▶️ 恢复工单: ${ticketId}`);

            // 从挂起集合中移除
            this.suspendedTickets.delete(ticketId);

            this.logger.info(`✅ 工单 ${ticketId} 已恢复`);

        } catch (error) {
            this.logger.error(`❌ 恢复工单失败 ${ticketId}:`, error);
            throw error;
        }
    }

    /**
     * 检查工单是否被挂起
     */
    isTicketSuspended(ticketId) {
        return this.suspendedTickets.has(ticketId);
    }

    /**
     * 手动触发人工辅助完成
     */
    completeHumanAssistance() {
        if (this.humanAssistanceRequired) {
            this.emit('human_assistance_completed');
            this.logger.info('✅ 人工辅助已完成');
        }
    }
}

module.exports = EnhancedAIController;
