/**
 * 操作验证器 - AI-First RPA架构的验证层
 * 负责操作结果验证，页面状态变化检测，错误自动识别
 */

class ActionValidator {
    constructor(page, logger = console) {
        this.page = page;
        this.logger = logger;
        this.beforeState = null;
        this.afterState = null;
    }

    /**
     * 验证操作结果
     */
    async validateAction(executionResult, plannedAction, executionContext) {
        try {
            this.logger.info(`🔍 验证操作结果: ${plannedAction.action_type}`);

            // 基础验证
            const basicValidation = this.validateBasicExecution(executionResult, plannedAction);
            
            if (!basicValidation.isValid) {
                return basicValidation;
            }

            // 页面状态验证
            const stateValidation = await this.validatePageStateChange(plannedAction);
            
            // 预期结果验证
            const outcomeValidation = await this.validateExpectedOutcome(plannedAction);
            
            // 综合评估
            const overallValidation = this.combineValidationResults([
                basicValidation,
                stateValidation,
                outcomeValidation
            ]);

            this.logger.info(`✅ 操作验证完成，置信度: ${overallValidation.confidence}`);
            
            return overallValidation;
        } catch (error) {
            this.logger.error('❌ 操作验证失败:', error);
            return {
                isValid: false,
                hasError: true,
                error: error.message,
                confidence: 0.1,
                validationType: 'validation_error'
            };
        }
    }

    /**
     * 基础执行验证
     */
    validateBasicExecution(executionResult, plannedAction) {
        try {
            // 检查执行是否成功
            if (!executionResult.success) {
                return {
                    isValid: false,
                    hasError: true,
                    error: executionResult.message || '操作执行失败',
                    confidence: 0.9,
                    validationType: 'execution_failure',
                    details: {
                        plannedAction: plannedAction.action_type,
                        executionMessage: executionResult.message
                    }
                };
            }

            // 检查执行时间是否合理
            const executionTime = Date.now() - new Date(executionResult.timestamp).getTime();
            if (executionTime > 30000) { // 超过30秒
                return {
                    isValid: false,
                    hasError: true,
                    error: '操作执行时间过长',
                    confidence: 0.7,
                    validationType: 'timeout',
                    details: {
                        executionTime: executionTime,
                        threshold: 30000
                    }
                };
            }

            return {
                isValid: true,
                hasError: false,
                confidence: 0.8,
                validationType: 'basic_execution',
                message: '基础执行验证通过'
            };
        } catch (error) {
            return {
                isValid: false,
                hasError: true,
                error: `基础验证失败: ${error.message}`,
                confidence: 0.1,
                validationType: 'validation_error'
            };
        }
    }

    /**
     * 页面状态变化验证
     */
    async validatePageStateChange(plannedAction) {
        try {
            // 等待页面稳定
            await this.page.waitForTimeout(1000);

            const currentUrl = this.page.url();
            const currentTitle = await this.page.title();

            // 根据操作类型验证相应的状态变化
            switch (plannedAction.action_type) {
                case 'navigate':
                    return this.validateNavigationChange(plannedAction, currentUrl);
                
                case 'click':
                    return await this.validateClickChange(plannedAction, currentUrl, currentTitle);
                
                case 'type':
                    return await this.validateTypeChange(plannedAction);
                
                case 'wait':
                    return this.validateWaitChange(plannedAction);
                
                case 'screenshot':
                    return this.validateScreenshotChange(plannedAction);
                
                default:
                    return {
                        isValid: true,
                        hasError: false,
                        confidence: 0.6,
                        validationType: 'state_change',
                        message: '未知操作类型，跳过状态验证'
                    };
            }
        } catch (error) {
            return {
                isValid: false,
                hasError: true,
                error: `页面状态验证失败: ${error.message}`,
                confidence: 0.2,
                validationType: 'state_validation_error'
            };
        }
    }

    /**
     * 验证导航操作的状态变化
     */
    validateNavigationChange(plannedAction, currentUrl) {
        const expectedUrl = plannedAction.action_data.url;
        
        if (!expectedUrl) {
            return {
                isValid: false,
                hasError: true,
                error: '导航操作缺少目标URL',
                confidence: 0.9,
                validationType: 'navigation_validation'
            };
        }

        // 检查URL是否匹配
        const urlMatches = currentUrl.includes(expectedUrl) || expectedUrl.includes(currentUrl);
        
        if (urlMatches) {
            return {
                isValid: true,
                hasError: false,
                confidence: 0.9,
                validationType: 'navigation_validation',
                message: `导航成功，当前URL: ${currentUrl}`
            };
        } else {
            return {
                isValid: false,
                hasError: true,
                error: `导航失败，期望URL: ${expectedUrl}，实际URL: ${currentUrl}`,
                confidence: 0.8,
                validationType: 'navigation_validation'
            };
        }
    }

    /**
     * 验证点击操作的状态变化
     */
    async validateClickChange(plannedAction, currentUrl, currentTitle) {
        try {
            // 检查是否有页面跳转
            const hasNavigation = this.beforeState && this.beforeState.url !== currentUrl;
            
            // 检查是否有新的元素出现
            const hasNewElements = await this.detectNewElements();
            
            // 检查是否有弹窗或对话框
            const hasDialog = await this.detectDialog();
            
            // 检查是否有加载状态
            const isLoading = await this.detectLoadingState();

            if (hasNavigation || hasNewElements || hasDialog || isLoading) {
                return {
                    isValid: true,
                    hasError: false,
                    confidence: 0.8,
                    validationType: 'click_validation',
                    message: '点击操作产生了预期的页面变化',
                    details: {
                        hasNavigation,
                        hasNewElements,
                        hasDialog,
                        isLoading
                    }
                };
            } else {
                return {
                    isValid: true,
                    hasError: false,
                    confidence: 0.6,
                    validationType: 'click_validation',
                    message: '点击操作完成，但未检测到明显的页面变化'
                };
            }
        } catch (error) {
            return {
                isValid: false,
                hasError: true,
                error: `点击验证失败: ${error.message}`,
                confidence: 0.3,
                validationType: 'click_validation'
            };
        }
    }

    /**
     * 验证输入操作的状态变化
     */
    async validateTypeChange(plannedAction) {
        try {
            const targetText = plannedAction.action_data.text;
            const targetSelector = plannedAction.target_element.selector;

            if (!targetText || !targetSelector) {
                return {
                    isValid: false,
                    hasError: true,
                    error: '输入操作缺少必要参数',
                    confidence: 0.9,
                    validationType: 'type_validation'
                };
            }

            // 尝试验证输入框的值
            try {
                const element = await this.page.$(targetSelector);
                if (element) {
                    const currentValue = await element.inputValue();
                    if (currentValue === targetText) {
                        return {
                            isValid: true,
                            hasError: false,
                            confidence: 0.9,
                            validationType: 'type_validation',
                            message: '输入验证成功，文本已正确输入'
                        };
                    } else {
                        return {
                            isValid: false,
                            hasError: true,
                            error: `输入验证失败，期望: "${targetText}"，实际: "${currentValue}"`,
                            confidence: 0.8,
                            validationType: 'type_validation'
                        };
                    }
                }
            } catch (e) {
                // 如果无法验证具体值，认为操作成功
                return {
                    isValid: true,
                    hasError: false,
                    confidence: 0.6,
                    validationType: 'type_validation',
                    message: '输入操作完成，无法验证具体值'
                };
            }

            return {
                isValid: true,
                hasError: false,
                confidence: 0.7,
                validationType: 'type_validation',
                message: '输入操作完成'
            };
        } catch (error) {
            return {
                isValid: false,
                hasError: true,
                error: `输入验证失败: ${error.message}`,
                confidence: 0.3,
                validationType: 'type_validation'
            };
        }
    }

    /**
     * 验证等待操作
     */
    validateWaitChange(plannedAction) {
        const waitTime = plannedAction.action_data.timeout || 3000;
        
        return {
            isValid: true,
            hasError: false,
            confidence: 0.9,
            validationType: 'wait_validation',
            message: `等待操作完成，等待时间: ${waitTime}ms`
        };
    }

    /**
     * 验证截图操作
     */
    validateScreenshotChange(plannedAction) {
        return {
            isValid: true,
            hasError: false,
            confidence: 0.9,
            validationType: 'screenshot_validation',
            message: '截图操作完成'
        };
    }

    /**
     * 验证预期结果
     */
    async validateExpectedOutcome(plannedAction) {
        try {
            const expectedOutcome = plannedAction.expected_outcome;
            
            if (!expectedOutcome) {
                return {
                    isValid: true,
                    hasError: false,
                    confidence: 0.5,
                    validationType: 'outcome_validation',
                    message: '未指定预期结果，跳过验证'
                };
            }

            // 简单的关键词匹配验证
            const pageContent = await this.page.textContent('body');
            const pageTitle = await this.page.title();
            const pageUrl = this.page.url();

            const searchText = expectedOutcome.toLowerCase();
            const hasExpectedContent = 
                pageContent.toLowerCase().includes(searchText) ||
                pageTitle.toLowerCase().includes(searchText) ||
                pageUrl.toLowerCase().includes(searchText);

            if (hasExpectedContent) {
                return {
                    isValid: true,
                    hasError: false,
                    confidence: 0.7,
                    validationType: 'outcome_validation',
                    message: '预期结果验证通过'
                };
            } else {
                return {
                    isValid: true,
                    hasError: false,
                    confidence: 0.4,
                    validationType: 'outcome_validation',
                    message: '未检测到预期结果，但操作可能仍然成功'
                };
            }
        } catch (error) {
            return {
                isValid: true,
                hasError: false,
                confidence: 0.3,
                validationType: 'outcome_validation',
                message: `预期结果验证失败: ${error.message}`
            };
        }
    }

    /**
     * 检测新元素
     */
    async detectNewElements() {
        try {
            // 简单检测：查看是否有新的可见元素
            const visibleElements = await this.page.$$('*:visible');
            return visibleElements.length > 0;
        } catch (error) {
            return false;
        }
    }

    /**
     * 检测对话框
     */
    async detectDialog() {
        try {
            const dialogs = await this.page.$$('[role="dialog"], .modal, .popup, .alert');
            return dialogs.length > 0;
        } catch (error) {
            return false;
        }
    }

    /**
     * 检测加载状态
     */
    async detectLoadingState() {
        try {
            const loadingElements = await this.page.$$('.loading, .spinner, [aria-busy="true"]');
            return loadingElements.length > 0;
        } catch (error) {
            return false;
        }
    }

    /**
     * 组合验证结果
     */
    combineValidationResults(validations) {
        const validValidations = validations.filter(v => v.isValid);
        const invalidValidations = validations.filter(v => !v.isValid);
        
        // 如果有任何验证失败，整体失败
        if (invalidValidations.length > 0) {
            const primaryError = invalidValidations[0];
            return {
                isValid: false,
                hasError: true,
                error: primaryError.error,
                confidence: primaryError.confidence,
                validationType: 'combined_validation',
                details: {
                    validValidations: validValidations.length,
                    invalidValidations: invalidValidations.length,
                    allValidations: validations
                }
            };
        }

        // 计算平均置信度
        const avgConfidence = validValidations.reduce((sum, v) => sum + v.confidence, 0) / validValidations.length;

        return {
            isValid: true,
            hasError: false,
            confidence: avgConfidence,
            validationType: 'combined_validation',
            message: '所有验证都通过',
            details: {
                validValidations: validValidations.length,
                invalidValidations: 0,
                avgConfidence: avgConfidence
            }
        };
    }

    /**
     * 捕获操作前状态
     */
    async captureBeforeState() {
        try {
            this.beforeState = {
                url: this.page.url(),
                title: await this.page.title(),
                timestamp: Date.now()
            };
        } catch (error) {
            this.logger.error('❌ 捕获操作前状态失败:', error);
        }
    }

    /**
     * 捕获操作后状态
     */
    async captureAfterState() {
        try {
            this.afterState = {
                url: this.page.url(),
                title: await this.page.title(),
                timestamp: Date.now()
            };
        } catch (error) {
            this.logger.error('❌ 捕获操作后状态失败:', error);
        }
    }

    /**
     * 比较状态变化
     */
    compareStates() {
        if (!this.beforeState || !this.afterState) {
            return null;
        }

        return {
            urlChanged: this.beforeState.url !== this.afterState.url,
            titleChanged: this.beforeState.title !== this.afterState.title,
            timeDiff: this.afterState.timestamp - this.beforeState.timestamp,
            beforeState: this.beforeState,
            afterState: this.afterState
        };
    }
}

module.exports = ActionValidator;
