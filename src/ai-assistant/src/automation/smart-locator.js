/**
 * 智能元素定位器 - AI-First RPA架构的自动化层
 * 负责AI驱动的元素定位，多种定位策略组合，自适应选择器生成
 */

class SmartLocator {
    constructor(page, logger = console) {
        this.page = page;
        this.logger = logger;
        this.locatorStrategies = [
            'byId',
            'byClass',
            'byText',
            'byRole',
            'byPlaceholder',
            'byTestId',
            'byXPath',
            'byCSS'
        ];
    }

    /**
     * 智能定位元素
     */
    async locateElement(targetElement) {
        try {
            this.logger.info(`🎯 开始智能定位元素: ${targetElement.description}`);

            const strategies = [];
            
            // 尝试所有定位策略
            for (const strategy of this.locatorStrategies) {
                try {
                    const result = await this[strategy](targetElement);
                    if (result.success) {
                        strategies.push(result);
                    }
                } catch (error) {
                    this.logger.debug(`定位策略 ${strategy} 失败:`, error.message);
                }
            }

            // 选择最佳策略
            const bestStrategy = this.selectBestStrategy(strategies);
            
            if (bestStrategy) {
                this.logger.info(`✅ 元素定位成功，使用策略: ${bestStrategy.strategy}`);
                return bestStrategy;
            } else {
                this.logger.warn(`⚠️ 元素定位失败: ${targetElement.description}`);
                return {
                    success: false,
                    strategy: 'none',
                    element: null,
                    confidence: 0,
                    message: '所有定位策略都失败了'
                };
            }
        } catch (error) {
            this.logger.error('❌ 智能定位失败:', error);
            return {
                success: false,
                strategy: 'error',
                element: null,
                confidence: 0,
                message: error.message
            };
        }
    }

    /**
     * 通过ID定位
     */
    async byId(targetElement) {
        const selector = targetElement.selector;
        
        if (!selector || !selector.startsWith('#')) {
            return { success: false };
        }

        try {
            const element = await this.page.$(selector);
            if (element && await element.isVisible()) {
                return {
                    success: true,
                    strategy: 'byId',
                    element: element,
                    selector: selector,
                    confidence: 0.9,
                    message: `通过ID定位成功: ${selector}`
                };
            }
        } catch (error) {
            // 继续尝试其他策略
        }

        return { success: false };
    }

    /**
     * 通过Class定位
     */
    async byClass(targetElement) {
        const selector = targetElement.selector;
        
        if (!selector || !selector.startsWith('.')) {
            return { success: false };
        }

        try {
            const elements = await this.page.$$(selector);
            
            // 找到可见的元素
            for (const element of elements) {
                if (await element.isVisible()) {
                    return {
                        success: true,
                        strategy: 'byClass',
                        element: element,
                        selector: selector,
                        confidence: 0.7,
                        message: `通过Class定位成功: ${selector}`
                    };
                }
            }
        } catch (error) {
            // 继续尝试其他策略
        }

        return { success: false };
    }

    /**
     * 通过文本内容定位
     */
    async byText(targetElement) {
        const text = targetElement.description || targetElement.text;
        
        if (!text) {
            return { success: false };
        }

        try {
            // 尝试精确匹配
            let element = await this.page.getByText(text, { exact: true }).first();
            if (await element.isVisible().catch(() => false)) {
                return {
                    success: true,
                    strategy: 'byText',
                    element: element,
                    selector: `text="${text}"`,
                    confidence: 0.8,
                    message: `通过精确文本定位成功: ${text}`
                };
            }

            // 尝试部分匹配
            element = await this.page.getByText(text).first();
            if (await element.isVisible().catch(() => false)) {
                return {
                    success: true,
                    strategy: 'byText',
                    element: element,
                    selector: `text*="${text}"`,
                    confidence: 0.6,
                    message: `通过部分文本定位成功: ${text}`
                };
            }
        } catch (error) {
            // 继续尝试其他策略
        }

        return { success: false };
    }

    /**
     * 通过角色定位
     */
    async byRole(targetElement) {
        const description = targetElement.description || '';
        const text = targetElement.text || '';
        
        // 根据描述推断角色
        let role = null;
        let name = text || description;

        if (description.includes('按钮') || description.includes('button')) {
            role = 'button';
        } else if (description.includes('链接') || description.includes('link')) {
            role = 'link';
        } else if (description.includes('输入') || description.includes('input')) {
            role = 'textbox';
        } else if (description.includes('选择') || description.includes('select')) {
            role = 'combobox';
        }

        if (!role) {
            return { success: false };
        }

        try {
            const element = await this.page.getByRole(role, { name: name }).first();
            if (await element.isVisible().catch(() => false)) {
                return {
                    success: true,
                    strategy: 'byRole',
                    element: element,
                    selector: `role=${role}[name="${name}"]`,
                    confidence: 0.8,
                    message: `通过角色定位成功: ${role} - ${name}`
                };
            }
        } catch (error) {
            // 继续尝试其他策略
        }

        return { success: false };
    }

    /**
     * 通过占位符定位
     */
    async byPlaceholder(targetElement) {
        const placeholder = targetElement.placeholder;
        
        if (!placeholder) {
            return { success: false };
        }

        try {
            const element = await this.page.getByPlaceholder(placeholder).first();
            if (await element.isVisible().catch(() => false)) {
                return {
                    success: true,
                    strategy: 'byPlaceholder',
                    element: element,
                    selector: `placeholder="${placeholder}"`,
                    confidence: 0.8,
                    message: `通过占位符定位成功: ${placeholder}`
                };
            }
        } catch (error) {
            // 继续尝试其他策略
        }

        return { success: false };
    }

    /**
     * 通过测试ID定位
     */
    async byTestId(targetElement) {
        const selector = targetElement.selector;
        
        if (!selector || !selector.includes('data-testid')) {
            return { success: false };
        }

        try {
            const testId = selector.match(/data-testid[=~]"([^"]+)"/)?.[1];
            if (testId) {
                const element = await this.page.getByTestId(testId).first();
                if (await element.isVisible().catch(() => false)) {
                    return {
                        success: true,
                        strategy: 'byTestId',
                        element: element,
                        selector: `data-testid="${testId}"`,
                        confidence: 0.9,
                        message: `通过测试ID定位成功: ${testId}`
                    };
                }
            }
        } catch (error) {
            // 继续尝试其他策略
        }

        return { success: false };
    }

    /**
     * 通过XPath定位
     */
    async byXPath(targetElement) {
        const text = targetElement.description || targetElement.text;
        
        if (!text) {
            return { success: false };
        }

        try {
            // 构建XPath表达式
            const xpaths = [
                `//button[contains(text(), "${text}")]`,
                `//a[contains(text(), "${text}")]`,
                `//input[@placeholder="${text}"]`,
                `//input[@value="${text}"]`,
                `//*[contains(text(), "${text}")]`
            ];

            for (const xpath of xpaths) {
                try {
                    const element = await this.page.locator(`xpath=${xpath}`).first();
                    if (await element.isVisible().catch(() => false)) {
                        return {
                            success: true,
                            strategy: 'byXPath',
                            element: element,
                            selector: xpath,
                            confidence: 0.6,
                            message: `通过XPath定位成功: ${xpath}`
                        };
                    }
                } catch (e) {
                    // 继续尝试下一个XPath
                }
            }
        } catch (error) {
            // 继续尝试其他策略
        }

        return { success: false };
    }

    /**
     * 通过CSS选择器定位
     */
    async byCSS(targetElement) {
        const selector = targetElement.selector;
        
        if (!selector || selector.startsWith('#') || selector.startsWith('.')) {
            return { success: false };
        }

        try {
            const element = await this.page.$(selector);
            if (element && await element.isVisible()) {
                return {
                    success: true,
                    strategy: 'byCSS',
                    element: element,
                    selector: selector,
                    confidence: 0.7,
                    message: `通过CSS选择器定位成功: ${selector}`
                };
            }
        } catch (error) {
            // 继续尝试其他策略
        }

        return { success: false };
    }

    /**
     * 选择最佳定位策略
     */
    selectBestStrategy(strategies) {
        if (strategies.length === 0) {
            return null;
        }

        // 按置信度排序
        strategies.sort((a, b) => b.confidence - a.confidence);
        
        // 返回置信度最高的策略
        return strategies[0];
    }

    /**
     * 生成自适应选择器
     */
    async generateAdaptiveSelector(element) {
        try {
            const selectors = [];

            // 尝试获取ID
            const id = await element.getAttribute('id');
            if (id) {
                selectors.push({
                    selector: `#${id}`,
                    confidence: 0.9,
                    type: 'id'
                });
            }

            // 尝试获取唯一的class组合
            const className = await element.getAttribute('class');
            if (className) {
                const classes = className.split(' ').filter(c => c.trim());
                if (classes.length > 0) {
                    selectors.push({
                        selector: `.${classes.join('.')}`,
                        confidence: 0.7,
                        type: 'class'
                    });
                }
            }

            // 尝试获取文本内容
            const text = await element.textContent();
            if (text && text.trim()) {
                selectors.push({
                    selector: `text="${text.trim()}"`,
                    confidence: 0.8,
                    type: 'text'
                });
            }

            // 尝试获取其他属性
            const name = await element.getAttribute('name');
            if (name) {
                selectors.push({
                    selector: `[name="${name}"]`,
                    confidence: 0.8,
                    type: 'name'
                });
            }

            const placeholder = await element.getAttribute('placeholder');
            if (placeholder) {
                selectors.push({
                    selector: `[placeholder="${placeholder}"]`,
                    confidence: 0.8,
                    type: 'placeholder'
                });
            }

            // 返回置信度最高的选择器
            if (selectors.length > 0) {
                selectors.sort((a, b) => b.confidence - a.confidence);
                return selectors[0];
            }

            return null;
        } catch (error) {
            this.logger.error('❌ 生成自适应选择器失败:', error);
            return null;
        }
    }

    /**
     * 验证元素可操作性
     */
    async validateElementInteractability(element) {
        try {
            const checks = {
                isVisible: await element.isVisible(),
                isEnabled: await element.isEnabled(),
                isEditable: await element.isEditable().catch(() => false),
                isChecked: await element.isChecked().catch(() => null)
            };

            const score = Object.values(checks).filter(Boolean).length / Object.keys(checks).length;

            return {
                ...checks,
                interactabilityScore: score,
                isInteractable: checks.isVisible && checks.isEnabled
            };
        } catch (error) {
            return {
                isVisible: false,
                isEnabled: false,
                isEditable: false,
                isChecked: null,
                interactabilityScore: 0,
                isInteractable: false,
                error: error.message
            };
        }
    }
}

module.exports = SmartLocator;
