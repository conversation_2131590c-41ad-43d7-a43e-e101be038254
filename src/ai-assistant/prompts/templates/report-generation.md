# 报告生成模板

## 报告类型

### 1. 执行进度报告
用于实时反馈任务执行进度。

```markdown
# 任务执行进度报告

**工单编号**: {{ticketId}}  
**任务名称**: {{taskName}}  
**开始时间**: {{startTime}}  
**当前时间**: {{currentTime}}  

## 执行进度
- **总进度**: {{progressPercentage}}%
- **当前阶段**: {{currentPhase}}
- **执行状态**: {{status}}

## 已完成步骤
{{#each completedSteps}}
- [x] {{this.name}} (耗时: {{this.duration}})
{{/each}}

## 正在执行
- [ ] {{currentStep.name}} (已进行: {{currentStep.elapsed}})

## 待执行步骤
{{#each pendingSteps}}
- [ ] {{this.name}} (预计: {{this.estimate}})
{{/each}}

## 关键指标
- 成功操作数: {{stats.successCount}}
- 失败操作数: {{stats.failureCount}}
- 重试次数: {{stats.retryCount}}
- 平均响应时间: {{stats.avgResponseTime}}ms
```

### 2. 任务完成报告
任务完成后的综合报告。

```markdown
# 任务完成报告

**工单编号**: {{ticketId}}  
**任务名称**: {{taskName}}  
**执行时间**: {{startTime}} - {{endTime}}  
**总耗时**: {{totalDuration}}  
**执行结果**: {{result}}

## 执行摘要
{{summary}}

## 任务目标达成情况
{{#each objectives}}
- [{{#if this.completed}}x{{else}} {{/if}}] {{this.description}}
  - 完成度: {{this.completionRate}}%
  - 说明: {{this.notes}}
{{/each}}

## 执行详情

### 关键操作记录
{{#each keyOperations}}
#### {{@index+1}}. {{this.operation}}
- **时间**: {{this.timestamp}}
- **目标**: {{this.target}}
- **结果**: {{this.result}}
- **证据**: 
  ![截图]({{this.screenshot}})
{{/each}}

### 数据变更
{{#if dataChanges}}
| 字段 | 操作前 | 操作后 | 状态 |
|------|--------|--------|------|
{{#each dataChanges}}
| {{this.field}} | {{this.before}} | {{this.after}} | {{this.status}} |
{{/each}}
{{/if}}

## 异常与处理
{{#if exceptions}}
{{#each exceptions}}
### {{this.type}}
- **发生时间**: {{this.timestamp}}
- **错误描述**: {{this.description}}
- **处理方式**: {{this.handling}}
- **最终结果**: {{this.resolution}}
{{/each}}
{{else}}
无异常发生，任务顺利完成。
{{/if}}

## 性能分析
- **总操作数**: {{performance.totalOperations}}
- **平均操作耗时**: {{performance.avgOperationTime}}ms
- **最慢操作**: {{performance.slowestOperation.name}} ({{performance.slowestOperation.time}}ms)
- **网络请求数**: {{performance.networkRequests}}
- **重试率**: {{performance.retryRate}}%

## 建议与改进
{{#each recommendations}}
- {{this}}
{{/each}}

---
*报告生成时间: {{reportGeneratedAt}}*  
*执行引擎: Multi-Agent RPA System v2.0*
```

### 3. 错误报告
详细的错误分析报告。

```markdown
# 任务执行错误报告

**工单编号**: {{ticketId}}  
**错误时间**: {{errorTime}}  
**错误级别**: {{errorLevel}}  
**影响范围**: {{impactScope}}

## 错误概述
{{errorSummary}}

## 错误详情

### 错误信息
- **错误类型**: {{error.type}}
- **错误代码**: {{error.code}}
- **错误消息**: {{error.message}}
- **发生位置**: {{error.location}}

### 执行上下文
- **当前步骤**: {{context.currentStep}}
- **页面URL**: {{context.pageUrl}}
- **操作目标**: {{context.target}}
- **Agent状态**: {{context.agentStatus}}

### 错误追踪
```
{{error.stackTrace}}
```

### 相关截图
![错误发生时截图]({{screenshots.error}})
![页面状态截图]({{screenshots.pageState}})

## 原因分析

### 直接原因
{{analysis.directCause}}

### 根本原因
{{analysis.rootCause}}

### 触发条件
{{#each analysis.triggers}}
- {{this}}
{{/each}}

## 已尝试的恢复措施
{{#each recoveryAttempts}}
### 尝试 {{@index+1}}
- **方法**: {{this.method}}
- **结果**: {{this.result}}
- **耗时**: {{this.duration}}
{{/each}}

## 解决方案

### 即时解决方案
{{solutions.immediate}}

### 长期改进建议
{{#each solutions.longTerm}}
- {{this}}
{{/each}}

## 预防措施
{{#each preventions}}
- {{this}}
{{/each}}

---
*需要人工介入: {{needsIntervention}}*  
*报告生成时间: {{reportGeneratedAt}}*
```

### 4. 人工介入请求报告

```markdown
# 人工介入请求

**紧急程度**: {{urgency}} 🚨  
**请求时间**: {{requestTime}}  
**工单编号**: {{ticketId}}  
**当前任务**: {{currentTask}}

## 需要协助的原因
{{interventionReason}}

## 当前状态
- **页面位置**: {{currentPage}}
- **执行进度**: {{progress}}%
- **已尝试方案**: {{attemptedSolutions}}

## 需要您做什么

### 具体操作步骤
{{#each requiredActions}}
{{@index+1}}. {{this}}
{{/each}}

### 注意事项
{{#each precautions}}
- ⚠️ {{this}}
{{/each}}

## 操作环境信息
- **系统**: {{environment.system}}
- **浏览器**: {{environment.browser}}
- **登录账号**: {{environment.account}}

## 完成后续步骤
1. 完成上述操作后，请点击"继续执行"按钮
2. 系统将自动验证您的操作结果
3. 如果需要额外信息，请在备注中说明

## 超时处理
- **超时时间**: {{timeout}} 分钟
- **超时后动作**: {{timeoutAction}}

---
*请在 {{deadline}} 前完成操作*
```

## 报告生成规则

### 1. 时机选择
- **进度报告**: 每完成25%生成一次
- **阶段报告**: 每个主要阶段结束时
- **完成报告**: 任务全部完成后
- **错误报告**: 发生不可恢复错误时
- **介入请求**: 需要人工协助时

### 2. 内容原则
- **准确性**: 数据必须真实准确
- **完整性**: 包含所有关键信息
- **可读性**: 结构清晰，易于理解
- **可操作性**: 提供具体的建议

### 3. 格式规范
- 使用Markdown格式
- 包含必要的元数据
- 提供视觉化元素（表格、截图）
- 保持风格一致

## 报告数据结构

### 基础数据模型
```typescript
interface ReportData {
  // 基础信息
  ticketId: string;
  taskName: string;
  reportType: 'progress' | 'completion' | 'error' | 'intervention';
  generatedAt: Date;
  
  // 执行信息
  execution: {
    startTime: Date;
    endTime?: Date;
    duration?: number;
    status: 'running' | 'completed' | 'failed' | 'suspended';
    progress: number;
  };
  
  // 统计信息
  statistics: {
    totalSteps: number;
    completedSteps: number;
    successfulOperations: number;
    failedOperations: number;
    retries: number;
  };
  
  // 详细内容
  details: {
    steps: StepRecord[];
    errors: ErrorRecord[];
    screenshots: Screenshot[];
    logs: LogEntry[];
  };
  
  // 分析结果
  analysis: {
    summary: string;
    achievements: string[];
    issues: string[];
    recommendations: string[];
  };
}
```

### 步骤记录
```typescript
interface StepRecord {
  id: string;
  name: string;
  startTime: Date;
  endTime?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: any;
  error?: string;
  screenshots?: string[];
  retries: number;
}
```

### 错误记录
```typescript
interface ErrorRecord {
  timestamp: Date;
  type: string;
  message: string;
  stack?: string;
  context: any;
  handled: boolean;
  resolution?: string;
}
```

## 报告样式指南

### 1. 视觉层次
- 使用标题层级区分内容重要性
- 关键信息使用粗体或高亮
- 使用表格展示结构化数据
- 适当使用emoji增强可读性

### 2. 截图规范
- 包含时间戳
- 标注关键区域
- 压缩到合适大小
- 提供图片说明

### 3. 数据展示
- 数字使用千分位分隔
- 百分比保留1位小数
- 时间使用友好格式
- 金额使用货币符号

## 报告分发

### 1. 存储位置
```
reports/
├── {{year}}/
│   ├── {{month}}/
│   │   ├── {{ticketId}}_progress_{{timestamp}}.md
│   │   ├── {{ticketId}}_completion_{{timestamp}}.md
│   │   └── {{ticketId}}_error_{{timestamp}}.md
```

### 2. 通知机制
- WebSocket实时推送
- 邮件通知（错误和完成）
- 系统内消息
- API回调

### 3. 访问权限
- 报告默认对工单创建者可见
- 管理员可查看所有报告
- 支持报告分享链接
- 敏感信息自动脱敏

## 报告模板扩展

### 自定义字段
```json
{
  "customFields": {
    "businessUnit": "业务单元",
    "costCenter": "成本中心",
    "approver": "审批人",
    "customMetrics": {
      "efficiency": "效率指标",
      "quality": "质量指标"
    }
  }
}
```

### 多语言支持
- 报告模板支持多语言
- 动态切换语言版本
- 保持格式一致性

### 导出格式
- Markdown (默认)
- PDF (带样式)
- HTML (交互式)
- JSON (原始数据)