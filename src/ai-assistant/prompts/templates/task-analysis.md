# 任务分析模板

## 任务分析框架

### 1. 任务理解

#### 基本信息提取
```json
{
  "taskInfo": {
    "id": "工单ID",
    "title": "任务标题",
    "description": "详细描述",
    "priority": "high|medium|low",
    "deadline": "截止时间",
    "requester": "请求者"
  }
}
```

#### 目标识别
```json
{
  "objectives": {
    "primary": "主要目标描述",
    "secondary": ["次要目标1", "次要目标2"],
    "constraints": ["约束条件1", "约束条件2"],
    "successCriteria": ["成功标准1", "成功标准2"]
  }
}
```

### 2. 任务分类

#### 任务类型判断
```json
{
  "taskType": {
    "category": "数据录入|信息查询|状态更新|文件处理|审批流程",
    "complexity": "simple|medium|complex",
    "estimatedDuration": "预计耗时（分钟）",
    "requiredSkills": ["所需能力1", "所需能力2"]
  }
}
```

#### 任务特征分析
```json
{
  "characteristics": {
    "isRepetitive": false,        // 是否重复性任务
    "requiresDecision": true,     // 是否需要决策
    "hasMultiplePaths": true,     // 是否有多条路径
    "needsHumanInput": false,     // 是否需要人工输入
    "involvesSensitiveData": false // 是否涉及敏感数据
  }
}
```

### 3. 任务分解

#### 分解原则
1. **原子性**: 每个子任务应该是独立可执行的
2. **完整性**: 所有子任务完成等于总任务完成
3. **有序性**: 明确子任务之间的依赖关系
4. **可验证**: 每个子任务都有明确的完成标准

#### 分解结构
```json
{
  "taskDecomposition": {
    "mainTask": "主任务描述",
    "subTasks": [
      {
        "id": "ST001",
        "name": "子任务名称",
        "description": "子任务描述",
        "type": "准备|执行|验证",
        "dependencies": ["依赖的任务ID"],
        "estimatedTime": 5,
        "criticalPath": true,
        "rollbackable": true
      }
    ],
    "executionOrder": {
      "sequential": ["ST001", "ST002"],
      "parallel": [["ST003", "ST004"]],
      "conditional": {
        "ST005": "条件描述"
      }
    }
  }
}
```

### 4. 资源需求分析

#### 系统资源
```json
{
  "systemRequirements": {
    "targetSystem": "目标系统名称",
    "accessLevel": "required|admin|user",
    "specificPages": ["页面1", "页面2"],
    "apiEndpoints": ["API1", "API2"],
    "browserRequirements": {
      "type": "chrome|firefox|edge",
      "version": "最低版本要求",
      "plugins": ["插件1", "插件2"]
    }
  }
}
```

#### 数据资源
```json
{
  "dataRequirements": {
    "inputData": [
      {
        "field": "字段名",
        "type": "string|number|date|file",
        "source": "工单|系统|用户输入",
        "required": true,
        "validation": "验证规则"
      }
    ],
    "outputData": [
      {
        "field": "字段名",
        "type": "数据类型",
        "destination": "目标位置",
        "format": "格式要求"
      }
    ]
  }
}
```

### 5. 风险评估

#### 风险识别
```json
{
  "riskAssessment": {
    "technicalRisks": [
      {
        "risk": "页面结构变化",
        "probability": "high|medium|low",
        "impact": "high|medium|low",
        "mitigation": "使用多种定位策略"
      }
    ],
    "businessRisks": [
      {
        "risk": "数据不一致",
        "probability": "medium",
        "impact": "high",
        "mitigation": "增加验证步骤"
      }
    ],
    "operationalRisks": [
      {
        "risk": "系统不可用",
        "probability": "low",
        "impact": "high",
        "mitigation": "设置重试机制"
      }
    ]
  }
}
```

### 6. 执行策略

#### 策略制定
```json
{
  "executionStrategy": {
    "approach": "串行执行|并行执行|混合执行",
    "prioritization": "时间优先|准确性优先|资源优先",
    "errorHandling": "快速失败|最大重试|人工介入",
    "optimization": {
      "batchProcessing": true,
      "caching": true,
      "parallelism": 3
    }
  }
}
```

#### 检查点设置
```json
{
  "checkpoints": [
    {
      "id": "CP001",
      "afterTask": "ST003",
      "validations": ["验证项1", "验证项2"],
      "rollbackPoint": true,
      "saveState": true
    }
  ]
}
```

## 任务分析示例

### 示例1：商品上架任务

```json
{
  "analysis": {
    "understanding": {
      "task": "上架榮記豆腐麵食的外卖商品：菜遠牛肉飯",
      "breakdown": {
        "restaurant": "榮記豆腐麵食",
        "location": "官也街",
        "product": "菜遠牛肉飯",
        "action": "上架到外卖平台"
      }
    },
    "steps": [
      {
        "step": 1,
        "action": "登录商户后台",
        "critical": true
      },
      {
        "step": 2,
        "action": "搜索并进入指定门店",
        "details": "使用门店名称搜索"
      },
      {
        "step": 3,
        "action": "进入商品管理",
        "navigation": "商品管理 > 外卖商品管理"
      },
      {
        "step": 4,
        "action": "添加新商品",
        "data": {
          "name": "菜遠牛肉飯",
          "category": "主食类",
          "price": "待确定",
          "description": "待补充"
        }
      },
      {
        "step": 5,
        "action": "上架商品",
        "validation": "确认商品状态为'已上架'"
      }
    ],
    "requirements": {
      "missingInfo": ["商品价格", "商品描述", "商品图片"],
      "assumptions": ["商品分类为主食类", "默认规格为单份"]
    }
  }
}
```

### 示例2：批量数据导入

```json
{
  "analysis": {
    "understanding": {
      "task": "批量导入员工信息到HR系统",
      "dataVolume": "约500条记录",
      "source": "Excel文件"
    },
    "approach": {
      "method": "分批处理",
      "batchSize": 50,
      "validation": "每批次验证",
      "errorHandling": "记录错误，继续处理"
    },
    "phases": [
      {
        "phase": "准备阶段",
        "tasks": [
          "验证Excel文件格式",
          "检查必填字段完整性",
          "数据预处理和清洗"
        ]
      },
      {
        "phase": "执行阶段",
        "tasks": [
          "登录HR系统",
          "导航到批量导入页面",
          "上传文件并映射字段",
          "执行导入并监控进度"
        ]
      },
      {
        "phase": "验证阶段",
        "tasks": [
          "检查导入成功率",
          "验证数据准确性",
          "生成导入报告"
        ]
      }
    ]
  }
}
```

## 任务分析检查清单

### 分析完整性检查
- [ ] 是否完全理解任务目标？
- [ ] 是否识别所有必需信息？
- [ ] 是否考虑所有可能场景？
- [ ] 是否评估潜在风险？
- [ ] 是否制定应急方案？

### 可执行性检查
- [ ] 每个步骤是否明确可执行？
- [ ] 是否有明确的开始和结束条件？
- [ ] 依赖关系是否清晰？
- [ ] 资源需求是否满足？
- [ ] 时间估算是否合理？

### 质量保证检查
- [ ] 是否设置足够的验证点？
- [ ] 错误处理是否完善？
- [ ] 是否支持断点续传？
- [ ] 日志记录是否充分？
- [ ] 报告生成是否完整？

## 持续优化

### 分析模式库
收集和维护常见任务的分析模式，提高分析效率：

```json
{
  "patterns": {
    "dataEntry": {
      "characteristics": ["表单填写", "数据验证"],
      "commonSteps": ["登录", "导航", "填写", "提交", "验证"],
      "tipicalDuration": "5-10分钟"
    },
    "dataExtraction": {
      "characteristics": ["搜索查询", "数据收集"],
      "commonSteps": ["登录", "搜索", "筛选", "导出", "整理"],
      "tipicalDuration": "10-20分钟"
    },
    "statusUpdate": {
      "characteristics": ["状态变更", "流程推进"],
      "commonSteps": ["定位记录", "检查条件", "更新状态", "确认"],
      "tipicalDuration": "3-5分钟"
    }
  }
}
```

### 经验总结
1. **粒度控制**: 子任务不宜过细或过粗
2. **灵活性**: 保留动态调整的空间
3. **可追溯性**: 记录分析决策的依据
4. **复用性**: 提取通用的任务模式
5. **演进性**: 根据执行反馈优化分析