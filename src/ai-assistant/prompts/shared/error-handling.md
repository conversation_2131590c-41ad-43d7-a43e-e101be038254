# 错误处理指南

## 错误分类

### 1. 可恢复错误
这些错误通常是临时性的，可以通过重试或调整策略解决。

#### 网络错误
- **症状**: 页面加载超时、资源加载失败
- **处理**: 重试3次，每次间隔递增
- **示例**: `ERR_NETWORK_TIMEOUT`, `ERR_CONNECTION_RESET`

#### 元素定位错误
- **症状**: 找不到目标元素
- **处理**: 尝试备选定位方式，等待元素出现
- **示例**: `ElementNotFound`, `ElementNotInteractable`

#### 时序错误
- **症状**: 操作过快导致失败
- **处理**: 增加等待时间，降低操作速度
- **示例**: `ElementNotReady`, `AnimationNotComplete`

### 2. 不可恢复错误
这些错误需要人工介入或策略调整。

#### 认证错误
- **症状**: 登录失败、会话过期
- **处理**: 请求人工重新登录
- **示例**: `AuthenticationFailed`, `SessionExpired`

#### 权限错误
- **症状**: 无权限访问页面或执行操作
- **处理**: 记录问题，请求权限调整
- **示例**: `AccessDenied`, `InsufficientPrivileges`

#### 业务逻辑错误
- **症状**: 违反业务规则
- **处理**: 分析原因，请求补充信息
- **示例**: `InvalidBusinessData`, `WorkflowViolation`

### 3. 系统错误
这些是系统级的严重错误。

#### 资源耗尽
- **症状**: 内存不足、磁盘空间不足
- **处理**: 释放资源，必要时重启
- **示例**: `OutOfMemory`, `DiskSpaceFull`

#### 浏览器崩溃
- **症状**: 浏览器进程异常终止
- **处理**: 重启浏览器，恢复状态
- **示例**: `BrowserCrashed`, `TabUnresponsive`

## 错误处理策略

### 1. 重试策略

```javascript
const retryStrategy = {
  maxAttempts: 3,
  delays: [1000, 3000, 5000], // 递增延迟
  conditions: {
    network: true,      // 网络错误可重试
    timeout: true,      // 超时可重试
    element: true,      // 元素错误可重试
    auth: false,        // 认证错误不重试
    business: false     // 业务错误不重试
  }
};
```

### 2. 降级策略

当主要方法失败时，尝试备选方案：

```javascript
const fallbackStrategies = {
  elementLocation: [
    "byId",           // 优先使用ID
    "byTestId",       // 其次测试ID
    "byText",         // 再次文本内容
    "byPartialText",  // 部分文本
    "byXPath",        // 最后XPath
  ],
  interaction: [
    "click",          // 标准点击
    "forceClick",     // 强制点击
    "jsClick",        // JavaScript点击
    "keyboardEnter"   // 键盘回车
  ]
};
```

### 3. 熔断策略

防止连续失败导致的资源浪费：

```javascript
const circuitBreaker = {
  threshold: 5,         // 连续失败5次
  timeout: 60000,       // 熔断60秒
  halfOpenAttempts: 1,  // 半开状态尝试1次
  reset: () => {        // 重置条件
    // 成功执行一次即重置
  }
};
```

## 错误恢复流程

```mermaid
graph TD
    A[错误发生] --> B{错误类型?}
    B -->|可恢复| C[应用重试策略]
    B -->|不可恢复| D[请求人工介入]
    B -->|系统错误| E[系统级恢复]
    
    C --> F{重试成功?}
    F -->|是| G[继续执行]
    F -->|否| H[尝试降级策略]
    
    H --> I{降级成功?}
    I -->|是| G
    I -->|否| D
    
    D --> J[保存当前状态]
    J --> K[生成错误报告]
    K --> L[等待人工处理]
    
    E --> M[释放资源]
    M --> N[重启相关服务]
    N --> O[恢复执行状态]
```

## 错误日志规范

### 1. 错误日志格式

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "ERROR",
  "category": "ELEMENT_NOT_FOUND",
  "message": "无法找到登录按钮",
  "details": {
    "selector": "#login-button",
    "page": "https://example.com/login",
    "attempts": 3,
    "duration": 15000
  },
  "context": {
    "ticketId": "TICKET-123",
    "step": "用户登录",
    "agent": "ExecutorAgent"
  },
  "stack": "错误堆栈信息...",
  "suggestions": [
    "检查元素选择器是否正确",
    "确认页面是否完全加载"
  ]
}
```

### 2. 错误级别

- **CRITICAL**: 系统无法继续运行
- **ERROR**: 操作失败，需要处理
- **WARNING**: 潜在问题，需要关注
- **INFO**: 一般信息，用于调试

## 常见错误处理示例

### 1. 页面加载超时

```javascript
// 错误检测
if (pageLoadTime > timeout) {
  // 第一步：检查网络
  checkNetworkConnectivity();
  
  // 第二步：尝试刷新
  await page.reload({ waitUntil: 'networkidle' });
  
  // 第三步：降级到基本加载
  await page.goto(url, { waitUntil: 'domcontentloaded' });
  
  // 第四步：请求协助
  if (stillTimeout) {
    requestHumanHelp("页面持续加载超时");
  }
}
```

### 2. 动态元素处理

```javascript
// 等待元素出现
async function waitForDynamicElement(selector, options = {}) {
  const maxAttempts = options.maxAttempts || 10;
  const interval = options.interval || 1000;
  
  for (let i = 0; i < maxAttempts; i++) {
    try {
      // 尝试找到元素
      const element = await page.$(selector);
      if (element) return element;
      
      // 触发可能的懒加载
      await page.evaluate(() => window.scrollBy(0, 100));
      
      // 等待间隔
      await page.waitForTimeout(interval);
    } catch (e) {
      // 记录尝试
      console.log(`Attempt ${i + 1} failed`);
    }
  }
  
  throw new Error(`元素 ${selector} 在 ${maxAttempts} 次尝试后仍未出现`);
}
```

### 3. 会话恢复

```javascript
// 保存会话状态
async function saveSession() {
  const cookies = await page.cookies();
  const localStorage = await page.evaluate(() => ({...localStorage}));
  const sessionStorage = await page.evaluate(() => ({...sessionStorage}));
  
  return {
    url: page.url(),
    cookies,
    localStorage,
    sessionStorage,
    timestamp: Date.now()
  };
}

// 恢复会话状态
async function restoreSession(session) {
  // 恢复cookies
  await page.setCookie(...session.cookies);
  
  // 导航到保存的URL
  await page.goto(session.url);
  
  // 恢复存储
  await page.evaluate((storage) => {
    Object.entries(storage.localStorage).forEach(([key, value]) => {
      localStorage.setItem(key, value);
    });
  }, session);
}
```

## 人工介入指南

### 1. 请求人工介入的时机

- 连续失败超过阈值
- 遇到未知页面或流程
- 需要人工判断的业务决策
- 安全相关的操作确认

### 2. 人工介入请求格式

```json
{
  "interventionRequest": {
    "reason": "需要人工登录",
    "urgency": "high|medium|low",
    "context": {
      "currentPage": "登录页面",
      "lastAction": "尝试自动登录",
      "failureReason": "验证码识别失败"
    },
    "instructions": "请手动完成登录，然后点击继续",
    "timeout": 300000,
    "fallback": "超时后标记任务失败"
  }
}
```

### 3. 人工介入后的恢复

1. 保存人工操作前的状态
2. 等待人工完成操作
3. 验证人工操作的结果
4. 从中断点继续执行
5. 记录人工介入的详情

## 错误预防措施

### 1. 防御性编程

- 总是检查元素存在性
- 设置合理的超时时间
- 处理所有可能的异常
- 避免硬编码等待时间

### 2. 健壮性设计

- 使用多种定位策略
- 实现优雅降级
- 保持操作幂等性
- 设计可恢复的流程

### 3. 监控和预警

- 实时监控错误率
- 设置错误阈值告警
- 分析错误模式
- 持续优化策略

## 错误处理最佳实践

1. **快速失败**: 尽早检测和报告错误
2. **详细记录**: 保存足够的上下文信息
3. **用户友好**: 提供清晰的错误描述
4. **持续改进**: 从错误中学习和优化
5. **预防为主**: 主动避免已知问题