# 操作模式说明

## 核心操作模式

### 1. 观察-理解-决策-执行-验证循环

这是RPA系统的核心操作模式，每个操作都遵循这个循环：

```
观察(Observe) → 理解(Understand) → 决策(Decide) → 执行(Execute) → 验证(Verify)
     ↑                                                                      ↓
     ←────────────────────────── 循环 ──────────────────────────────────←
```

#### 观察阶段
- 获取页面当前状态
- 识别所有可交互元素
- 检测动态变化
- 收集上下文信息

#### 理解阶段
- 分析页面业务含义
- 判断当前流程位置
- 识别可执行操作
- 评估页面稳定性

#### 决策阶段
- 基于目标选择行动
- 考虑多种可能路径
- 评估风险和收益
- 制定执行计划

#### 执行阶段
- 精确定位目标元素
- 执行具体操作
- 处理异常情况
- 记录执行过程

#### 验证阶段
- 检查操作结果
- 对比预期效果
- 判断是否需要重试
- 决定下一步行动

### 2. 任务分解模式

将复杂任务分解为可管理的子任务：

```
复杂任务
    ├── 子任务1（独立可验证）
    ├── 子任务2（独立可验证）
    └── 子任务3（独立可验证）
```

**原则**：
- 每个子任务应该独立可验证
- 子任务之间的依赖关系要明确
- 失败的子任务应该可以单独重试
- 保持子任务的原子性

### 3. 错误恢复模式

```
尝试执行
    ├── 成功 → 继续
    └── 失败 → 分析失败原因
                  ├── 临时性错误 → 重试（调整策略）
                  ├── 可恢复错误 → 执行恢复操作
                  └── 不可恢复错误 → 请求人工介入
```

**恢复策略**：
- 增加等待时间
- 使用备选定位方法
- 刷新页面重试
- 调整执行顺序

## 特定场景操作模式

### 1. 登录场景模式

```
检测登录状态
    ├── 已登录 → 继续执行
    ├── 需要登录 → 等待自动登录（10秒）
    │                 ├── 自动登录成功 → 继续
    │                 └── 未自动登录 → 请求人工登录
    └── 会话过期 → 重新登录流程
```

### 2. 搜索和筛选模式

```
输入搜索条件
    ├── 简单搜索 → 输入关键词 → 点击搜索
    └── 高级搜索 → 填写多个条件 → 应用筛选
                         ↓
                    等待结果加载
                         ↓
                    验证搜索结果
```

### 3. 表单填写模式

```
分析表单结构
    ├── 识别必填字段
    ├── 理解字段含义
    └── 确定填写顺序
           ↓
    逐字段填写
        ├── 文本输入
        ├── 下拉选择
        ├── 日期选择
        └── 文件上传
           ↓
    表单验证
        ├── 前端验证通过 → 提交
        └── 验证失败 → 修正错误
```

### 4. 列表操作模式

```
定位目标项
    ├── 直接可见 → 执行操作
    └── 不可见 → 翻页/滚动查找
                     ↓
              找到目标 → 执行操作
                     ↓
              验证操作结果
```

### 5. 批量操作模式

```
识别批量操作需求
        ↓
选择操作模式
    ├── 全选模式 → 全选 → 批量操作
    └── 逐项模式 → 循环处理每一项
                         ↓
                    收集操作结果
                         ↓
                    生成批量报告
```

## 智能交互模式

### 1. 自动完成输入框

这是一种特殊的输入模式，需要特别处理：

```
点击输入框激活
        ↓
缓慢输入关键词（每个字符后暂停）
        ↓
等待下拉选项出现（1-2秒）
        ↓
识别匹配的选项
        ↓
点击选择目标选项
        ↓
验证输入框值已更新
```

**注意事项**：
- 不能直接填充完整值
- 必须等待下拉选项出现
- 选项可能需要滚动才能看到
- 选择后输入框值可能会改变格式

### 2. 多级联动选择

```
选择一级选项
    ↓
等待二级选项加载
    ↓
选择二级选项
    ↓
等待三级选项加载（如有）
    ↓
继续直到所有级别选择完成
```

### 3. 动态内容加载

```
执行触发动作（点击、滚动等）
        ↓
等待加载指示器出现
        ↓
等待加载指示器消失
        ↓
验证新内容已加载
        ↓
继续操作新内容
```

## 决策优先级

在面临多个可能的操作时，按以下优先级决策：

1. **安全性优先**：选择风险最小的操作
2. **成功率优先**：选择最可能成功的路径
3. **效率优先**：在安全的前提下选择最快路径
4. **可恢复性优先**：选择容易恢复的操作

## 等待策略

### 智能等待决策树

```
需要等待吗？
    ├── 页面加载中 → 等待加载完成（最多30秒）
    ├── 元素不可见 → 等待元素出现（最多10秒）
    ├── 动画进行中 → 等待动画结束（最多5秒）
    ├── 网络请求中 → 等待请求完成（最多20秒）
    └── 其他 → 默认等待（1-2秒）
```

### 等待时机

- **操作前**：确保页面稳定
- **操作后**：等待页面响应
- **状态变化**：等待过渡完成
- **错误恢复**：给系统恢复时间

## 最佳实践

### 1. 渐进式操作
- 先尝试简单方法
- 失败后再用复杂方法
- 保持操作的可追溯性

### 2. 防御性编程
- 总是验证操作结果
- 准备好失败处理
- 保存关键状态点

### 3. 用户体验
- 清晰的进度反馈
- 有意义的错误信息
- 可理解的决策解释

### 4. 性能考虑
- 避免不必要的等待
- 利用并行操作
- 缓存重复信息

## 通用操作原则

1. **不假设**：不假设页面结构、元素位置或操作结果
2. **要验证**：每个操作后都要验证结果
3. **可恢复**：设计可恢复的操作流程
4. **够智能**：根据实际情况调整策略
5. **有耐心**：给页面足够的响应时间
6. **会学习**：从成功和失败中积累经验