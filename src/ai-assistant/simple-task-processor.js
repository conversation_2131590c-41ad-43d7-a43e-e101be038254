const axios = require('axios');
const logger = require('./src/utils/logger');

class SimpleTaskProcessor {
    constructor() {
        this.mainModel = 'qwen-plus';
        this.mcpUrl = 'http://127.0.0.1:8931';
        this.isProcessing = false;
    }

    async processTicket(ticket) {
        if (this.isProcessing) {
            logger.info('任务处理器忙碌中，跳过工单:', ticket.id);
            return;
        }

        this.isProcessing = true;
        logger.info(`🚀 开始处理工单: ${ticket.id} - ${ticket.title}`);

        try {
            // 简单的任务处理逻辑
            await this.updateTicketStatus(ticket.id, '处理中');
            
            // 1. 导航到正确的BD商户后台
            await this.navigateToTarget();
            
            // 2. 等待用户登录
            await this.waitForLogin();
            
            // 3. 执行具体任务
            const result = await this.executeTask(ticket);
            
            // 4. 更新工单状态
            await this.updateTicketStatus(ticket.id, '已完成', {
                summary: result.summary,
                notes: result.notes
            });
            
            logger.info(`✅ 工单处理完成: ${ticket.id}`);
            
        } catch (error) {
            logger.error(`❌ 工单处理失败: ${ticket.id}`, error);
            await this.updateTicketStatus(ticket.id, '处理失败', {
                notes: `处理失败: ${error.message}`
            });
        } finally {
            this.isProcessing = false;
        }
    }

    async navigateToTarget() {
        logger.info('🌐 导航到BD商户后台...');
        
        try {
            // 首先尝试主URL
            await this.callMCP('browser_navigate', {
                url: 'https://uat-merchant.aomiapp.com/#/bdlogin'
            });
            
            await this.sleep(3000);
            
            // 检查页面是否加载成功
            const snapshot = await this.callMCP('browser_snapshot', {});
            
            if (snapshot.content && snapshot.content[0] && snapshot.content[0].text.length < 50) {
                logger.warn('主URL页面空白，尝试备用URL...');
                await this.callMCP('browser_navigate', {
                    url: 'https://uat-merchant.aomiapp.com/#/select?noDirect=1'
                });
                await this.sleep(3000);
            }
            
            logger.info('✅ 页面导航完成');
            
        } catch (error) {
            logger.error('❌ 页面导航失败:', error);
            throw error;
        }
    }

    async waitForLogin() {
        logger.info('⏳ 等待用户登录...');
        
        let attempts = 0;
        const maxAttempts = 60; // 最多等待5分钟
        
        while (attempts < maxAttempts) {
            try {
                const snapshot = await this.callMCP('browser_snapshot', {});
                const pageContent = snapshot.content[0]?.text || '';
                
                // 检查是否已登录（页面包含商户管理相关内容）
                if (pageContent.includes('商户') || pageContent.includes('门店') || pageContent.includes('用户')) {
                    logger.info('✅ 检测到用户已登录');
                    return;
                }
                
                await this.sleep(5000);
                attempts++;
                
            } catch (error) {
                logger.warn('登录检查失败，继续等待...', error.message);
                await this.sleep(5000);
                attempts++;
            }
        }
        
        throw new Error('等待登录超时，请手动登录后重试');
    }

    async executeTask(ticket) {
        logger.info('🎯 开始执行具体任务...');
        
        // 这里实现具体的任务逻辑
        // 暂时返回一个简单的结果
        return {
            summary: '任务执行完成',
            notes: `已处理工单: ${ticket.title}`
        };
    }

    async callMCP(method, params) {
        try {
            const response = await axios.post(this.mcpUrl, {
                jsonrpc: '2.0',
                id: Date.now(),
                method: method,
                params: params
            });
            
            if (response.data.error) {
                throw new Error(response.data.error.message);
            }
            
            return response.data.result;
            
        } catch (error) {
            logger.error(`MCP调用失败: ${method}`, error.message);
            throw error;
        }
    }

    async updateTicketStatus(ticketId, status, data = {}) {
        try {
            await axios.patch(`http://localhost:3001/api/tickets/${ticketId}/status`, {
                status: status,
                ...data
            });
            logger.info(`✅ 工单状态更新: ${ticketId} -> ${status}`);
        } catch (error) {
            logger.error(`❌ 工单状态更新失败: ${ticketId}`, error);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 兼容性方法
    getQueueSize() {
        return 0;
    }

    getStatus() {
        return {
            isProcessing: this.isProcessing,
            queueSize: 0,
            model: this.mainModel
        };
    }
}

module.exports = SimpleTaskProcessor;
