const dbConnection = require('../database/ensure-connected');

/**
 * 工单管理器 - 负责工单的状态管理和生命周期
 */
class TicketManager {
    constructor() {
        this.db = dbConnection;
        
        // 工单状态定义
        this.STATUSES = {
            PENDING: '待开始',
            QUEUED: '排队中', 
            PROCESSING: '处理中',
            COMPLETED: '已完成',
            NEED_INFO: '待补充信息'
        };
        
        // 优先级定义
        this.PRIORITIES = {
            HIGH: 2,
            MEDIUM: 1,
            LOW: 0
        };
    }

    /**
     * 创建工单
     */
    async createTicket(data) {
        try {
            const ticketData = {
                ticket_number: this.generateTicketNumber(),
                title: data.title,
                content: data.content,
                priority: this.mapPriorityToNumber(data.priority || 'medium'),
                status: this.STATUSES.PENDING,
                task_list: null,
                progress: 0,
                report: null,
                queue_priority: null
            };

            const result = await this.db.run(
                `INSERT INTO tickets (ticket_number, title, content, priority, status, task_list, progress, report, queue_priority)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    ticketData.ticket_number,
                    ticketData.title,
                    ticketData.content,
                    ticketData.priority,
                    ticketData.status,
                    ticketData.task_list,
                    ticketData.progress,
                    ticketData.report,
                    ticketData.queue_priority
                ]
            );

            const ticket = await this.getTicket(result.lastID);
            console.log(`工单创建成功: ${ticket.ticket_number}`);
            
            return ticket;
        } catch (error) {
            console.error('创建工单失败:', error);
            throw error;
        }
    }

    /**
     * 获取工单
     */
    async getTicket(ticketId) {
        try {
            const ticket = await this.db.get(
                'SELECT * FROM tickets WHERE id = ?',
                [ticketId]
            );
            
            if (ticket && ticket.task_list) {
                try {
                    ticket.task_list = JSON.parse(ticket.task_list);
                } catch (e) {
                    ticket.task_list = null;
                }
            }
            
            return ticket;
        } catch (error) {
            console.error('获取工单失败:', error);
            throw error;
        }
    }

    /**
     * 更新工单状态
     */
    async updateTicketStatus(ticketId, status, additionalData = {}) {
        try {
            const updateFields = ['status = ?', 'updated_at = CURRENT_TIMESTAMP'];
            const updateValues = [status];

            // 根据状态添加额外字段
            if (status === this.STATUSES.COMPLETED && !additionalData.completed_at) {
                updateFields.push('completed_at = CURRENT_TIMESTAMP');
            }

            // 添加其他字段
            Object.keys(additionalData).forEach(key => {
                if (key !== 'status') {
                    updateFields.push(`${key} = ?`);
                    updateValues.push(additionalData[key]);
                }
            });

            updateValues.push(ticketId);

            await this.db.run(
                `UPDATE tickets SET ${updateFields.join(', ')} WHERE id = ?`,
                updateValues
            );

            console.log(`工单状态更新: ID=${ticketId}, 状态=${status}`);
            return await this.getTicket(ticketId);
        } catch (error) {
            console.error('更新工单状态失败:', error);
            throw error;
        }
    }

    /**
     * 更新任务清单
     */
    async updateTaskList(ticketId, taskList) {
        try {
            await this.db.run(
                'UPDATE tickets SET task_list = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [JSON.stringify(taskList), ticketId]
            );
            
            console.log(`任务清单更新: ID=${ticketId}`);
            return await this.getTicket(ticketId);
        } catch (error) {
            console.error('更新任务清单失败:', error);
            throw error;
        }
    }

    /**
     * 更新进度
     */
    async updateProgress(ticketId, progress) {
        try {
            await this.db.run(
                'UPDATE tickets SET progress = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [progress, ticketId]
            );
            
            console.log(`进度更新: ID=${ticketId}, 进度=${progress}%`);
            return await this.getTicket(ticketId);
        } catch (error) {
            console.error('更新进度失败:', error);
            throw error;
        }
    }

    /**
     * 更新报告
     */
    async updateReport(ticketId, report) {
        try {
            await this.db.run(
                'UPDATE tickets SET report = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [report, ticketId]
            );
            
            console.log(`报告更新: ID=${ticketId}`);
            return await this.getTicket(ticketId);
        } catch (error) {
            console.error('更新报告失败:', error);
            throw error;
        }
    }

    /**
     * 获取待开始的工单列表
     */
    async getPendingTickets(limit = 10) {
        try {
            return await this.db.all(
                `SELECT * FROM tickets 
                 WHERE status = ? 
                 ORDER BY priority DESC, created_at ASC 
                 LIMIT ?`,
                [this.STATUSES.PENDING, limit]
            );
        } catch (error) {
            console.error('获取待开始工单失败:', error);
            throw error;
        }
    }

    /**
     * 获取工单列表（分页）
     */
    async getTickets(options = {}) {
        try {
            const {
                page = 1,
                pageSize = 20,
                status = null,
                priority = null,
                search = null
            } = options;

            let whereConditions = [];
            let params = [];

            if (status) {
                whereConditions.push('status = ?');
                params.push(status);
            }

            if (priority !== null) {
                whereConditions.push('priority = ?');
                params.push(priority);
            }

            if (search) {
                whereConditions.push('(title LIKE ? OR content LIKE ? OR ticket_number LIKE ?)');
                params.push(`%${search}%`, `%${search}%`, `%${search}%`);
            }

            const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
            
            // 获取总数
            const countResult = await this.db.get(
                `SELECT COUNT(*) as total FROM tickets ${whereClause}`,
                params
            );

            // 获取分页数据
            const offset = (page - 1) * pageSize;
            params.push(pageSize, offset);

            const tickets = await this.db.all(
                `SELECT * FROM tickets ${whereClause} 
                 ORDER BY created_at DESC 
                 LIMIT ? OFFSET ?`,
                params
            );

            return {
                tickets,
                total: countResult.total,
                page,
                pageSize,
                totalPages: Math.ceil(countResult.total / pageSize)
            };
        } catch (error) {
            console.error('获取工单列表失败:', error);
            throw error;
        }
    }

    /**
     * 生成工单号
     */
    generateTicketNumber() {
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
        const timeStr = now.getTime().toString().slice(-6);
        return `WO-${dateStr}-${timeStr}`;
    }

    /**
     * 映射优先级文本到数字
     */
    mapPriorityToNumber(priority) {
        const priorityMap = {
            'high': this.PRIORITIES.HIGH,
            'medium': this.PRIORITIES.MEDIUM,
            'low': this.PRIORITIES.LOW,
            '高': this.PRIORITIES.HIGH,
            '中': this.PRIORITIES.MEDIUM,
            '低': this.PRIORITIES.LOW
        };
        
        return priorityMap[priority] || this.PRIORITIES.MEDIUM;
    }

    /**
     * 映射优先级数字到文本
     */
    mapPriorityToText(priority) {
        const priorityMap = {
            [this.PRIORITIES.HIGH]: '高',
            [this.PRIORITIES.MEDIUM]: '中',
            [this.PRIORITIES.LOW]: '低'
        };
        
        return priorityMap[priority] || '中';
    }
}

module.exports = TicketManager;
