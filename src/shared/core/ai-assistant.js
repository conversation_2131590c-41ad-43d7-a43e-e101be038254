const TicketManager = require('./ticket-manager');
const MessageQueue = require('./message-queue');
const dbConnection = require('../database/connection');

/**
 * AI助手核心 - 简化的工单处理逻辑
 */
class AIAssistant {
    constructor() {
        this.ticketManager = new TicketManager();
        this.messageQueue = new MessageQueue();
        this.db = dbConnection;
        this.isProcessing = false;
        this.processingInterval = null;
        
        // 系统指引缓存
        this.systemGuides = new Map();
        
        // 工单状态
        this.STATUSES = {
            PENDING: '待开始',
            QUEUED: '排队中',
            PROCESSING: '处理中',
            COMPLETED: '已完成',
            NEED_INFO: '待补充信息'
        };
    }

    /**
     * 启动AI助手
     */
    async start() {
        try {
            console.log('启动AI助手...');

            // 确保数据库连接
            if (!this.db.isConnected) {
                await this.db.connect();
            }

            // 加载系统指引
            await this.loadSystemGuides();

            // 开始处理工单
            await this.startProcessing();

            console.log('AI助手启动成功');
        } catch (error) {
            console.error('启动AI助手失败:', error);
            throw error;
        }
    }

    /**
     * 停止AI助手
     */
    stop() {
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
        }
        console.log('AI助手已停止');
    }

    /**
     * 开始处理工单
     */
    async startProcessing() {
        // 立即处理一次
        await this.processNextTicket();
        
        // 定期检查新工单（每10秒）
        this.processingInterval = setInterval(async () => {
            await this.processNextTicket();
        }, 10000);
    }

    /**
     * 处理下一个工单
     */
    async processNextTicket() {
        if (this.isProcessing) {
            return; // 避免重复处理
        }

        try {
            this.isProcessing = true;
            
            // 从队列获取工单
            const queueItem = await this.messageQueue.dequeue();
            
            if (!queueItem) {
                return; // 没有待处理的工单
            }

            console.log(`开始处理工单: ${queueItem.ticket.ticket_number}`);
            
            // 处理工单
            await this.processTicket(queueItem);
            
        } catch (error) {
            console.error('处理工单失败:', error);
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 处理单个工单
     */
    async processTicket(queueItem) {
        try {
            const { queueId, ticketId, ticket } = queueItem;
            
            // 分析工单内容
            const analysis = await this.analyzeTicketContent(ticket.content);
            
            if (analysis.needsMoreInfo) {
                // 需要补充信息
                await this.handleNeedMoreInfo(ticketId, analysis);
            } else {
                // 信息完整，开始执行
                await this.executeTicket(ticketId, ticket, analysis);
            }
            
            // 完成队列项目
            await this.messageQueue.completeQueueItem(queueId, true);
            
        } catch (error) {
            console.error(`处理工单失败: ${queueItem.ticket.ticket_number}`, error);
            
            // 标记队列项目失败
            await this.messageQueue.completeQueueItem(queueItem.queueId, false);
            
            // 更新工单状态为需要补充信息
            await this.ticketManager.updateTicketStatus(queueItem.ticketId, this.STATUSES.NEED_INFO, {
                report: `处理失败: ${error.message}`
            });
        }
    }

    /**
     * 分析工单内容
     */
    async analyzeTicketContent(content) {
        try {
            // 匹配系统指引
            const guide = this.matchSystemGuide(content);
            
            if (!guide) {
                return {
                    needsMoreInfo: true,
                    missingInfo: ['无法识别工单类型，请提供更详细的操作说明'],
                    guide: null
                };
            }

            // 检查必需信息
            const missingInfo = this.checkRequiredFields(content, guide);
            
            return {
                needsMoreInfo: missingInfo.length > 0,
                missingInfo: missingInfo,
                guide: guide,
                taskList: missingInfo.length === 0 ? this.generateTaskList(content, guide) : null
            };
            
        } catch (error) {
            console.error('分析工单内容失败:', error);
            return {
                needsMoreInfo: true,
                missingInfo: ['分析工单内容时发生错误'],
                guide: null
            };
        }
    }

    /**
     * 匹配系统指引
     */
    matchSystemGuide(content) {
        const contentLower = content.toLowerCase();
        
        for (const [name, guide] of this.systemGuides) {
            const keywords = JSON.parse(guide.keywords);
            const matchCount = keywords.filter(keyword => 
                contentLower.includes(keyword.toLowerCase())
            ).length;
            
            // 如果匹配到一半以上的关键词，认为匹配
            if (matchCount >= Math.ceil(keywords.length / 2)) {
                return guide;
            }
        }
        
        // 默认返回通用指引
        return this.systemGuides.get('通用浏览器操作指引');
    }

    /**
     * 检查必需字段
     */
    checkRequiredFields(content, guide) {
        const requiredFields = JSON.parse(guide.required_fields);
        const missingInfo = [];
        
        for (const field of requiredFields) {
            if (!this.containsFieldInfo(content, field)) {
                missingInfo.push(`请提供${field}`);
            }
        }
        
        return missingInfo;
    }

    /**
     * 检查内容是否包含字段信息
     */
    containsFieldInfo(content, field) {
        const fieldKeywords = {
            '门店名称': ['门店', '店铺', '商店'],
            '商品名称': ['商品', '产品', '菜品'],
            '商品信息': ['商品', '产品', '菜品', '信息', '详情'],
            '价格': ['价格', '金额', '费用'],
            '更新内容': ['更新', '修改', '变更'],
            '目标网站': ['网站', '网址', 'url', 'http'],
            '操作描述': ['操作', '步骤', '流程']
        };
        
        const keywords = fieldKeywords[field] || [field];
        const contentLower = content.toLowerCase();
        
        return keywords.some(keyword => contentLower.includes(keyword.toLowerCase()));
    }

    /**
     * 生成任务清单
     */
    generateTaskList(content, guide) {
        try {
            const template = JSON.parse(guide.template);
            const steps = template.steps || [];
            
            return {
                title: `${guide.name}任务清单`,
                steps: steps.map((step, index) => ({
                    id: index + 1,
                    description: step,
                    status: 'pending', // pending, processing, completed, failed
                    startTime: null,
                    endTime: null,
                    result: null,
                    screenshot: null
                })),
                totalSteps: steps.length,
                completedSteps: 0,
                progress: 0
            };
        } catch (error) {
            console.error('生成任务清单失败:', error);
            return {
                title: '基础任务清单',
                steps: [
                    {
                        id: 1,
                        description: '分析工单内容',
                        status: 'completed',
                        result: '已完成内容分析'
                    },
                    {
                        id: 2,
                        description: '执行指定操作',
                        status: 'pending'
                    }
                ],
                totalSteps: 2,
                completedSteps: 1,
                progress: 50
            };
        }
    }

    /**
     * 处理需要补充信息的情况
     */
    async handleNeedMoreInfo(ticketId, analysis) {
        try {
            const infoRequest = `请补充以下信息：\n${analysis.missingInfo.map(info => `• ${info}`).join('\n')}`;
            
            await this.ticketManager.updateTicketStatus(ticketId, this.STATUSES.NEED_INFO, {
                report: infoRequest
            });
            
            console.log(`工单需要补充信息: ID=${ticketId}`);
            
        } catch (error) {
            console.error('处理补充信息失败:', error);
            throw error;
        }
    }

    /**
     * 执行工单
     */
    async executeTicket(ticketId, ticket, analysis) {
        try {
            // 更新任务清单
            await this.ticketManager.updateTaskList(ticketId, analysis.taskList);
            
            // 模拟执行过程（后续会集成Playwright MCP）
            await this.simulateExecution(ticketId, analysis.taskList);
            
            // 生成报告
            const report = this.generateReport(ticket, analysis.taskList);
            
            // 更新工单为已完成
            await this.ticketManager.updateTicketStatus(ticketId, this.STATUSES.COMPLETED, {
                progress: 100,
                report: report
            });
            
            console.log(`工单执行完成: ${ticket.ticket_number}`);
            
        } catch (error) {
            console.error('执行工单失败:', error);
            throw error;
        }
    }

    /**
     * 模拟执行过程（临时实现）
     */
    async simulateExecution(ticketId, taskList) {
        for (let i = 0; i < taskList.steps.length; i++) {
            const step = taskList.steps[i];
            
            // 更新步骤状态为处理中
            step.status = 'processing';
            step.startTime = new Date().toISOString();
            
            await this.ticketManager.updateTaskList(ticketId, taskList);
            await this.ticketManager.updateProgress(ticketId, Math.round((i / taskList.steps.length) * 100));
            
            // 模拟处理时间
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 更新步骤状态为已完成
            step.status = 'completed';
            step.endTime = new Date().toISOString();
            step.result = `${step.description} - 执行成功`;
            
            taskList.completedSteps = i + 1;
            taskList.progress = Math.round((taskList.completedSteps / taskList.totalSteps) * 100);
            
            await this.ticketManager.updateTaskList(ticketId, taskList);
            await this.ticketManager.updateProgress(ticketId, taskList.progress);
        }
    }

    /**
     * 生成报告
     */
    generateReport(ticket, taskList) {
        const completedSteps = taskList.steps.filter(step => step.status === 'completed');
        const failedSteps = taskList.steps.filter(step => step.status === 'failed');
        
        let report = `# 工单执行报告\n\n`;
        report += `**工单标题**: ${ticket.title}\n`;
        report += `**执行时间**: ${new Date().toLocaleString()}\n`;
        report += `**执行结果**: ${failedSteps.length === 0 ? '成功' : '部分失败'}\n`;
        report += `**完成步骤**: ${completedSteps.length}/${taskList.totalSteps}\n\n`;
        
        report += `## 执行步骤详情\n\n`;
        taskList.steps.forEach((step, index) => {
            const statusIcon = {
                'completed': '✅',
                'failed': '❌',
                'processing': '🔄',
                'pending': '⏳'
            }[step.status] || '❓';
            
            report += `${index + 1}. ${statusIcon} ${step.description}\n`;
            if (step.result) {
                report += `   结果: ${step.result}\n`;
            }
            report += `\n`;
        });
        
        if (failedSteps.length === 0) {
            report += `## 总结\n\n所有任务步骤已成功完成。`;
        } else {
            report += `## 总结\n\n执行过程中遇到 ${failedSteps.length} 个失败步骤，请检查具体错误信息。`;
        }
        
        return report;
    }

    /**
     * 加载系统指引
     */
    async loadSystemGuides() {
        try {
            const guides = await this.db.all('SELECT * FROM system_guides');
            
            this.systemGuides.clear();
            guides.forEach(guide => {
                this.systemGuides.set(guide.name, guide);
            });
            
            console.log(`加载了 ${guides.length} 个系统指引`);
        } catch (error) {
            console.error('加载系统指引失败:', error);
            throw error;
        }
    }
}

module.exports = AIAssistant;
