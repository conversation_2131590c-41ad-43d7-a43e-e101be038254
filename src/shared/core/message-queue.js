const dbConnection = require('../database/ensure-connected');
const TicketManager = require('./ticket-manager');

/**
 * 消息队列系统 - 管理工单的队列处理
 */
class MessageQueue {
    constructor() {
        this.db = dbConnection;
        this.ticketManager = new TicketManager();
        this.maxQueueSize = 10; // 最大队列大小
        this.isProcessing = false;
        this.processingInterval = null;
        
        // 队列状态
        this.QUEUE_STATUSES = {
            QUEUED: 'queued',
            PROCESSING: 'processing', 
            COMPLETED: 'completed'
        };
    }

    /**
     * 启动队列管理
     */
    async start() {
        try {
            console.log('启动消息队列管理...');
            
            // 初始队列检查
            await this.manageQueue();
            
            // 定期检查队列（每30秒）
            this.processingInterval = setInterval(async () => {
                await this.manageQueue();
            }, 30000);
            
            console.log('消息队列管理启动成功');
        } catch (error) {
            console.error('启动消息队列失败:', error);
            throw error;
        }
    }

    /**
     * 停止队列管理
     */
    stop() {
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
        }
        console.log('消息队列管理已停止');
    }

    /**
     * 队列管理 - 自动将待开始工单加入队列
     */
    async manageQueue() {
        if (this.isProcessing) {
            return; // 避免重复处理
        }

        try {
            this.isProcessing = true;
            
            // 获取当前队列大小
            const currentQueueSize = await this.getQueueSize();
            
            if (currentQueueSize >= this.maxQueueSize) {
                console.log(`队列已满 (${currentQueueSize}/${this.maxQueueSize})`);
                return;
            }

            // 计算可添加的工单数量
            const availableSlots = this.maxQueueSize - currentQueueSize;
            
            // 获取待开始的工单
            const pendingTickets = await this.ticketManager.getPendingTickets(availableSlots);
            
            if (pendingTickets.length === 0) {
                return;
            }

            console.log(`发现 ${pendingTickets.length} 个待处理工单，开始加入队列...`);

            // 将工单加入队列
            for (const ticket of pendingTickets) {
                await this.enqueue(ticket.id);
            }

        } catch (error) {
            console.error('队列管理失败:', error);
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 将工单加入队列
     */
    async enqueue(ticketId) {
        try {
            const ticket = await this.ticketManager.getTicket(ticketId);
            if (!ticket) {
                throw new Error(`工单不存在: ${ticketId}`);
            }

            // 计算优先级分数 (优先级 * 1000 - 创建时间戳，确保高优先级和早创建的排在前面)
            const createdTime = new Date(ticket.created_at).getTime();
            const priorityScore = (ticket.priority * 1000) - (createdTime % 1000000);

            // 添加到队列表
            await this.db.run(
                `INSERT INTO message_queue (ticket_id, priority_score, status)
                 VALUES (?, ?, ?)`,
                [ticketId, priorityScore, this.QUEUE_STATUSES.QUEUED]
            );

            // 更新工单状态为排队中
            await this.ticketManager.updateTicketStatus(ticketId, this.ticketManager.STATUSES.QUEUED);

            console.log(`工单加入队列: ${ticket.ticket_number} (优先级分数: ${priorityScore})`);
            
        } catch (error) {
            console.error('工单加入队列失败:', error);
            throw error;
        }
    }

    /**
     * 从队列获取下一个工单
     */
    async dequeue() {
        try {
            // 获取优先级最高的排队工单
            const queueItem = await this.db.get(
                `SELECT mq.*, t.ticket_number, t.title 
                 FROM message_queue mq
                 JOIN tickets t ON mq.ticket_id = t.id
                 WHERE mq.status = ?
                 ORDER BY mq.priority_score DESC, mq.queued_at ASC
                 LIMIT 1`,
                [this.QUEUE_STATUSES.QUEUED]
            );

            if (!queueItem) {
                return null;
            }

            // 更新队列状态为处理中
            await this.db.run(
                `UPDATE message_queue 
                 SET status = ?, started_at = CURRENT_TIMESTAMP 
                 WHERE id = ?`,
                [this.QUEUE_STATUSES.PROCESSING, queueItem.id]
            );

            // 更新工单状态为处理中
            await this.ticketManager.updateTicketStatus(queueItem.ticket_id, this.ticketManager.STATUSES.PROCESSING);

            console.log(`从队列获取工单: ${queueItem.ticket_number}`);
            
            return {
                queueId: queueItem.id,
                ticketId: queueItem.ticket_id,
                ticket: await this.ticketManager.getTicket(queueItem.ticket_id)
            };

        } catch (error) {
            console.error('从队列获取工单失败:', error);
            throw error;
        }
    }

    /**
     * 完成队列项目
     */
    async completeQueueItem(queueId, success = true) {
        try {
            await this.db.run(
                `UPDATE message_queue 
                 SET status = ?, completed_at = CURRENT_TIMESTAMP 
                 WHERE id = ?`,
                [this.QUEUE_STATUSES.COMPLETED, queueId]
            );

            console.log(`队列项目完成: ID=${queueId}, 成功=${success}`);
            
        } catch (error) {
            console.error('完成队列项目失败:', error);
            throw error;
        }
    }

    /**
     * 获取队列大小
     */
    async getQueueSize() {
        try {
            const result = await this.db.get(
                'SELECT COUNT(*) as count FROM message_queue WHERE status IN (?, ?)',
                [this.QUEUE_STATUSES.QUEUED, this.QUEUE_STATUSES.PROCESSING]
            );
            
            return result.count || 0;
        } catch (error) {
            console.error('获取队列大小失败:', error);
            return 0;
        }
    }

    /**
     * 获取队列状态
     */
    async getQueueStatus() {
        try {
            const stats = await this.db.all(
                `SELECT status, COUNT(*) as count 
                 FROM message_queue 
                 GROUP BY status`
            );

            const statusMap = {
                [this.QUEUE_STATUSES.QUEUED]: 0,
                [this.QUEUE_STATUSES.PROCESSING]: 0,
                [this.QUEUE_STATUSES.COMPLETED]: 0
            };

            stats.forEach(stat => {
                statusMap[stat.status] = stat.count;
            });

            return {
                queued: statusMap[this.QUEUE_STATUSES.QUEUED],
                processing: statusMap[this.QUEUE_STATUSES.PROCESSING],
                completed: statusMap[this.QUEUE_STATUSES.COMPLETED],
                total: statusMap[this.QUEUE_STATUSES.QUEUED] + statusMap[this.QUEUE_STATUSES.PROCESSING]
            };
        } catch (error) {
            console.error('获取队列状态失败:', error);
            return {
                queued: 0,
                processing: 0,
                completed: 0,
                total: 0
            };
        }
    }

    /**
     * 清理已完成的队列项目（可选，用于维护）
     */
    async cleanupCompletedItems(olderThanDays = 7) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

            const result = await this.db.run(
                `DELETE FROM message_queue 
                 WHERE status = ? AND completed_at < ?`,
                [this.QUEUE_STATUSES.COMPLETED, cutoffDate.toISOString()]
            );

            console.log(`清理了 ${result.changes} 个已完成的队列项目`);
            return result.changes;
        } catch (error) {
            console.error('清理队列项目失败:', error);
            return 0;
        }
    }
}

module.exports = MessageQueue;
