/**
 * 确保数据库连接的辅助模块
 * 在需要使用数据库的地方引入此模块而不是直接引入connection
 */

const dbConnection = require('./connection');

let connectionPromise = null;

/**
 * 获取已连接的数据库实例
 * @returns {Promise<DatabaseConnection>}
 */
async function getConnectedDb() {
    if (!connectionPromise) {
        connectionPromise = dbConnection.connect().then(() => dbConnection);
    }
    return connectionPromise;
}

/**
 * 确保数据库已连接的包装器
 */
const ensureConnected = {
    async run(...args) {
        const db = await getConnectedDb();
        return db.run(...args);
    },
    
    async get(...args) {
        const db = await getConnectedDb();
        return db.get(...args);
    },
    
    async all(...args) {
        const db = await getConnectedDb();
        return db.all(...args);
    },
    
    async close() {
        const db = await getConnectedDb();
        return db.close();
    }
};

module.exports = ensureConnected;