const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class DatabaseConnection {
    constructor() {
        this.db = null;
        // 统一使用环境变量中的数据库路径，确保所有服务访问同一数据库
        const dbPath = process.env.DATABASE_PATH || './data/rpa_platform.db';
        // 找到项目根目录（包含package.json的目录）
        let projectRoot = __dirname;
        while (projectRoot !== '/' && !fs.existsSync(path.join(projectRoot, 'package.json'))) {
            projectRoot = path.dirname(projectRoot);
        }
        this.dbPath = path.isAbsolute(dbPath) ? dbPath : path.resolve(projectRoot, dbPath);
        this.isConnected = false;
        
        // 创建数据库目录（如果不存在）
        const dbDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
        }
        
        console.log(`数据库路径配置: ${this.dbPath}`);
    }

    /**
     * 初始化数据库连接
     */
    async connect() {
        return new Promise((resolve, reject) => {
            try {
                // 确保数据库目录存在
                const dbDir = path.dirname(this.dbPath);
                if (!fs.existsSync(dbDir)) {
                    fs.mkdirSync(dbDir, { recursive: true });
                }

                // 创建数据库连接
                this.db = new sqlite3.Database(this.dbPath, async (err) => {
                    if (err) {
                        console.error('数据库连接失败:', err.message);
                        reject(err);
                        return;
                    }

                    console.log('数据库连接成功:', this.dbPath);
                    this.isConnected = true;

                    try {
                        // 启用外键约束
                        await this.run('PRAGMA foreign_keys = ON');
                        resolve(this.db);
                    } catch (pragmaError) {
                        console.error('设置PRAGMA失败:', pragmaError);
                        reject(pragmaError);
                    }
                });
            } catch (error) {
                console.error('数据库初始化失败:', error);
                reject(error);
            }
        });
    }

    /**
     * 执行SQL语句(无返回结果)
     */
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.isConnected) {
                reject(new Error('数据库未连接'));
                return;
            }

            this.db.run(sql, params, function(err) {
                if (err) {
                    console.error('SQL执行失败:', err.message);
                    console.error('SQL语句:', sql);
                    console.error('参数:', params);
                    reject(err);
                } else {
                    resolve({
                        lastID: this.lastID,
                        changes: this.changes
                    });
                }
            });
        });
    }

    /**
     * 查询单条记录
     */
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.isConnected) {
                reject(new Error('数据库未连接'));
                return;
            }

            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('查询失败:', err.message);
                    console.error('SQL语句:', sql);
                    console.error('参数:', params);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * 查询多条记录
     */
    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.isConnected) {
                reject(new Error('数据库未连接'));
                return;
            }

            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('查询失败:', err.message);
                    console.error('SQL语句:', sql);
                    console.error('参数:', params);
                    reject(err);
                } else {
                    resolve(rows || []);
                }
            });
        });
    }

    /**
     * 执行事务
     */
    async transaction(operations) {
        try {
            await this.run('BEGIN TRANSACTION');

            for (const operation of operations) {
                if (typeof operation === 'function') {
                    await operation(this);
                } else if (operation.sql) {
                    await this.run(operation.sql, operation.params);
                }
            }

            await this.run('COMMIT');
            return true;
        } catch (error) {
            await this.run('ROLLBACK');
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    close() {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve();
                return;
            }

            this.db.close((err) => {
                if (err) {
                    console.error('关闭数据库连接失败:', err.message);
                    reject(err);
                } else {
                    console.log('数据库连接已关闭');
                    this.isConnected = false;
                    resolve();
                }
            });
        });
    }

    /**
     * 初始化数据库表结构
     */
    async initializeSchema() {
        try {
            const schemaPath = path.join(__dirname, '../../../config/database.sql');
            const schema = fs.readFileSync(schemaPath, 'utf8');

            // 分割SQL语句并执行
            const statements = schema.split(';').filter(stmt => stmt.trim());

            for (const statement of statements) {
                if (statement.trim()) {
                    await this.run(statement.trim());
                }
            }

            console.log('数据库表结构初始化完成');
        } catch (error) {
            console.error('数据库表结构初始化失败:', error);
            throw error;
        }
    }

    /**
     * 检查数据库健康状态
     */
    async healthCheck() {
        try {
            const result = await this.get('SELECT 1 as health');
            return result && result.health === 1;
        } catch (error) {
            console.error('数据库健康检查失败:', error);
            return false;
        }
    }
}

// 创建单例实例
const dbConnection = new DatabaseConnection();

module.exports = dbConnection;