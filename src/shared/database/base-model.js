const db = require('./connection');

class BaseModel {
    constructor(tableName) {
        this.tableName = tableName;
        this.db = db;
    }

    /**
     * 创建记录
     */
    async create(data) {
        try {
            // 自动添加创建时间和更新时间
            const now = new Date().toISOString();
            if (!data.created_at) {
                data.created_at = now;
            }
            if (!data.updated_at) {
                data.updated_at = now;
            }

            const fields = Object.keys(data);
            const values = Object.values(data);
            const placeholders = fields.map(() => '?').join(', ');

            const sql = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
            const result = await this.db.run(sql, values);

            // 返回创建的记录
            return await this.findById(result.lastID);
        } catch (error) {
            console.error(`创建${this.tableName}记录失败:`, error);
            throw error;
        }
    }

    /**
     * 根据ID查找记录
     */
    async findById(id) {
        try {
            const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
            return await this.db.get(sql, [id]);
        } catch (error) {
            console.error(`查找${this.tableName}记录失败:`, error);
            throw error;
        }
    }

    /**
     * 查找所有记录
     */
    async findAll(conditions = {}, orderBy = 'id DESC', limit = null) {
        try {
            let sql = `SELECT * FROM ${this.tableName}`;
            const params = [];

            // 添加条件
            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions)
                    .map(key => `${key} = ?`)
                    .join(' AND ');
                sql += ` WHERE ${whereClause}`;
                params.push(...Object.values(conditions));
            }

            // 添加排序
            if (orderBy) {
                sql += ` ORDER BY ${orderBy}`;
            }

            // 添加限制
            if (limit) {
                sql += ` LIMIT ?`;
                params.push(limit);
            }

            return await this.db.all(sql, params);
        } catch (error) {
            console.error(`查找${this.tableName}记录失败:`, error);
            throw error;
        }
    }

    /**
     * 根据条件查找单条记录
     */
    async findOne(conditions = {}) {
        try {
            let sql = `SELECT * FROM ${this.tableName}`;
            const params = [];

            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions)
                    .map(key => `${key} = ?`)
                    .join(' AND ');
                sql += ` WHERE ${whereClause}`;
                params.push(...Object.values(conditions));
            }

            sql += ' LIMIT 1';
            return await this.db.get(sql, params);
        } catch (error) {
            console.error(`查找${this.tableName}记录失败:`, error);
            throw error;
        }
    }

    /**
     * 更新记录
     */
    async update(id, data) {
        try {
            // 添加更新时间
            data.updated_at = new Date().toISOString();

            const fields = Object.keys(data);
            const values = Object.values(data);
            const setClause = fields.map(field => `${field} = ?`).join(', ');

            const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`;
            const result = await this.db.run(sql, [...values, id]);

            if (result.changes === 0) {
                throw new Error(`记录不存在或更新失败: ID ${id}`);
            }

            // 返回更新后的记录
            return await this.findById(id);
        } catch (error) {
            console.error(`更新${this.tableName}记录失败:`, error);
            throw error;
        }
    }

    /**
     * 批量更新记录
     */
    async updateMany(conditions, data) {
        try {
            // 添加更新时间
            data.updated_at = new Date().toISOString();

            const setFields = Object.keys(data);
            const setValues = Object.values(data);
            const setClause = setFields.map(field => `${field} = ?`).join(', ');

            let sql = `UPDATE ${this.tableName} SET ${setClause}`;
            const params = [...setValues];

            // 添加条件
            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions)
                    .map(key => `${key} = ?`)
                    .join(' AND ');
                sql += ` WHERE ${whereClause}`;
                params.push(...Object.values(conditions));
            }

            const result = await this.db.run(sql, params);
            return result.changes;
        } catch (error) {
            console.error(`批量更新${this.tableName}记录失败:`, error);
            throw error;
        }
    }

    /**
     * 删除记录
     */
    async delete(id) {
        try {
            const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
            const result = await this.db.run(sql, [id]);

            if (result.changes === 0) {
                throw new Error(`记录不存在或删除失败: ID ${id}`);
            }

            return result.changes;
        } catch (error) {
            console.error(`删除${this.tableName}记录失败:`, error);
            throw error;
        }
    }

    /**
     * 批量删除记录
     */
    async deleteMany(conditions) {
        try {
            let sql = `DELETE FROM ${this.tableName}`;
            const params = [];

            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions)
                    .map(key => `${key} = ?`)
                    .join(' AND ');
                sql += ` WHERE ${whereClause}`;
                params.push(...Object.values(conditions));
            }

            const result = await this.db.run(sql, params);
            return result.changes;
        } catch (error) {
            console.error(`批量删除${this.tableName}记录失败:`, error);
            throw error;
        }
    }

    /**
     * 统计记录数量
     */
    async count(conditions = {}) {
        try {
            let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
            const params = [];

            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions)
                    .map(key => `${key} = ?`)
                    .join(' AND ');
                sql += ` WHERE ${whereClause}`;
                params.push(...Object.values(conditions));
            }

            const result = await this.db.get(sql, params);
            return result.count;
        } catch (error) {
            console.error(`统计${this.tableName}记录失败:`, error);
            throw error;
        }
    }

    /**
     * 分页查询
     */
    async paginate(page = 1, pageSize = 10, conditions = {}, orderBy = 'id DESC') {
        try {
            const offset = (page - 1) * pageSize;

            // 查询总数
            const total = await this.count(conditions);

            // 查询数据
            let sql = `SELECT * FROM ${this.tableName}`;
            const params = [];

            if (Object.keys(conditions).length > 0) {
                const whereClause = Object.keys(conditions)
                    .map(key => `${key} = ?`)
                    .join(' AND ');
                sql += ` WHERE ${whereClause}`;
                params.push(...Object.values(conditions));
            }

            if (orderBy) {
                sql += ` ORDER BY ${orderBy}`;
            }

            sql += ` LIMIT ? OFFSET ?`;
            params.push(pageSize, offset);

            const data = await this.db.all(sql, params);

            return {
                data,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize)
                }
            };
        } catch (error) {
            console.error(`分页查询${this.tableName}记录失败:`, error);
            throw error;
        }
    }
}

module.exports = BaseModel;