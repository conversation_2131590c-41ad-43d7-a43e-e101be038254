const BaseModel = require('../base-model');

class TaskModel extends BaseModel {
    constructor() {
        super('tasks');
    }

    /**
     * 为工单创建任务列表
     */
    async createTasksForTicket(ticketId, tasks) {
        try {
            const createdTasks = [];

            for (let i = 0; i < tasks.length; i++) {
                const taskData = {
                    ticket_id: ticketId,
                    task_order: i + 1,
                    title: tasks[i].title,
                    description: tasks[i].description,
                    action_type: tasks[i].action_type,
                    target_selector: tasks[i].target_selector,
                    action_data: JSON.stringify(tasks[i].action_data || {}),
                    status: '待开始'
                };

                const createdTask = await this.create(taskData);
                createdTasks.push(createdTask);
            }

            return createdTasks;
        } catch (error) {
            console.error('为工单创建任务列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取工单的所有任务
     */
    async getTasksByTicketId(ticketId) {
        try {
            return await this.findAll({ ticket_id: ticketId }, 'task_order ASC');
        } catch (error) {
            console.error('获取工单任务失败:', error);
            throw error;
        }
    }

    /**
     * 获取工单的下一个待执行任务
     */
    async getNextPendingTask(ticketId) {
        try {
            const sql = `
                SELECT * FROM ${this.tableName}
                WHERE ticket_id = ? AND status = '待开始'
                ORDER BY task_order ASC
                LIMIT 1
            `;
            return await this.db.get(sql, [ticketId]);
        } catch (error) {
            console.error('获取下一个待执行任务失败:', error);
            throw error;
        }
    }

    /**
     * 更新任务状态
     */
    async updateTaskStatus(taskId, status, result = null) {
        try {
            const updateData = { status };

            if (result) {
                if (result.screenshot_path) {
                    updateData.screenshot_path = result.screenshot_path;
                }
                if (result.summary) {
                    updateData.result_summary = result.summary;
                }
                if (result.error) {
                    updateData.error_message = result.error;
                }
            }

            // 如果任务完成，设置完成时间
            if (status === '已完成') {
                updateData.completed_at = new Date().toISOString();
            }

            return await this.update(taskId, updateData);
        } catch (error) {
            console.error('更新任务状态失败:', error);
            throw error;
        }
    }

    /**
     * 增加任务重试次数
     */
    async incrementRetryCount(taskId) {
        try {
            const task = await this.findById(taskId);
            if (!task) {
                throw new Error(`任务不存在: ${taskId}`);
            }

            const newRetryCount = (task.retry_count || 0) + 1;
            return await this.update(taskId, { retry_count: newRetryCount });
        } catch (error) {
            console.error('增加任务重试次数失败:', error);
            throw error;
        }
    }

    /**
     * 获取工单任务进度
     */
    async getTicketProgress(ticketId) {
        try {
            const sql = `
                SELECT
                    COUNT(*) as total_tasks,
                    SUM(CASE WHEN status = '已完成' THEN 1 ELSE 0 END) as completed_tasks,
                    SUM(CASE WHEN status = '失败' THEN 1 ELSE 0 END) as failed_tasks,
                    SUM(CASE WHEN status = '执行中' THEN 1 ELSE 0 END) as running_tasks
                FROM ${this.tableName}
                WHERE ticket_id = ?
            `;

            const result = await this.db.get(sql, [ticketId]);

            return {
                total: result.total_tasks,
                completed: result.completed_tasks,
                failed: result.failed_tasks,
                running: result.running_tasks,
                pending: result.total_tasks - result.completed_tasks - result.failed_tasks - result.running_tasks,
                progress_percentage: result.total_tasks > 0 ? Math.round((result.completed_tasks / result.total_tasks) * 100) : 0
            };
        } catch (error) {
            console.error('获取工单任务进度失败:', error);
            throw error;
        }
    }

    /**
     * 获取失败的任务列表
     */
    async getFailedTasks(ticketId = null) {
        try {
            let sql = `SELECT * FROM ${this.tableName} WHERE status = 'failed'`;
            const params = [];

            if (ticketId) {
                sql += ` AND ticket_id = ?`;
                params.push(ticketId);
            }

            sql += ` ORDER BY updated_at DESC`;
            return await this.db.all(sql, params);
        } catch (error) {
            console.error('获取失败任务列表失败:', error);
            throw error;
        }
    }

    /**
     * 重置任务状态(用于重试)
     */
    async resetTaskStatus(taskId) {
        try {
            return await this.update(taskId, {
                status: '待开始',
                error_message: null,
                result_summary: null
            });
        } catch (error) {
            console.error('重置任务状态失败:', error);
            throw error;
        }
    }

    /**
     * 批量更新工单的所有任务状态
     */
    async updateAllTasksStatus(ticketId, status) {
        try {
            return await this.updateMany({ ticket_id: ticketId }, { status });
        } catch (error) {
            console.error('批量更新任务状态失败:', error);
            throw error;
        }
    }

    /**
     * 删除工单的所有任务
     */
    async deleteTasksByTicketId(ticketId) {
        try {
            return await this.deleteMany({ ticket_id: ticketId });
        } catch (error) {
            console.error('删除工单任务失败:', error);
            throw error;
        }
    }

    /**
     * 获取任务执行统计
     */
    async getTaskStatistics(ticketId = null) {
        try {
            let sql = `
                SELECT
                    status,
                    COUNT(*) as count,
                    AVG(retry_count) as avg_retry_count
                FROM ${this.tableName}
            `;
            const params = [];

            if (ticketId) {
                sql += ` WHERE ticket_id = ?`;
                params.push(ticketId);
            }

            sql += ` GROUP BY status`;

            return await this.db.all(sql, params);
        } catch (error) {
            console.error('获取任务统计失败:', error);
            throw error;
        }
    }
}

module.exports = new TaskModel();