const BaseModel = require('../base-model');

class TicketModel extends BaseModel {
    constructor() {
        super('tickets');
    }

    /**
     * 生成工单号
     */
    generateTicketNumber() {
        const now = new Date();
        // 使用本地时间而不是UTC时间
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const dateStr = `${year}${month}${day}`;
        const timeStr = now.getTime().toString().slice(-6);
        return `WO-${dateStr}-${timeStr}`;
    }

    /**
     * 创建工单
     */
    async createTicket(data) {
        try {
            // 自动生成工单号
            if (!data.ticket_number) {
                data.ticket_number = this.generateTicketNumber();
            }

            // 设置默认值
            data.status = data.status || '待开始';
            data.priority = data.priority || 0;

            return await this.create(data);
        } catch (error) {
            console.error('创建工单失败:', error);
            throw error;
        }
    }

    /**
     * 根据工单号查找工单
     */
    async findByTicketNumber(ticketNumber) {
        try {
            return await this.findOne({ ticket_number: ticketNumber });
        } catch (error) {
            console.error('根据工单号查找工单失败:', error);
            throw error;
        }
    }

    /**
     * 根据状态查找工单
     */
    async findByStatus(status, orderBy = 'created_at ASC') {
        try {
            return await this.findAll({ status }, orderBy);
        } catch (error) {
            console.error('根据状态查找工单失败:', error);
            throw error;
        }
    }

    /**
     * 获取待处理的工单队列
     */
    async getPendingQueue() {
        try {
            const sql = `
                SELECT * FROM ${this.tableName}
                WHERE status IN ('待开始', '排队中')
                ORDER BY priority DESC, created_at ASC
            `;
            return await this.db.all(sql);
        } catch (error) {
            console.error('获取待处理工单队列失败:', error);
            throw error;
        }
    }

    /**
     * 获取正在处理的工单
     */
    async getProcessingTickets() {
        try {
            return await this.findAll({ status: '处理中' }, 'created_at ASC');
        } catch (error) {
            console.error('获取正在处理的工单失败:', error);
            throw error;
        }
    }

    /**
     * 更新工单状态
     */
    async updateStatus(id, status, notes = null) {
        try {
            const updateData = { status };

            if (notes) {
                updateData.notes = notes;
            }

            // 如果状态是已完成，设置完成时间和处理时长
            if (status === '已完成') {
                const ticket = await this.findById(id);
                if (ticket) {
                    // 使用本地时间格式
                    const now = new Date();
                    updateData.completed_at = now.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    });
                    const createdAt = new Date(ticket.created_at);
                    const completedAt = now;
                    updateData.processing_time = Math.round((completedAt - createdAt) / (1000 * 60)); // 分钟
                }
            }

            return await this.update(id, updateData);
        } catch (error) {
            console.error('更新工单状态失败:', error);
            throw error;
        }
    }

    /**
     * 分配工单给工作线程
     */
    async assignToWorker(ticketId, workerId) {
        try {
            return await this.update(ticketId, {
                assigned_worker_id: workerId,
                status: '处理中'
            });
        } catch (error) {
            console.error('分配工单给工作线程失败:', error);
            throw error;
        }
    }

    /**
     * 释放工单(从工作线程中移除)
     */
    async releaseFromWorker(ticketId) {
        try {
            return await this.update(ticketId, {
                assigned_worker_id: null,
                status: '待开始'
            });
        } catch (error) {
            console.error('释放工单失败:', error);
            throw error;
        }
    }

    /**
     * 搜索工单
     */
    async search(filters = {}) {
        try {
            let sql = `SELECT * FROM ${this.tableName} WHERE 1=1`;
            const params = [];

            // 工单号搜索(部分匹配)
            if (filters.ticket_number) {
                sql += ` AND ticket_number LIKE ?`;
                params.push(`%${filters.ticket_number}%`);
            }

            // 内容搜索(部分匹配)
            if (filters.content) {
                sql += ` AND (title LIKE ? OR content LIKE ?)`;
                params.push(`%${filters.content}%`, `%${filters.content}%`);
            }

            // 状态筛选
            if (filters.status) {
                sql += ` AND status = ?`;
                params.push(filters.status);
            }

            // 优先级筛选
            if (filters.priority !== undefined) {
                sql += ` AND priority = ?`;
                params.push(filters.priority);
            }

            // 日期范围筛选
            if (filters.start_date) {
                sql += ` AND created_at >= ?`;
                params.push(filters.start_date);
            }

            if (filters.end_date) {
                sql += ` AND created_at <= ?`;
                params.push(filters.end_date);
            }

            // 排序
            const orderBy = filters.order_by || 'created_at DESC';
            sql += ` ORDER BY ${orderBy}`;

            // 分页
            if (filters.limit) {
                sql += ` LIMIT ?`;
                params.push(filters.limit);

                if (filters.offset) {
                    sql += ` OFFSET ?`;
                    params.push(filters.offset);
                }
            }

            return await this.db.all(sql, params);
        } catch (error) {
            console.error('搜索工单失败:', error);
            throw error;
        }
    }

    /**
     * 获取工单统计信息
     */
    async getStatistics() {
        try {
            const sql = `
                SELECT
                    status,
                    COUNT(*) as count,
                    AVG(processing_time) as avg_processing_time
                FROM ${this.tableName}
                GROUP BY status
            `;
            const stats = await this.db.all(sql);

            // 获取总数
            const totalSql = `SELECT COUNT(*) as total FROM ${this.tableName}`;
            const totalResult = await this.db.get(totalSql);

            return {
                total: totalResult.total,
                by_status: stats
            };
        } catch (error) {
            console.error('获取工单统计信息失败:', error);
            throw error;
        }
    }
}

module.exports = new TicketModel();