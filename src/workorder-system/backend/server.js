const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

// 导入路由
const ticketRoutes = require('./src/routes/tickets');
const taskRoutes = require('./src/routes/tasks');
const systemRoutes = require('./src/routes/system');

// 导入WebSocket服务
const WebSocketService = require('./src/websocket/websocket-service');

// 导入数据库连接
const db = require('../../shared/database/connection');

const app = express();
const PORT = process.env.WORKORDER_BACKEND_PORT || 3001;

// 中间件配置
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));

// CORS配置
app.use(cors({
    origin: [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:3002',
        'http://localhost:3003',
        'http://localhost:3004'
    ],
    credentials: true
}));

// 请求限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 1000, // 限制每个IP 15分钟内最多1000个请求
    message: '请求过于频繁，请稍后再试'
});
app.use(limiter);

// 解析JSON和URL编码的请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// API路由
app.use('/api/tickets', ticketRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/system', systemRoutes);

// 健康检查端点
app.get('/health', async (req, res) => {
    try {
        const dbHealth = await db.healthCheck();
        res.json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            database: dbHealth ? 'connected' : 'disconnected',
            uptime: process.uptime()
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: error.message
        });
    }
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'API端点不存在',
        path: req.originalUrl
    });
});

// 全局错误处理
app.use((error, req, res, next) => {
    console.error('服务器错误:', error);

    res.status(error.status || 500).json({
        error: process.env.NODE_ENV === 'production'
            ? '服务器内部错误'
            : error.message,
        ...(process.env.NODE_ENV !== 'production' && { stack: error.stack })
    });
});

// 启动服务器
async function startServer() {
    try {
        // 连接数据库
        await db.connect();
        console.log('数据库连接成功');

        // 启动HTTP服务器
        const server = app.listen(PORT, () => {
            console.log(`工单系统后端服务启动成功`);
            console.log(`HTTP服务: http://localhost:${PORT}`);
            console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
        });

        // 启动WebSocket服务
        const wsService = new WebSocketService(server);
        wsService.start();

        // 将WebSocket服务注入到控制器中
        const TicketController = require('./src/controllers/ticket-controller');
        const ticketController = new TicketController();
        ticketController.setWebSocketService(wsService);

        // 设置全局控制器实例
        global.ticketController = ticketController;

        // 启动消息队列管理
        await ticketController.startMessageQueue();

        const TaskController = require('./src/controllers/task-controller');
        const taskController = new TaskController();
        taskController.setWebSocketService(wsService);

        // 将控制器实例设置为全局可访问，供路由使用
        global.ticketController = ticketController;
        global.taskController = taskController;

        // 优雅关闭
        process.on('SIGTERM', async () => {
            console.log('收到SIGTERM信号，开始优雅关闭...');
            server.close(async () => {
                await db.close();
                console.log('服务器已关闭');
                process.exit(0);
            });
        });

        process.on('SIGINT', async () => {
            console.log('收到SIGINT信号，开始优雅关闭...');
            server.close(async () => {
                await db.close();
                console.log('服务器已关闭');
                process.exit(0);
            });
        });

    } catch (error) {
        console.error('服务器启动失败:', error);
        process.exit(1);
    }
}

// 启动服务器
startServer();