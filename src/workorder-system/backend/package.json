{"name": "workorder-backend", "version": "1.0.0", "description": "工单系统后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "ws": "^8.16.0", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["workorder", "api", "express", "websocket"], "author": "RPA Team", "license": "MIT"}