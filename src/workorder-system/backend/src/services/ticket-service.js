const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class TicketService {
    /**
     * 处理上传的图片，将其转换为base64并嵌入到内容中
     */
    async processUploadedImages(content, files) {
        try {
            let processedContent = content;

            for (const file of files) {
                // 生成唯一的文件名
                const fileExtension = path.extname(file.originalname);
                const fileName = crypto.randomUUID() + fileExtension;
                const filePath = path.join(__dirname, '../uploads', fileName);

                // 确保上传目录存在
                const uploadDir = path.dirname(filePath);
                if (!fs.existsSync(uploadDir)) {
                    fs.mkdirSync(uploadDir, { recursive: true });
                }

                // 保存文件
                fs.writeFileSync(filePath, file.buffer);

                // 读取文件并转换为base64
                const imageBuffer = fs.readFileSync(filePath);
                const base64Image = imageBuffer.toString('base64');
                const mimeType = file.mimetype;

                // 创建base64数据URL
                const dataUrl = `data:${mimeType};base64,${base64Image}`;

                // 在内容中添加图片标记，前端可以根据这个标记来显示图片
                const imageMarkdown = `\n\n![上传图片](${dataUrl})\n\n`;
                processedContent += imageMarkdown;

                // 删除临时文件
                fs.unlinkSync(filePath);
            }

            return processedContent;
        } catch (error) {
            console.error('处理上传图片失败:', error);
            throw new Error('图片处理失败');
        }
    }

    /**
     * 验证工单数据
     */
    validateTicketData(data) {
        const errors = [];

        if (!data.title || data.title.trim().length === 0) {
            errors.push('工单标题不能为空');
        }

        if (data.title && data.title.length > 200) {
            errors.push('工单标题不能超过200个字符');
        }

        if (!data.content || data.content.trim().length === 0) {
            errors.push('工单内容不能为空');
        }

        if (data.priority !== undefined) {
            const priority = parseInt(data.priority);
            if (isNaN(priority) || priority < 0 || priority > 2) {
                errors.push('优先级必须是0-2之间的整数');
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 检查工单状态转换是否合法
     */
    isValidStatusTransition(currentStatus, newStatus) {
        const validTransitions = {
            '待开始': ['排队中', '处理中', '已挂起'],
            '排队中': ['处理中', '已挂起'],
            '处理中': ['待补充信息', '已完成', '已挂起', '处理失败'],
            '待补充信息': ['待开始', '已挂起'],
            '已完成': [], // 已完成状态不能转换到其他状态
            '已挂起': ['待开始'],
            '处理失败': ['待开始', '已挂起']
        };

        return validTransitions[currentStatus]?.includes(newStatus) || false;
    }

    /**
     * 计算工单处理时长
     */
    calculateProcessingTime(createdAt, completedAt = null) {
        const startTime = new Date(createdAt);
        const endTime = completedAt ? new Date(completedAt) : new Date();

        const diffInMs = endTime - startTime;
        const diffInHours = Math.round(diffInMs / (1000 * 60 * 60));

        return diffInHours;
    }

    /**
     * 格式化工单数据用于显示
     */
    formatTicketForDisplay(ticket) {
        return {
            ...ticket,
            processing_time_display: this.formatProcessingTime(ticket.processing_time),
            created_at_display: this.formatDateTime(ticket.created_at),
            updated_at_display: this.formatDateTime(ticket.updated_at),
            completed_at_display: ticket.completed_at ? this.formatDateTime(ticket.completed_at) : null,
            priority_display: this.getPriorityDisplay(ticket.priority),
            status_display: this.getStatusDisplay(ticket.status)
        };
    }

    /**
     * 格式化处理时长显示
     */
    formatProcessingTime(hours) {
        if (!hours) return '0小时';

        if (hours < 24) {
            return `${hours}小时`;
        } else {
            const days = Math.floor(hours / 24);
            const remainingHours = hours % 24;
            return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`;
        }
    }

    /**
     * 格式化日期时间显示
     */
    formatDateTime(dateTime) {
        if (!dateTime) return null;

        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 获取优先级显示文本
     */
    getPriorityDisplay(priority) {
        const priorityMap = {
            0: '低',
            1: '中',
            2: '高'
        };
        return priorityMap[priority] || '未知';
    }

    /**
     * 获取状态显示文本和颜色
     */
    getStatusDisplay(status) {
        const statusMap = {
            '待开始': { text: '待开始', color: 'default' },
            '排队中': { text: '排队中', color: 'processing' },
            '处理中': { text: '处理中', color: 'processing' },
            '待补充信息': { text: '待补充信息', color: 'warning' },
            '已完成': { text: '已完成', color: 'success' },
            '已挂起': { text: '已挂起', color: 'default' },
            '处理失败': { text: '处理失败', color: 'error' }
        };
        return statusMap[status] || { text: status, color: 'default' };
    }

    /**
     * 提取内容中的图片信息
     */
    extractImagesFromContent(content) {
        const imageRegex = /!\[.*?\]\((data:image\/[^;]+;base64,[^)]+)\)/g;
        const images = [];
        let match;

        while ((match = imageRegex.exec(content)) !== null) {
            images.push({
                dataUrl: match[1],
                alt: match[0].match(/!\[(.*?)\]/)?.[1] || '图片'
            });
        }

        return images;
    }

    /**
     * 清理内容中的base64图片（用于存储优化）
     */
    cleanContentForStorage(content) {
        // 将base64图片替换为占位符，实际项目中可能需要将图片存储到文件系统或云存储
        return content.replace(/data:image\/[^;]+;base64,[^)]+/g, '[图片]');
    }
}

module.exports = new TicketService();