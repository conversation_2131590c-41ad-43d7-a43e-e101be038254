const express = require('express');
const router = express.Router();
const SystemController = require('../controllers/system-controller');

// 获取系统配置
router.get('/config', SystemController.getSystemConfig);

// 更新系统配置
router.put('/config', SystemController.updateSystemConfig);

// 获取系统状态
router.get('/status', SystemController.getSystemStatus);

// 获取系统统计信息
router.get('/statistics', SystemController.getSystemStatistics);

// 获取操作指引版本
router.get('/guide-version', SystemController.getGuideVersion);

// 更新操作指引版本
router.put('/guide-version', SystemController.updateGuideVersion);

// 系统健康检查
router.get('/health', SystemController.healthCheck);

// AI助手控制
router.post('/ai-assistant/start', SystemController.startAIAssistant);
router.post('/ai-assistant/stop', SystemController.stopAIAssistant);

module.exports = router;