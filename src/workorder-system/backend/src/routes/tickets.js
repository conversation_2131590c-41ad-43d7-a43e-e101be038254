const express = require('express');
const router = express.Router();
const upload = require('../middleware/upload');

// 获取控制器实例的辅助函数
const getTicketController = () => global.ticketController;

// 获取工单列表
router.get('/', (req, res) => getTicketController().getTickets(req, res));

// 获取工单详情
router.get('/:id', (req, res) => getTicketController().getTicketById(req, res));

// 创建工单
router.post('/', upload.array('images', 10), (req, res) => getTicketController().createTicket(req, res));

// 更新工单
router.put('/:id', upload.array('images', 10), (req, res) => getTicketController().updateTicket(req, res));

// 更新工单状态
router.patch('/:id/status', (req, res) => getTicketController().updateTicketStatus(req, res));

// 批量更新工单状态
router.patch('/batch/status', (req, res) => getTicketController().batchUpdateStatus(req, res));

// 删除工单
router.delete('/:id', (req, res) => getTicketController().deleteTicket(req, res));

// 批量删除工单
router.delete('/batch', (req, res) => getTicketController().batchDeleteTickets(req, res));

// 获取队列状态
router.get('/queue/status', (req, res) => getTicketController().getQueueStatus(req, res));

// 搜索工单
router.post('/search', (req, res) => getTicketController().searchTickets(req, res));

// 获取工单统计
router.get('/stats/overview', (req, res) => getTicketController().getStatistics(req, res));

// 获取工单任务
router.get('/:id/tasks', (req, res) => getTicketController().getTicketTasks(req, res));

// 获取工单进度
router.get('/:id/progress', (req, res) => getTicketController().getTicketProgress(req, res));

module.exports = router;