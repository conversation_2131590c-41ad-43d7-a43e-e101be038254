const express = require('express');
const router = express.Router();

// 获取控制器实例的辅助函数
const getTaskController = () => global.taskController;

// 获取任务列表
router.get('/', (req, res) => getTaskController().getTasks(req, res));

// 获取任务详情
router.get('/:id', (req, res) => getTaskController().getTaskById(req, res));

// 创建任务
router.post('/', (req, res) => getTaskController().createTask(req, res));

// 批量创建任务
router.post('/batch', (req, res) => getTaskController().createTasksBatch(req, res));

// 更新任务
router.put('/:id', (req, res) => getTaskController().updateTask(req, res));

// 更新任务状态
router.patch('/:id/status', (req, res) => getTaskController().updateTaskStatus(req, res));

// 增加任务重试次数
router.patch('/:id/retry', (req, res) => getTaskController().incrementRetryCount(req, res));

// 重置任务状态
router.patch('/:id/reset', (req, res) => getTaskController().resetTaskStatus(req, res));

// 删除任务
router.delete('/:id', (req, res) => getTaskController().deleteTask(req, res));

// 获取工单的任务列表
router.get('/ticket/:ticketId', (req, res) => getTaskController().getTasksByTicketId(req, res));

// 获取工单的下一个待执行任务
router.get('/ticket/:ticketId/next', (req, res) => getTaskController().getNextPendingTask(req, res));

// 获取工单任务进度
router.get('/ticket/:ticketId/progress', (req, res) => getTaskController().getTicketProgress(req, res));

// 获取失败的任务列表
router.get('/failed/list', (req, res) => getTaskController().getFailedTasks(req, res));

// 获取任务统计信息
router.get('/stats/overview', (req, res) => getTaskController().getTaskStatistics(req, res));

module.exports = router;