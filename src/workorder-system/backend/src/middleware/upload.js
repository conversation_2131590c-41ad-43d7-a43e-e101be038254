const multer = require('multer');
const path = require('path');

// 配置multer用于处理文件上传
const storage = multer.memoryStorage(); // 使用内存存储，便于处理

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 检查文件类型
    const allowedMimeTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/bmp'
    ];

    if (allowedMimeTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('不支持的文件类型。只允许上传图片文件（JPEG, PNG, GIF, WebP, BMP）'), false);
    }
};

// 创建multer实例
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024, // 限制文件大小为10MB
        files: 10 // 最多10个文件
    }
});

// 错误处理中间件
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                message: '文件大小超过限制（最大10MB）'
            });
        }

        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                success: false,
                message: '文件数量超过限制（最多10个文件）'
            });
        }

        if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
                success: false,
                message: '意外的文件字段'
            });
        }
    }

    if (error.message.includes('不支持的文件类型')) {
        return res.status(400).json({
            success: false,
            message: error.message
        });
    }

    // 其他错误
    return res.status(500).json({
        success: false,
        message: '文件上传失败',
        error: error.message
    });
};

// 包装upload中间件以添加错误处理
const uploadWithErrorHandling = (uploadMiddleware) => {
    return (req, res, next) => {
        uploadMiddleware(req, res, (error) => {
            if (error) {
                return handleUploadError(error, req, res, next);
            }
            next();
        });
    };
};

// 导出不同类型的上传中间件
module.exports = {
    // 单文件上传
    single: (fieldName) => uploadWithErrorHandling(upload.single(fieldName)),

    // 多文件上传（同一字段）
    array: (fieldName, maxCount) => uploadWithErrorHandling(upload.array(fieldName, maxCount)),

    // 多文件上传（不同字段）
    fields: (fields) => uploadWithErrorHandling(upload.fields(fields)),

    // 任意文件上传
    any: () => uploadWithErrorHandling(upload.any()),

    // 只解析表单数据，不处理文件
    none: () => uploadWithErrorHandling(upload.none()),

    // 默认导出（用于向后兼容）
    ...upload
};