const TaskModel = require('../../../../shared/database/models/task');
const TicketModel = require('../../../../shared/database/models/ticket');

class TaskController {
    constructor() {
        this.wsService = null; // WebSocket服务引用，将在启动时设置
    }

    /**
     * 设置WebSocket服务引用
     */
    setWebSocketService(wsService) {
        this.wsService = wsService;
    }
    /**
     * 获取任务列表
     */
    async getTasks(req, res) {
        try {
            const { page = 1, pageSize = 10, status, ticketId } = req.query;

            const conditions = {};
            if (status) conditions.status = status;
            if (ticketId) conditions.ticket_id = parseInt(ticketId);

            const result = await TaskModel.paginate(
                parseInt(page),
                parseInt(pageSize),
                conditions,
                'task_order ASC'
            );

            res.json({
                success: true,
                data: result.data,
                pagination: result.pagination
            });
        } catch (error) {
            console.error('获取任务列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取任务列表失败',
                error: error.message
            });
        }
    }

    /**
     * 获取任务详情
     */
    async getTaskById(req, res) {
        try {
            const { id } = req.params;
            const task = await TaskModel.findById(id);

            if (!task) {
                return res.status(404).json({
                    success: false,
                    message: '任务不存在'
                });
            }

            // 解析action_data JSON字符串
            if (task.action_data) {
                try {
                    task.action_data = JSON.parse(task.action_data);
                } catch (e) {
                    console.warn('解析action_data失败:', e);
                }
            }

            res.json({
                success: true,
                data: task
            });
        } catch (error) {
            console.error('获取任务详情失败:', error);
            res.status(500).json({
                success: false,
                message: '获取任务详情失败',
                error: error.message
            });
        }
    }

    /**
     * 创建任务
     */
    async createTask(req, res) {
        try {
            const {
                ticket_id,
                title,
                description,
                action_type,
                target_selector,
                action_data,
                task_order
            } = req.body;

            if (!ticket_id || !title) {
                return res.status(400).json({
                    success: false,
                    message: '工单ID和任务标题不能为空'
                });
            }

            // 验证工单是否存在
            const ticket = await TicketModel.findById(ticket_id);
            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '关联的工单不存在'
                });
            }

            const taskData = {
                ticket_id: parseInt(ticket_id),
                title,
                description,
                action_type,
                target_selector,
                action_data: action_data ? JSON.stringify(action_data) : null,
                task_order: task_order || 1
            };

            const task = await TaskModel.create(taskData);

            res.status(201).json({
                success: true,
                message: '任务创建成功',
                data: task
            });
        } catch (error) {
            console.error('创建任务失败:', error);
            res.status(500).json({
                success: false,
                message: '创建任务失败',
                error: error.message
            });
        }
    }

    /**
     * 批量创建任务
     */
    async createTasksBatch(req, res) {
        try {
            const { ticket_id, tasks } = req.body;

            if (!ticket_id || !tasks || !Array.isArray(tasks) || tasks.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '工单ID和任务列表不能为空'
                });
            }

            // 验证工单是否存在
            const ticket = await TicketModel.findById(ticket_id);
            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '关联的工单不存在'
                });
            }

            const createdTasks = await TaskModel.createTasksForTicket(ticket_id, tasks);

            res.status(201).json({
                success: true,
                message: `成功创建 ${createdTasks.length} 个任务`,
                data: createdTasks
            });
        } catch (error) {
            console.error('批量创建任务失败:', error);
            res.status(500).json({
                success: false,
                message: '批量创建任务失败',
                error: error.message
            });
        }
    }

    /**
     * 更新任务
     */
    async updateTask(req, res) {
        try {
            const { id } = req.params;
            const {
                title,
                description,
                action_type,
                target_selector,
                action_data,
                task_order
            } = req.body;

            const task = await TaskModel.findById(id);
            if (!task) {
                return res.status(404).json({
                    success: false,
                    message: '任务不存在'
                });
            }

            const updateData = {};
            if (title) updateData.title = title;
            if (description) updateData.description = description;
            if (action_type) updateData.action_type = action_type;
            if (target_selector) updateData.target_selector = target_selector;
            if (action_data) updateData.action_data = JSON.stringify(action_data);
            if (task_order !== undefined) updateData.task_order = parseInt(task_order);

            const updatedTask = await TaskModel.update(id, updateData);

            res.json({
                success: true,
                message: '任务更新成功',
                data: updatedTask
            });
        } catch (error) {
            console.error('更新任务失败:', error);
            res.status(500).json({
                success: false,
                message: '更新任务失败',
                error: error.message
            });
        }
    }

    /**
     * 更新任务状态
     */
    async updateTaskStatus(req, res) {
        try {
            const { id } = req.params;
            const { status, result } = req.body;

            if (!status) {
                return res.status(400).json({
                    success: false,
                    message: '状态不能为空'
                });
            }

            const validStatuses = ['待开始', '执行中', '已完成', '失败', '跳过'];
            if (!validStatuses.includes(status)) {
                return res.status(400).json({
                    success: false,
                    message: '无效的状态值'
                });
            }

            const updatedTask = await TaskModel.updateTaskStatus(id, status, result);

            // 发送WebSocket事件通知
            if (this.wsService) {
                this.wsService.broadcastToRoom('tickets', {
                    type: 'task_updated',
                    data: updatedTask,
                    changes: { status, result },
                    timestamp: new Date().toISOString()
                });
            }

            res.json({
                success: true,
                message: '任务状态更新成功',
                data: updatedTask
            });
        } catch (error) {
            console.error('更新任务状态失败:', error);
            res.status(500).json({
                success: false,
                message: '更新任务状态失败',
                error: error.message
            });
        }
    }

    /**
     * 增加任务重试次数
     */
    async incrementRetryCount(req, res) {
        try {
            const { id } = req.params;

            const updatedTask = await TaskModel.incrementRetryCount(id);

            res.json({
                success: true,
                message: '任务重试次数增加成功',
                data: updatedTask
            });
        } catch (error) {
            console.error('增加任务重试次数失败:', error);
            res.status(500).json({
                success: false,
                message: '增加任务重试次数失败',
                error: error.message
            });
        }
    }

    /**
     * 重置任务状态
     */
    async resetTaskStatus(req, res) {
        try {
            const { id } = req.params;

            const updatedTask = await TaskModel.resetTaskStatus(id);

            res.json({
                success: true,
                message: '任务状态重置成功',
                data: updatedTask
            });
        } catch (error) {
            console.error('重置任务状态失败:', error);
            res.status(500).json({
                success: false,
                message: '重置任务状态失败',
                error: error.message
            });
        }
    }

    /**
     * 删除任务
     */
    async deleteTask(req, res) {
        try {
            const { id } = req.params;

            const task = await TaskModel.findById(id);
            if (!task) {
                return res.status(404).json({
                    success: false,
                    message: '任务不存在'
                });
            }

            await TaskModel.delete(id);

            res.json({
                success: true,
                message: '任务删除成功'
            });
        } catch (error) {
            console.error('删除任务失败:', error);
            res.status(500).json({
                success: false,
                message: '删除任务失败',
                error: error.message
            });
        }
    }

    /**
     * 获取工单的任务列表
     */
    async getTasksByTicketId(req, res) {
        try {
            const { ticketId } = req.params;

            // 验证工单是否存在
            const ticket = await TicketModel.findById(ticketId);
            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '工单不存在'
                });
            }

            const tasks = await TaskModel.getTasksByTicketId(ticketId);

            res.json({
                success: true,
                data: tasks
            });
        } catch (error) {
            console.error('获取工单任务列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取工单任务列表失败',
                error: error.message
            });
        }
    }

    /**
     * 获取工单的下一个待执行任务
     */
    async getNextPendingTask(req, res) {
        try {
            const { ticketId } = req.params;

            // 验证工单是否存在
            const ticket = await TicketModel.findById(ticketId);
            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '工单不存在'
                });
            }

            const nextTask = await TaskModel.getNextPendingTask(ticketId);

            res.json({
                success: true,
                data: nextTask
            });
        } catch (error) {
            console.error('获取下一个待执行任务失败:', error);
            res.status(500).json({
                success: false,
                message: '获取下一个待执行任务失败',
                error: error.message
            });
        }
    }

    /**
     * 获取工单任务进度
     */
    async getTicketProgress(req, res) {
        try {
            const { ticketId } = req.params;

            // 验证工单是否存在
            const ticket = await TicketModel.findById(ticketId);
            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '工单不存在'
                });
            }

            const progress = await TaskModel.getTicketProgress(ticketId);

            res.json({
                success: true,
                data: progress
            });
        } catch (error) {
            console.error('获取工单任务进度失败:', error);
            res.status(500).json({
                success: false,
                message: '获取工单任务进度失败',
                error: error.message
            });
        }
    }

    /**
     * 获取失败的任务列表
     */
    async getFailedTasks(req, res) {
        try {
            const { ticketId } = req.query;

            const failedTasks = await TaskModel.getFailedTasks(ticketId);

            res.json({
                success: true,
                data: failedTasks
            });
        } catch (error) {
            console.error('获取失败任务列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取失败任务列表失败',
                error: error.message
            });
        }
    }

    /**
     * 获取任务统计信息
     */
    async getTaskStatistics(req, res) {
        try {
            const { ticketId } = req.query;

            const stats = await TaskModel.getTaskStatistics(ticketId);

            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            console.error('获取任务统计失败:', error);
            res.status(500).json({
                success: false,
                message: '获取任务统计失败',
                error: error.message
            });
        }
    }
}

module.exports = TaskController;