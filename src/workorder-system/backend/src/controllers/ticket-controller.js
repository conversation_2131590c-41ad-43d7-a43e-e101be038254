const TicketModel = require('../../../../shared/database/models/ticket');
const TaskModel = require('../../../../shared/database/models/task');
const TicketService = require('../services/ticket-service');
const TicketManager = require('../../../../shared/core/ticket-manager');
const MessageQueue = require('../../../../shared/core/message-queue');

class TicketController {
    constructor() {
        this.wsService = null; // WebSocket服务引用，将在启动时设置
        this.ticketManager = new TicketManager();
        this.messageQueue = new MessageQueue();
    }

    /**
     * 设置WebSocket服务引用
     */
    setWebSocketService(wsService) {
        this.wsService = wsService;
    }

    /**
     * 启动消息队列管理
     */
    async startMessageQueue() {
        try {
            await this.messageQueue.start();
            console.log('消息队列管理已启动');
        } catch (error) {
            console.error('启动消息队列失败:', error);
            throw error;
        }
    }

    /**
     * 停止消息队列管理
     */
    stopMessageQueue() {
        this.messageQueue.stop();
        console.log('消息队列管理已停止');
    }

    /**
     * 获取队列状态
     */
    async getQueueStatus(req, res) {
        try {
            const status = await this.messageQueue.getQueueStatus();
            res.json({
                success: true,
                data: status
            });
        } catch (error) {
            console.error('获取队列状态失败:', error);
            res.status(500).json({
                success: false,
                message: '获取队列状态失败'
            });
        }
    }
    /**
     * 获取工单列表
     */
    async getTickets(req, res) {
        try {
            const { page = 1, pageSize = 20, status, priority, search } = req.query;

            // 处理status参数，可能是字符串或数组
            let statusValue = null;
            if (status) {
                if (Array.isArray(status)) {
                    // 如果是数组，取第一个非空值
                    const validStatus = status.find(s => s && typeof s === 'string' && s.trim() !== '');
                    if (validStatus) statusValue = validStatus.trim();
                } else if (typeof status === 'string' && status.trim() !== '') {
                    statusValue = status.trim();
                }
            }

            const options = {
                page: parseInt(page),
                pageSize: parseInt(pageSize),
                status: statusValue,
                priority: priority !== undefined && priority !== '' && !isNaN(priority) ? parseInt(priority) : null,
                search: search && typeof search === 'string' && search.trim() !== '' ? search.trim() : null
            };

            const result = await this.ticketManager.getTickets(options);

            res.json({
                success: true,
                data: result.tickets,
                pagination: {
                    page: result.page,
                    pageSize: result.pageSize,
                    total: result.total,
                    totalPages: result.totalPages
                }
            });
        } catch (error) {
            console.error('获取工单列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取工单列表失败',
                error: error.message
            });
        }
    }

    /**
     * 获取工单详情
     */
    async getTicketById(req, res) {
        try {
            const { id } = req.params;
            const ticket = await TicketModel.findById(id);

            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '工单不存在'
                });
            }

            // 获取工单任务
            const tasks = await TaskModel.getTasksByTicketId(id);

            res.json({
                success: true,
                data: {
                    ...ticket,
                    tasks
                }
            });
        } catch (error) {
            console.error('获取工单详情失败:', error);
            res.status(500).json({
                success: false,
                message: '获取工单详情失败',
                error: error.message
            });
        }
    }

    /**
     * 创建工单
     */
    async createTicket(req, res) {
        try {
            const { title, content, priority = 0 } = req.body;

            if (!title || !content) {
                return res.status(400).json({
                    success: false,
                    message: '标题和内容不能为空'
                });
            }

            // 处理上传的图片
            let processedContent = content;
            if (req.files && req.files.length > 0) {
                processedContent = await TicketService.processUploadedImages(content, req.files);
            }

            const ticketData = {
                title,
                content: processedContent,
                priority: priority // 使用字符串形式，让TicketManager处理转换
            };

            const ticket = await this.ticketManager.createTicket(ticketData);

            // 发送WebSocket事件通知
            if (this.wsService) {
                this.wsService.broadcastToRoom('tickets', {
                    type: 'ticket_created',
                    data: ticket,
                    timestamp: new Date().toISOString()
                });

                // 特别通知AI助手
                this.wsService.broadcastToRoom('ai-assistant', {
                    type: 'new_ticket_available',
                    data: ticket,
                    timestamp: new Date().toISOString()
                });
            }

            res.status(201).json({
                success: true,
                message: '工单创建成功',
                data: ticket
            });
        } catch (error) {
            console.error('创建工单失败:', error);
            res.status(500).json({
                success: false,
                message: '创建工单失败',
                error: error.message
            });
        }
    }

    /**
     * 更新工单
     */
    async updateTicket(req, res) {
        try {
            const { id } = req.params;
            const { title, content, priority, status } = req.body;

            const ticket = await TicketModel.findById(id);
            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '工单不存在'
                });
            }

            // 检查是否可以编辑
            if (!['待开始', '待补充信息', '已挂起'].includes(ticket.status)) {
                return res.status(400).json({
                    success: false,
                    message: '当前状态下不允许编辑工单'
                });
            }

            const updateData = {};
            if (title) updateData.title = title;
            if (content) updateData.content = content;
            if (priority !== undefined) updateData.priority = parseInt(priority);
            if (status) updateData.status = status;

            // 处理上传的图片
            if (req.files && req.files.length > 0 && content) {
                updateData.content = await TicketService.processUploadedImages(content, req.files);
            }

            const updatedTicket = await TicketModel.update(id, updateData);

            res.json({
                success: true,
                message: '工单更新成功',
                data: updatedTicket
            });
        } catch (error) {
            console.error('更新工单失败:', error);
            res.status(500).json({
                success: false,
                message: '更新工单失败',
                error: error.message
            });
        }
    }

    /**
     * 更新工单状态
     */
    async updateTicketStatus(req, res) {
        try {
            const { id } = req.params;
            const { status, notes } = req.body;

            if (!status) {
                return res.status(400).json({
                    success: false,
                    message: '状态不能为空'
                });
            }

            const validStatuses = ['待开始', '排队中', '处理中', '待补充信息', '已完成', '已挂起', '处理失败'];
            if (!validStatuses.includes(status)) {
                return res.status(400).json({
                    success: false,
                    message: '无效的状态值'
                });
            }

            const updatedTicket = await TicketModel.updateStatus(id, status, notes);

            // 发送WebSocket事件通知
            if (this.wsService) {
                this.wsService.broadcastToRoom('tickets', {
                    type: 'ticket_status_changed',
                    data: updatedTicket,
                    changes: { status, notes },
                    timestamp: new Date().toISOString()
                });

                // 如果状态变为"待开始"，通知AI助手
                if (status === '待开始') {
                    this.wsService.broadcastToRoom('ai-assistant', {
                        type: 'new_ticket_available',
                        data: updatedTicket,
                        timestamp: new Date().toISOString()
                    });
                }

                // 如果状态变为"已挂起"，通知AI助手停止处理
                if (status === '已挂起') {
                    this.wsService.broadcastToRoom('ai-assistant', {
                        type: 'ticket_status_changed',
                        data: updatedTicket,
                        status: '已挂起',
                        timestamp: new Date().toISOString()
                    });
                }
            }

            res.json({
                success: true,
                message: '工单状态更新成功',
                data: updatedTicket
            });
        } catch (error) {
            console.error('更新工单状态失败:', error);
            res.status(500).json({
                success: false,
                message: '更新工单状态失败',
                error: error.message
            });
        }
    }

    /**
     * 批量更新工单状态
     */
    async batchUpdateStatus(req, res) {
        try {
            const { ticketIds, status, notes } = req.body;

            if (!ticketIds || !Array.isArray(ticketIds) || ticketIds.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '工单ID列表不能为空'
                });
            }

            if (!status) {
                return res.status(400).json({
                    success: false,
                    message: '状态不能为空'
                });
            }

            const validStatuses = ['待开始', '排队中', '处理中', '待补充信息', '已完成', '已挂起', '处理失败'];
            if (!validStatuses.includes(status)) {
                return res.status(400).json({
                    success: false,
                    message: '无效的状态值'
                });
            }

            const results = [];
            const errors = [];

            for (const ticketId of ticketIds) {
                try {
                    const updatedTicket = await TicketModel.updateStatus(ticketId, status, notes);
                    results.push(updatedTicket);
                } catch (error) {
                    errors.push({
                        ticketId,
                        error: error.message
                    });
                }
            }

            res.json({
                success: true,
                message: `成功更新 ${results.length} 个工单状态`,
                data: {
                    updated: results,
                    errors: errors
                }
            });
        } catch (error) {
            console.error('批量更新工单状态失败:', error);
            res.status(500).json({
                success: false,
                message: '批量更新工单状态失败',
                error: error.message
            });
        }
    }

    /**
     * 删除工单
     */
    async deleteTicket(req, res) {
        try {
            const { id } = req.params;

            const ticket = await TicketModel.findById(id);
            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '工单不存在'
                });
            }

            // 检查是否可以删除
            const deletableStatuses = ['待开始', '排队中', '已挂起', '处理失败'];
            if (!deletableStatuses.includes(ticket.status)) {
                return res.status(400).json({
                    success: false,
                    message: '当前状态下不允许删除工单'
                });
            }

            // 删除相关任务
            await TaskModel.deleteTasksByTicketId(id);

            // 删除工单
            await TicketModel.delete(id);

            res.json({
                success: true,
                message: '工单删除成功'
            });
        } catch (error) {
            console.error('删除工单失败:', error);
            res.status(500).json({
                success: false,
                message: '删除工单失败',
                error: error.message
            });
        }
    }

    /**
     * 批量删除工单
     */
    async batchDeleteTickets(req, res) {
        try {
            const { ticketIds } = req.body;

            if (!ticketIds || !Array.isArray(ticketIds) || ticketIds.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '工单ID列表不能为空'
                });
            }

            const results = [];
            const errors = [];
            const deletableStatuses = ['待开始', '已挂起', '已完成'];

            for (const ticketId of ticketIds) {
                try {
                    const ticket = await TicketModel.findById(ticketId);
                    if (!ticket) {
                        errors.push({
                            ticketId,
                            error: '工单不存在'
                        });
                        continue;
                    }

                    if (!deletableStatuses.includes(ticket.status)) {
                        errors.push({
                            ticketId,
                            error: '当前状态下不允许删除'
                        });
                        continue;
                    }

                    // 删除相关任务
                    await TaskModel.deleteTasksByTicketId(ticketId);

                    // 删除工单
                    await TicketModel.delete(ticketId);

                    results.push(ticketId);
                } catch (error) {
                    errors.push({
                        ticketId,
                        error: error.message
                    });
                }
            }

            res.json({
                success: true,
                message: `成功删除 ${results.length} 个工单`,
                data: {
                    deleted: results,
                    errors: errors
                }
            });
        } catch (error) {
            console.error('批量删除工单失败:', error);
            res.status(500).json({
                success: false,
                message: '批量删除工单失败',
                error: error.message
            });
        }
    }

    /**
     * 搜索工单
     */
    async searchTickets(req, res) {
        try {
            const filters = req.body;
            const tickets = await TicketModel.search(filters);

            res.json({
                success: true,
                data: tickets,
                count: tickets.length
            });
        } catch (error) {
            console.error('搜索工单失败:', error);
            res.status(500).json({
                success: false,
                message: '搜索工单失败',
                error: error.message
            });
        }
    }

    /**
     * 获取工单统计信息
     */
    async getStatistics(req, res) {
        try {
            const stats = await TicketModel.getStatistics();

            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            console.error('获取工单统计失败:', error);
            res.status(500).json({
                success: false,
                message: '获取工单统计失败',
                error: error.message
            });
        }
    }

    /**
     * 获取工单任务列表
     */
    async getTicketTasks(req, res) {
        try {
            const { id } = req.params;

            const ticket = await TicketModel.findById(id);
            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '工单不存在'
                });
            }

            const tasks = await TaskModel.getTasksByTicketId(id);

            res.json({
                success: true,
                data: tasks
            });
        } catch (error) {
            console.error('获取工单任务失败:', error);
            res.status(500).json({
                success: false,
                message: '获取工单任务失败',
                error: error.message
            });
        }
    }

    /**
     * 获取工单进度
     */
    async getTicketProgress(req, res) {
        try {
            const { id } = req.params;

            const ticket = await TicketModel.findById(id);
            if (!ticket) {
                return res.status(404).json({
                    success: false,
                    message: '工单不存在'
                });
            }

            const progress = await TaskModel.getTicketProgress(id);

            res.json({
                success: true,
                data: {
                    ticket_id: id,
                    ticket_status: ticket.status,
                    ...progress
                }
            });
        } catch (error) {
            console.error('获取工单进度失败:', error);
            res.status(500).json({
                success: false,
                message: '获取工单进度失败',
                error: error.message
            });
        }
    }
}

module.exports = TicketController;