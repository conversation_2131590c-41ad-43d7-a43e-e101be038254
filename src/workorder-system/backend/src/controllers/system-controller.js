const db = require('../../../../shared/database/connection');
const TicketModel = require('../../../../shared/database/models/ticket');
const TaskModel = require('../../../../shared/database/models/task');
const axios = require('axios');

/**
 * 获取AI助手状态
 */
async function getAIAssistantStatus() {
    try {
        // 尝试连接AI助手服务
        const response = await axios.get('http://localhost:3002/status', {
            timeout: 3000
        });

        if (response.data && response.data.success) {
            return {
                status: 'online',
                workers: response.data.data.workers || 1,
                queue_size: response.data.data.queue_size || 0,
                last_activity: response.data.data.last_activity,
                uptime: response.data.data.uptime
            };
        } else {
            return {
                status: 'error',
                workers: 0,
                queue_size: 0,
                error: 'AI助手响应异常'
            };
        }
    } catch (error) {
        console.log('AI助手连接失败:', error.message);
        return {
            status: 'offline',
            workers: 0,
            queue_size: 0,
            error: error.message
        };
    }
}

class SystemController {
    /**
     * 获取系统配置
     */
    async getSystemConfig(req, res) {
        try {
            const sql = 'SELECT * FROM system_config ORDER BY key';
            const configs = await db.all(sql);

            const configMap = {};
            configs.forEach(config => {
                configMap[config.key] = {
                    value: config.value,
                    description: config.description,
                    updated_at: config.updated_at
                };
            });

            res.json({
                success: true,
                data: configMap
            });
        } catch (error) {
            console.error('获取系统配置失败:', error);
            res.status(500).json({
                success: false,
                message: '获取系统配置失败',
                error: error.message
            });
        }
    }

    /**
     * 更新系统配置
     */
    async updateSystemConfig(req, res) {
        try {
            const { configs } = req.body;

            if (!configs || typeof configs !== 'object') {
                return res.status(400).json({
                    success: false,
                    message: '配置数据格式错误'
                });
            }

            const results = [];
            const errors = [];

            for (const [key, value] of Object.entries(configs)) {
                try {
                    const sql = `
                        INSERT OR REPLACE INTO system_config (key, value, updated_at)
                        VALUES (?, ?, CURRENT_TIMESTAMP)
                    `;
                    await db.run(sql, [key, value]);
                    results.push({ key, value });
                } catch (error) {
                    errors.push({ key, error: error.message });
                }
            }

            res.json({
                success: true,
                message: `成功更新 ${results.length} 个配置项`,
                data: {
                    updated: results,
                    errors: errors
                }
            });
        } catch (error) {
            console.error('更新系统配置失败:', error);
            res.status(500).json({
                success: false,
                message: '更新系统配置失败',
                error: error.message
            });
        }
    }

    /**
     * 获取系统状态
     */
    async getSystemStatus(req, res) {
        try {
            // 获取数据库状态
            const dbHealth = await db.healthCheck();

            // 获取工单统计
            const ticketStats = await TicketModel.getStatistics();

            // 获取任务统计
            const taskStats = await TaskModel.getTaskStatistics();

            // 获取系统配置
            const configSql = 'SELECT key, value FROM system_config';
            const configs = await db.all(configSql);
            const configMap = {};
            configs.forEach(config => {
                configMap[config.key] = config.value;
            });

            // 获取正在处理的工单
            const processingTickets = await TicketModel.getProcessingTickets();

            // 获取AI助手状态
            const aiAssistantStatus = await getAIAssistantStatus();

            res.json({
                success: true,
                data: {
                    status: 'healthy',
                    uptime: process.uptime(),
                    memory: {
                        used: process.memoryUsage().heapUsed,
                        total: process.memoryUsage().heapTotal,
                        percentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100)
                    },
                    database: {
                        connected: dbHealth,
                        tables: 5, // tickets, tasks, system_config, etc.
                        records: ticketStats.total + taskStats.total
                    },
                    websocket: {
                        connected: true,
                        clients: 1 // 简化处理，实际应该从WebSocket服务获取
                    },
                    ai_assistant: aiAssistantStatus,
                    tickets: ticketStats,
                    tasks: taskStats,
                    processing: {
                        active_tickets: processingTickets.length,
                        tickets: processingTickets
                    },
                    config: configMap,
                    server: {
                        uptime: process.uptime(),
                        memory: process.memoryUsage(),
                        version: process.version,
                        platform: process.platform
                    },
                    timestamp: new Date().toISOString()
                }
            });
        } catch (error) {
            console.error('获取系统状态失败:', error);
            res.status(500).json({
                success: false,
                message: '获取系统状态失败',
                error: error.message
            });
        }
    }

    /**
     * 获取系统统计信息
     */
    async getSystemStatistics(req, res) {
        try {
            const { timeRange = '7d' } = req.query;

            // 计算时间范围
            let startDate;
            const endDate = new Date();

            switch (timeRange) {
                case '1d':
                    startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case '7d':
                    startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case '30d':
                    startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
            }

            // 获取时间范围内的工单统计
            const ticketStatsSql = `
                SELECT
                    DATE(created_at) as date,
                    status,
                    COUNT(*) as count
                FROM tickets
                WHERE created_at >= ? AND created_at <= ?
                GROUP BY DATE(created_at), status
                ORDER BY date DESC
            `;
            const ticketStats = await db.all(ticketStatsSql, [
                startDate.toISOString(),
                endDate.toISOString()
            ]);

            // 获取平均处理时间
            const avgProcessingTimeSql = `
                SELECT AVG(processing_time) as avg_time
                FROM tickets
                WHERE status = '已完成'
                AND completed_at >= ? AND completed_at <= ?
            `;
            const avgTimeResult = await db.get(avgProcessingTimeSql, [
                startDate.toISOString(),
                endDate.toISOString()
            ]);

            res.json({
                success: true,
                data: {
                    time_range: timeRange,
                    start_date: startDate.toISOString(),
                    end_date: endDate.toISOString(),
                    ticket_stats: ticketStats,
                    avg_processing_time: avgTimeResult?.avg_time || 0
                }
            });
        } catch (error) {
            console.error('获取系统统计失败:', error);
            res.status(500).json({
                success: false,
                message: '获取系统统计失败',
                error: error.message
            });
        }
    }

    /**
     * 获取操作指引版本
     */
    async getGuideVersion(req, res) {
        try {
            const sql = 'SELECT value FROM system_config WHERE key = ?';
            const result = await db.get(sql, ['guide_version']);

            res.json({
                success: true,
                data: {
                    version: result?.value || '1.0.0'
                }
            });
        } catch (error) {
            console.error('获取操作指引版本失败:', error);
            res.status(500).json({
                success: false,
                message: '获取操作指引版本失败',
                error: error.message
            });
        }
    }

    /**
     * 更新操作指引版本
     */
    async updateGuideVersion(req, res) {
        try {
            const { version } = req.body;

            if (!version) {
                return res.status(400).json({
                    success: false,
                    message: '版本号不能为空'
                });
            }

            const sql = `
                INSERT OR REPLACE INTO system_config (key, value, updated_at)
                VALUES ('guide_version', ?, CURRENT_TIMESTAMP)
            `;
            await db.run(sql, [version]);

            res.json({
                success: true,
                message: '操作指引版本更新成功',
                data: { version }
            });
        } catch (error) {
            console.error('更新操作指引版本失败:', error);
            res.status(500).json({
                success: false,
                message: '更新操作指引版本失败',
                error: error.message
            });
        }
    }

    /**
     * 系统健康检查
     */
    async healthCheck(req, res) {
        try {
            const dbHealth = await db.healthCheck();

            const health = {
                status: 'ok',
                timestamp: new Date().toISOString(),
                checks: {
                    database: dbHealth ? 'healthy' : 'unhealthy',
                    memory: this.checkMemoryUsage(),
                    uptime: process.uptime()
                }
            };

            const statusCode = dbHealth ? 200 : 503;
            res.status(statusCode).json({
                success: dbHealth,
                data: health
            });
        } catch (error) {
            console.error('健康检查失败:', error);
            res.status(503).json({
                success: false,
                message: '健康检查失败',
                error: error.message
            });
        }
    }



    /**
     * 启动AI助手
     */
    async startAIAssistant(req, res) {
        try {
            const response = await axios.post('http://localhost:3002/start', {}, {
                timeout: 10000
            });

            res.json({
                success: true,
                message: 'AI助手启动成功',
                data: response.data
            });
        } catch (error) {
            console.error('启动AI助手失败:', error);
            res.status(500).json({
                success: false,
                message: '启动AI助手失败',
                error: error.message
            });
        }
    }

    /**
     * 停止AI助手
     */
    async stopAIAssistant(req, res) {
        try {
            const response = await axios.post('http://localhost:3002/stop', {}, {
                timeout: 10000
            });

            res.json({
                success: true,
                message: 'AI助手已停止',
                data: response.data
            });
        } catch (error) {
            console.error('停止AI助手失败:', error);
            res.status(500).json({
                success: false,
                message: '停止AI助手失败',
                error: error.message
            });
        }
    }

    /**
     * 检查内存使用情况
     */
    checkMemoryUsage() {
        const usage = process.memoryUsage();
        const totalMB = Math.round(usage.heapTotal / 1024 / 1024);
        const usedMB = Math.round(usage.heapUsed / 1024 / 1024);
        const usagePercent = Math.round((usage.heapUsed / usage.heapTotal) * 100);

        return {
            total_mb: totalMB,
            used_mb: usedMB,
            usage_percent: usagePercent,
            status: usagePercent > 90 ? 'warning' : 'healthy'
        };
    }
}

module.exports = new SystemController();