const WebSocket = require('ws');
const url = require('url');

class WebSocketService {
    constructor(server) {
        this.server = server;
        this.wss = null;
        this.clients = new Map(); // 存储客户端连接信息
        this.rooms = new Map(); // 存储房间信息
    }

    /**
     * 启动WebSocket服务
     */
    start() {
        try {
            this.wss = new WebSocket.Server({
                server: this.server,
                path: '/ws'
            });

            this.wss.on('connection', (ws, request) => {
                this.handleConnection(ws, request);
            });

            console.log('WebSocket服务启动成功');
        } catch (error) {
            console.error('WebSocket服务启动失败:', error);
            throw error;
        }
    }

    /**
     * 处理新的WebSocket连接
     */
    handleConnection(ws, request) {
        try {
            // 解析连接参数
            const query = url.parse(request.url, true).query;
            const clientId = query.clientId || this.generateClientId();
            const clientType = query.type || 'unknown'; // workorder, ai-assistant, etc.

            // 存储客户端信息
            const clientInfo = {
                id: clientId,
                type: clientType,
                ws: ws,
                connectedAt: new Date(),
                lastPing: new Date()
            };

            this.clients.set(clientId, clientInfo);

            console.log(`WebSocket客户端连接: ${clientId} (${clientType})`);

            // 发送连接确认消息
            this.sendToClient(clientId, {
                type: 'connection_established',
                data: {
                    clientId: clientId,
                    serverTime: new Date().toISOString()
                }
            });

            // 设置消息处理器
            ws.on('message', (message) => {
                this.handleMessage(clientId, message);
            });

            // 设置关闭处理器
            ws.on('close', () => {
                this.handleDisconnection(clientId);
            });

            // 设置错误处理器
            ws.on('error', (error) => {
                console.error(`WebSocket客户端错误 ${clientId}:`, error);
                this.handleDisconnection(clientId);
            });

            // 设置心跳检测
            this.setupHeartbeat(clientId);

        } catch (error) {
            console.error('处理WebSocket连接失败:', error);
            ws.close();
        }
    }

    /**
     * 处理客户端消息
     */
    handleMessage(clientId, message) {
        try {
            const data = JSON.parse(message);
            const client = this.clients.get(clientId);

            if (!client) {
                console.warn(`收到未知客户端消息: ${clientId}`);
                return;
            }

            // 更新最后活跃时间
            client.lastPing = new Date();

            console.log(`收到客户端消息 ${clientId}:`, data.type);

            switch (data.type) {
                case 'ping':
                    this.handlePing(clientId);
                    break;
                case 'join_room':
                    this.handleJoinRoom(clientId, data.room);
                    break;
                case 'leave_room':
                    this.handleLeaveRoom(clientId, data.room);
                    break;
                case 'ticket_update':
                    this.handleTicketUpdate(clientId, data.data);
                    break;
                case 'task_update':
                    this.handleTaskUpdate(clientId, data.data);
                    break;
                case 'system_status':
                    this.handleSystemStatus(clientId, data.data);
                    break;
                case 'register':
                    this.handleClientRegister(clientId, data);
                    break;
                case 'client_connected':
                    this.handleClientConnected(clientId, data);
                    break;
                case 'heartbeat':
                    this.handleHeartbeat(clientId, data);
                    break;
                case 'subscribe_tickets':
                    this.handleSubscribeTickets(clientId, data);
                    break;
                case 'unsubscribe_tickets':
                    this.handleUnsubscribeTickets(clientId, data);
                    break;
                default:
                    console.warn(`未知消息类型: ${data.type}`);
            }
        } catch (error) {
            console.error(`处理客户端消息失败 ${clientId}:`, error);
        }
    }

    /**
     * 处理客户端断开连接
     */
    handleDisconnection(clientId) {
        const client = this.clients.get(clientId);
        if (client) {
            console.log(`WebSocket客户端断开连接: ${clientId} (${client.type})`);

            // 从所有房间中移除客户端
            this.rooms.forEach((room, roomName) => {
                if (room.has(clientId)) {
                    room.delete(clientId);
                    if (room.size === 0) {
                        this.rooms.delete(roomName);
                    }
                }
            });

            // 移除客户端
            this.clients.delete(clientId);
        }
    }

    /**
     * 处理心跳消息
     */
    handlePing(clientId) {
        this.sendToClient(clientId, {
            type: 'pong',
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 处理加入房间
     */
    handleJoinRoom(clientId, roomName) {
        if (!this.rooms.has(roomName)) {
            this.rooms.set(roomName, new Set());
        }

        this.rooms.get(roomName).add(clientId);

        this.sendToClient(clientId, {
            type: 'room_joined',
            data: { room: roomName }
        });

        console.log(`客户端 ${clientId} 加入房间: ${roomName}`);
    }

    /**
     * 处理离开房间
     */
    handleLeaveRoom(clientId, roomName) {
        const room = this.rooms.get(roomName);
        if (room) {
            room.delete(clientId);
            if (room.size === 0) {
                this.rooms.delete(roomName);
            }
        }

        this.sendToClient(clientId, {
            type: 'room_left',
            data: { room: roomName }
        });

        console.log(`客户端 ${clientId} 离开房间: ${roomName}`);
    }

    /**
     * 处理工单更新
     */
    handleTicketUpdate(clientId, data) {
        // 广播工单更新到所有相关客户端
        this.broadcastToRoom('tickets', {
            type: 'ticket_updated',
            data: data,
            from: clientId,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 处理任务更新
     */
    handleTaskUpdate(clientId, data) {
        // 广播任务更新到所有相关客户端
        this.broadcastToRoom('tasks', {
            type: 'task_updated',
            data: data,
            from: clientId,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 处理系统状态更新
     */
    handleSystemStatus(clientId, data) {
        // 广播系统状态到所有客户端
        this.broadcastToAll({
            type: 'system_status_updated',
            data: data,
            from: clientId,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 处理客户端注册
     */
    handleClientRegister(clientId, data) {
        const client = this.clients.get(clientId);
        if (client) {
            client.type = data.clientType || client.type;
            client.registeredAt = new Date();
            console.log(`客户端注册: ${clientId} (${client.type})`);

            // 发送注册确认
            this.sendToClient(clientId, {
                type: 'register_confirmed',
                data: {
                    clientId: clientId,
                    clientType: client.type,
                    timestamp: new Date().toISOString()
                }
            });

            // 如果是AI助手，自动订阅工单事件
            if (client.type === 'ai-assistant') {
                this.handleJoinRoom(clientId, 'ai-assistant');
                this.handleJoinRoom(clientId, 'tickets');
            }
        }
    }

    /**
     * 处理客户端连接确认
     */
    handleClientConnected(clientId, data) {
        const client = this.clients.get(clientId);
        if (client) {
            client.clientType = data.client_type || 'unknown';
            console.log(`客户端连接确认: ${clientId} (${client.clientType})`);

            // 如果是前端客户端，自动订阅工单事件
            if (client.clientType === 'workorder_frontend') {
                this.handleJoinRoom(clientId, 'frontend');
                this.handleJoinRoom(clientId, 'tickets');
            }
        }
    }

    /**
     * 处理心跳消息
     */
    handleHeartbeat(clientId, data) {
        const client = this.clients.get(clientId);
        if (client) {
            client.lastPing = new Date();

            // 发送心跳响应
            this.sendToClient(clientId, {
                type: 'heartbeat_response',
                data: {
                    timestamp: new Date().toISOString(),
                    serverTime: Date.now()
                }
            });
        }
    }

    /**
     * 处理订阅工单事件
     */
    handleSubscribeTickets(clientId, data) {
        this.handleJoinRoom(clientId, 'tickets');
        console.log(`客户端 ${clientId} 订阅工单事件`);

        this.sendToClient(clientId, {
            type: 'subscription_confirmed',
            data: {
                room: 'tickets',
                timestamp: new Date().toISOString()
            }
        });
    }

    /**
     * 处理取消订阅工单事件
     */
    handleUnsubscribeTickets(clientId, data) {
        this.handleLeaveRoom(clientId, 'tickets');
        console.log(`客户端 ${clientId} 取消订阅工单事件`);

        this.sendToClient(clientId, {
            type: 'unsubscription_confirmed',
            data: {
                room: 'tickets',
                timestamp: new Date().toISOString()
            }
        });
    }

    /**
     * 发送消息给指定客户端
     */
    sendToClient(clientId, message) {
        const client = this.clients.get(clientId);
        if (client && client.ws.readyState === WebSocket.OPEN) {
            try {
                client.ws.send(JSON.stringify(message));
                return true;
            } catch (error) {
                console.error(`发送消息给客户端失败 ${clientId}:`, error);
                this.handleDisconnection(clientId);
                return false;
            }
        }
        return false;
    }

    /**
     * 广播消息到指定房间
     */
    broadcastToRoom(roomName, message) {
        const room = this.rooms.get(roomName);
        if (!room) {
            return 0;
        }

        let successCount = 0;
        room.forEach(clientId => {
            if (this.sendToClient(clientId, message)) {
                successCount++;
            }
        });

        return successCount;
    }

    /**
     * 广播消息到所有客户端
     */
    broadcastToAll(message) {
        let successCount = 0;
        this.clients.forEach((client, clientId) => {
            if (this.sendToClient(clientId, message)) {
                successCount++;
            }
        });

        return successCount;
    }

    /**
     * 广播消息到指定类型的客户端
     */
    broadcastToType(clientType, message) {
        let successCount = 0;
        this.clients.forEach((client, clientId) => {
            if (client.type === clientType) {
                if (this.sendToClient(clientId, message)) {
                    successCount++;
                }
            }
        });

        return successCount;
    }

    /**
     * 设置心跳检测
     */
    setupHeartbeat(clientId) {
        const client = this.clients.get(clientId);
        if (!client) return;

        // 每30秒检查一次客户端状态
        const heartbeatInterval = setInterval(() => {
            const currentClient = this.clients.get(clientId);
            if (!currentClient) {
                clearInterval(heartbeatInterval);
                return;
            }

            const now = new Date();
            const timeSinceLastPing = now - currentClient.lastPing;

            // 如果超过60秒没有收到心跳，断开连接
            if (timeSinceLastPing > 60000) {
                console.log(`客户端心跳超时，断开连接: ${clientId}`);
                currentClient.ws.close();
                clearInterval(heartbeatInterval);
                return;
            }

            // 发送心跳请求
            this.sendToClient(clientId, {
                type: 'ping_request',
                timestamp: now.toISOString()
            });
        }, 30000);
    }

    /**
     * 生成客户端ID
     */
    generateClientId() {
        return 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 获取连接统计信息
     */
    getConnectionStats() {
        const stats = {
            total_clients: this.clients.size,
            clients_by_type: {},
            rooms: {},
            uptime: process.uptime()
        };

        // 统计不同类型的客户端数量
        this.clients.forEach(client => {
            stats.clients_by_type[client.type] = (stats.clients_by_type[client.type] || 0) + 1;
        });

        // 统计房间信息
        this.rooms.forEach((clients, roomName) => {
            stats.rooms[roomName] = clients.size;
        });

        return stats;
    }

    /**
     * 关闭WebSocket服务
     */
    close() {
        if (this.wss) {
            // 通知所有客户端服务即将关闭
            this.broadcastToAll({
                type: 'server_shutdown',
                message: '服务器即将关闭',
                timestamp: new Date().toISOString()
            });

            // 关闭所有连接
            this.clients.forEach((client, clientId) => {
                client.ws.close();
            });

            // 关闭WebSocket服务器
            this.wss.close();
            console.log('WebSocket服务已关闭');
        }
    }
}

module.exports = WebSocketService;