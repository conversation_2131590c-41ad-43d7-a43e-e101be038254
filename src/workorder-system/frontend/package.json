{"name": "workorder-frontend", "version": "1.0.0", "description": "工单系统前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^10.7.1", "axios": "^1.6.5", "dayjs": "^1.11.10", "dompurify": "^3.0.7", "element-plus": "^2.4.4", "marked": "^11.2.0", "md-editor-v3": "^5.7.1", "pinia": "^2.1.7", "quill": "^2.0.3", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "prettier": "^3.2.4", "unplugin-auto-import": "^0.17.3", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.11"}, "keywords": ["vue", "workorder", "frontend", "element-plus"], "author": "RPA Team", "license": "MIT"}