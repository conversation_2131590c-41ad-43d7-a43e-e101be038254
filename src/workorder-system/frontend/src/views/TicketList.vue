<template>
  <div class="ticket-list-container">
    <!-- 页面头部 - 紧凑设计 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">工单管理</h2>
        <p class="page-subtitle">管理和跟踪所有自动化工单</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          启动AI助手
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-item">
        <span class="stats-number">{{ stats.total }}</span>
        <span class="stats-label">总计</span>
      </div>
      <div class="stats-item">
        <span class="stats-number orange">{{ stats.pending }}</span>
        <span class="stats-label">待处理</span>
      </div>
      <div class="stats-item">
        <span class="stats-number blue">{{ stats.processing }}</span>
        <span class="stats-label">处理中</span>
      </div>
      <div class="stats-item">
        <span class="stats-number green">{{ stats.completed }}</span>
        <span class="stats-label">已完成</span>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-input
          v-model="filters.search"
          placeholder="搜索工单号或标题内容..."
          clearable
          style="width: 300px"
          @keyup.enter="loadTickets"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-select
          v-model="filters.status"
          placeholder="全部状态"
          clearable
          style="width: 120px"
          @change="loadTickets"
        >
          <el-option label="待开始" value="待开始" />
          <el-option label="排队中" value="排队中" />
          <el-option label="处理中" value="处理中" />
          <el-option label="待补充信息" value="待补充信息" />
          <el-option label="已完成" value="已完成" />
          <el-option label="处理失败" value="处理失败" />
          <el-option label="已挂起" value="已挂起" />
        </el-select>

        <el-select
          v-model="filters.priority"
          placeholder="全部优先级"
          clearable
          style="width: 120px"
          @change="loadTickets"
        >
          <el-option label="低" :value="0" />
          <el-option label="中" :value="1" />
          <el-option label="高" :value="2" />
          <el-option label="紧急" :value="3" />
        </el-select>

        <el-button :icon="Filter" @click="loadTickets">筛选</el-button>
      </div>

      <div class="action-right">
        <el-button :icon="Refresh" @click="loadTickets" :loading="loading">刷新</el-button>
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">新建工单</el-button>
        <span class="result-count">显示 6 条结果</span>
        <el-button :icon="View" circle />
        <el-button :icon="MoreFilled" circle />
      </div>
    </div>

    <!-- 工单列表 -->
    <div class="work-list-section">
      <div class="list-header">
        <h3 class="list-title">工单列表</h3>
        <span class="list-count">共 6 条记录</span>
        <div class="list-actions">
          <el-button :icon="View" circle size="small" />
          <el-button :icon="MoreFilled" circle size="small" />
        </div>
      </div>

      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tickets"
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @row-click="handleRowClick"
        row-key="id"
        class="work-table"
      >
        <!-- 工单号列 -->
        <el-table-column
          prop="ticket_number"
          label="工单号"
          width="140"
          sortable="custom"
        >
          <template #default="{ row }">
            <el-link
              type="primary"
              @click.stop="viewTicketDetail(row)"
              class="ticket-link"
            >
              {{ row.ticket_number }}
            </el-link>
          </template>
        </el-table-column>

        <!-- 工单标题 -->
        <el-table-column
          prop="title"
          label="工单标题"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="title-cell">
              <div class="title-text">{{ row.title }}</div>
              <div v-if="row.notes" class="title-notes">{{ formatNotes(row.notes) }}</div>
            </div>
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column
          prop="status"
          label="状态"
          width="100"
        >
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 优先级 -->
        <el-table-column
          prop="priority"
          label="优先级"
          width="80"
        >
          <template #default="{ row }">
            <el-tag
              :type="getPriorityType(row.priority)"
              size="small"
            >
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 进度 -->
        <el-table-column
          label="进度"
          width="100"
        >
          <template #default="{ row }">
            <div class="progress-cell">
              <el-progress
                :percentage="getProgress(row.status)"
                :stroke-width="6"
                :show-text="false"
                :color="getProgressColor(row.status)"
              />
              <span class="progress-text">{{ getProgress(row.status) }}%</span>
            </div>
          </template>
        </el-table-column>

        <!-- 负责人 -->
        <el-table-column
          prop="assigned_to"
          label="负责人"
          width="100"
        >
          <template #default="{ row }">
            <div class="assignee-cell">
              <el-avatar :size="24" class="assignee-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="assignee-name">{{ row.assigned_to || '张三' }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column
          prop="created_at"
          label="创建时间"
          width="140"
          sortable="custom"
        >
          <template #default="{ row }">
            <div class="time-cell">
              <div class="time-date">{{ formatDate(row.created_at) }}</div>
              <div class="time-time">{{ formatTime(row.created_at) }}</div>
            </div>
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column
          label="操作"
          width="120"
          fixed="right"
        >
          <template #default="{ row }">
            <div class="action-cell">
              <div class="action-row">
                <el-button :icon="View" circle size="small" @click.stop="viewTicketDetail(row)" />
                <el-button :icon="Edit" circle size="small" @click.stop="editTicket(row)" />
                <el-button :icon="Play" circle size="small" @click.stop="startTicket(row)" />
              </div>
              <div class="action-row">
                <el-dropdown @command="handleCommand" trigger="click">
                  <el-button :icon="MoreFilled" circle size="small" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="copy" :icon="DocumentCopy">复制工单</el-dropdown-item>
                      <el-dropdown-item command="assign" :icon="User">分配任务</el-dropdown-item>
                      <el-dropdown-item command="delete" :icon="Delete" divided>删除工单</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 - 紧凑设计 -->
    <div class="pagination-section">
      <div class="pagination-content">
        <div class="pagination-info">
          <span class="info-text">每页显示</span>
          <el-select
            v-model="pagination.pageSize"
            size="small"
            style="width: 60px"
            @change="handlePageSizeChange"
          >
            <el-option label="20" :value="20" />
            <el-option label="50" :value="50" />
            <el-option label="100" :value="100" />
          </el-select>
          <span class="info-text">条</span>
        </div>

        <div class="pagination-nav">
          <el-button-group>
            <el-button size="small" @click="handlePageChange(pagination.page - 1)" :disabled="pagination.page <= 1">上一页</el-button>
            <el-button size="small" @click="handlePageChange(pagination.page + 1)" :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)">下一页</el-button>
          </el-button-group>
        </div>

        <div class="pagination-summary">
          <span class="summary-text">第 1-6 条，共 6 条</span>
        </div>
      </div>
    </div>



    <!-- 创建/编辑工单对话框 -->
    <TicketFormDialog
      v-model="showCreateDialog"
      :ticket="editingTicket"
      :mode="dialogMode"
      @success="handleTicketSaved"
      @cancelled="handleDialogCancelled"
    />

    <!-- 工单详情对话框 -->
    <TicketDetailDialog
      v-model="showDetailDialog"
      :ticket="viewingTicket"
      @edit="handleEditFromDetail"
      @action="handleActionFromDetail"
    />

    <!-- 排序设置对话框 -->
    <SortSettingsDialog
      v-model="showSortDialog"
      :current-sort="currentSort"
      @apply="handleSortApply"
    />

    <!-- 列设置对话框 -->
    <ColumnSettingsDialog
      v-model="showColumnSettings"
      :settings="columnSettings"
      @apply="handleColumnSettingsApply"
    />

    <!-- 批量操作确认对话框 -->
    <BatchActionDialog
      v-model="showBatchDialog"
      :action="batchAction"
      :tickets="selectedTickets"
      @confirm="handleBatchConfirm"
    />


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Plus, Search, Refresh, RefreshRight, Sort, Setting,
  ArrowUp, ArrowDown, Minus, Loading, Document, Timer,
  View, Edit, MoreFilled, CaretRight, VideoPause,
  CopyDocument, Delete, Select, Filter, User, Play,
  DocumentCopy
} from '@element-plus/icons-vue'

// 导入组件
import TicketFormDialog from '@/components/TicketFormDialog.vue'
import TicketDetailDialog from '@/components/TicketDetailDialog.vue'
import SortSettingsDialog from '@/components/SortSettingsDialog.vue'
import ColumnSettingsDialog from '@/components/ColumnSettingsDialog.vue'
import BatchActionDialog from '@/components/BatchActionDialog.vue'
// 导入API和工具函数
import { ticketAPI } from '@/api/tickets'
import { formatDateTime, getRelativeTime, formatProcessingTime } from '@/utils/date'
import { useWebSocketStore } from '@/store/websocket'
import { useTicketsStore } from '@/store/tickets'

const router = useRouter()
const wsStore = useWebSocketStore()
const ticketsStore = useTicketsStore()

// 响应式数据
const tableRef = ref()

// 使用store状态
const loading = computed(() => ticketsStore.loading)
const tickets = computed(() => ticketsStore.paginatedTickets)
const selectedTickets = computed({
  get: () => ticketsStore.selectedTickets,
  set: (value) => ticketsStore.setSelectedTickets(value)
})

// 对话框状态
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showSortDialog = ref(false)
const showColumnSettings = ref(false)
const showBatchDialog = ref(false)
const showAdvancedFilter = ref(false)

// 编辑状态
const editingTicket = ref(null)
const viewingTicket = ref(null)
const dialogMode = ref('create') // 'create' | 'edit' | 'duplicate'

// 批量操作
const batchAction = ref('')

// 筛选条件
const filters = reactive({
  ticketNumber: '',
  status: '',
  priority: '',
  search: '',
  dateRange: [],
  assignedStatus: '',
  processingTime: ''
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 排序配置
const currentSort = reactive({
  prop: 'created_at',
  order: 'descending'
})

// 列设置
const columnSettings = reactive({
  showProgress: true,
  showNotes: true,
  showCreatedAt: true,
  showUpdatedAt: false,
  showProcessingTime: true
})

// 统计数据 - 使用store
const stats = computed(() => ticketsStore.stats)

// 辅助方法
const getStatusType = (status) => {
  const statusMap = {
    '待开始': '',
    '排队中': 'warning',
    '处理中': 'primary',
    '待补充信息': 'warning',
    '已完成': 'success',
    '处理失败': 'danger',
    '已挂起': 'info'
  }
  return statusMap[status] || ''
}

const getPriorityType = (priority) => {
  const priorityMap = {
    0: 'info',    // 低
    1: '',        // 中
    2: 'warning', // 高
    3: 'danger'   // 紧急
  }
  return priorityMap[priority] || ''
}

const getPriorityText = (priority) => {
  const priorityMap = {
    0: '低',
    1: '中',
    2: '高',
    3: '紧急'
  }
  return priorityMap[priority] || '中'
}

const getProgress = (status) => {
  const progressMap = {
    '待开始': 0,
    '排队中': 25,
    '处理中': 50,
    '待补充信息': 75,
    '已完成': 100,
    '处理失败': 0,
    '已挂起': 50
  }
  return progressMap[status] || 0
}

const getProgressColor = (status) => {
  const colorMap = {
    '待开始': '#909399',
    '排队中': '#E6A23C',
    '处理中': '#409EFF',
    '待补充信息': '#E6A23C',
    '已完成': '#67C23A',
    '处理失败': '#F56C6C',
    '已挂起': '#909399'
  }
  return colorMap[status] || '#409EFF'
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

const formatNotes = (notes) => {
  if (!notes) return ''
  if (typeof notes === 'string') return notes
  if (typeof notes === 'object') {
    // 如果是对象，尝试提取有用信息
    if (notes.message) return notes.message
    if (notes.summary) return notes.summary
    return JSON.stringify(notes)
  }
  return String(notes)
}

const startTicket = (ticket) => {
  handleAction('start', ticket)
}

const handleCommand = (command, ticket) => {
  handleAction(command, ticket)
}

// 计算属性
const canBatchStart = computed(() => {
  return selectedTickets.value.some(ticket => canStart(ticket.status))
})

const canBatchSuspend = computed(() => {
  return selectedTickets.value.some(ticket => canSuspend(ticket.status))
})

const canBatchDelete = computed(() => {
  return selectedTickets.value.some(ticket => canDelete(ticket.status))
})

const batchStartCount = computed(() => {
  return selectedTickets.value.filter(ticket => canStart(ticket.status)).length
})

const batchSuspendCount = computed(() => {
  return selectedTickets.value.filter(ticket => canSuspend(ticket.status)).length
})

const batchDeleteCount = computed(() => {
  return selectedTickets.value.filter(ticket => canDelete(ticket.status)).length
})

// 主要方法
const loadTickets = async (showLoading = true) => {
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...filters,
      sortBy: currentSort.prop,
      sortOrder: currentSort.order
    }

    // 处理日期范围
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
      delete params.dateRange
    }

    // 清理空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    // 使用store加载数据
    await ticketsStore.loadTickets(params)
    pagination.total = ticketsStore.total

  } catch (error) {
    ElMessage.error('加载工单列表失败: ' + error.message)
    console.error('Load tickets error:', error)
  }
}


const resetFilters = () => {
  Object.assign(filters, {
    ticketNumber: '',
    status: '',
    priority: '',
    search: '',
    dateRange: [],
    assignedStatus: '',
    processingTime: ''
  })
  // 使用store清除筛选条件
  ticketsStore.clearFilters()
  pagination.page = 1
  loadTickets()
}

// 表格事件处理
const handleSelectionChange = (selection) => {
  ticketsStore.setSelectedTickets(selection)
}

const handleSortChange = ({ prop, order }) => {
  currentSort.prop = prop
  currentSort.order = order
  pagination.page = 1
  loadTickets()
}

const handleRowClick = (row) => {
  // 单击行时切换选择状态
  const isSelected = selectedTickets.value.some(ticket => ticket.id === row.id)
  if (isSelected) {
    selectedTickets.value = selectedTickets.value.filter(ticket => ticket.id !== row.id)
  } else {
    selectedTickets.value.push(row)
  }

  // 更新表格选择状态
  nextTick(() => {
    tableRef.value?.toggleRowSelection(row, !isSelected)
  })
}

const clearSelection = () => {
  selectedTickets.value = []
  tableRef.value?.clearSelection()
}

// 分页事件处理
const handlePageChange = (page) => {
  pagination.page = page
  loadTickets()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadTickets()
}

// 工单操作方法
const viewTicketDetail = async (ticket) => {
  try {
    // 获取完整的工单详情（包含任务数据）
    const response = await ticketAPI.getTicketDetail(ticket.id)
    viewingTicket.value = response.data
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取工单详情失败: ' + error.message)
    console.error('获取工单详情失败:', error)
  }
}

const editTicket = (ticket) => {
  editingTicket.value = { ...ticket }
  dialogMode.value = 'edit'
  showCreateDialog.value = true
}

const duplicateTicket = (ticket) => {
  editingTicket.value = {
    ...ticket,
    id: null,
    ticket_number: '',
    status: '待开始',
    created_at: null,
    updated_at: null,
    completed_at: null,
    processing_time: null,
    assigned_worker_id: null
  }
  dialogMode.value = 'duplicate'
  showCreateDialog.value = true
}

const handleAction = async (command, ticket) => {
  try {
    switch (command) {
      case 'start':
        await updateTicketStatus(ticket.id, '排队中', '手动开始处理')
        break
      case 'suspend':
        await updateTicketStatus(ticket.id, '已挂起', '手动挂起')
        break
      case 'resume':
        await updateTicketStatus(ticket.id, '待开始', '恢复处理')
        break
      case 'duplicate':
        duplicateTicket(ticket)
        break

      case 'delete':
        await deleteTicket(ticket)
        break
    }
  } catch (error) {
    ElMessage.error('操作失败: ' + error.message)
  }
}

const updateTicketStatus = async (ticketId, status, notes = '') => {
  try {
    await ticketAPI.updateStatus(ticketId, { status, notes })
    ElMessage.success('状态更新成功')
    await loadTickets(false) // 不显示loading，保持用户体验

    // 发送WebSocket消息通知其他客户端
    wsStore.send({
      type: 'ticket_update',
      data: { ticketId, status, notes }
    })
  } catch (error) {
    throw new Error('状态更新失败: ' + error.message)
  }
}

const deleteTicket = async (ticket) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工单 "${ticket.ticket_number}" 吗？删除后无法恢复。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger'
      }
    )

    await ticketAPI.deleteTicket(ticket.id)
    ElMessage.success('删除成功')
    await loadTickets(false)

    // 从选中列表中移除
    selectedTickets.value = selectedTickets.value.filter(t => t.id !== ticket.id)

  } catch (error) {
    if (error !== 'cancel') {
      throw error
    }
  }
}



// 批量操作方法
const batchStart = async () => {
  const validTickets = selectedTickets.value.filter(ticket => canStart(ticket.status))
  if (validTickets.length === 0) {
    ElMessage.warning('没有可以开始的工单')
    return
  }

  batchAction.value = 'start'
  showBatchDialog.value = true
}

const batchSuspend = async () => {
  const validTickets = selectedTickets.value.filter(ticket => canSuspend(ticket.status))
  if (validTickets.length === 0) {
    ElMessage.warning('没有可以挂起的工单')
    return
  }

  batchAction.value = 'suspend'
  showBatchDialog.value = true
}

const batchDuplicate = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要复制选中的 ${selectedTickets.value.length} 个工单吗？`,
      '确认批量复制',
      { type: 'info' }
    )

    const promises = selectedTickets.value.map(ticket => {
      const duplicatedData = {
        title: `${ticket.title} (副本)`,
        content: ticket.content,
        priority: ticket.priority
      }
      return ticketAPI.createTicket(duplicatedData)
    })

    await Promise.all(promises)
    ElMessage.success(`成功复制 ${selectedTickets.value.length} 个工单`)
    await loadTickets(false)
    clearSelection()

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量复制失败: ' + error.message)
    }
  }
}



const batchDelete = async () => {
  const validTickets = selectedTickets.value.filter(ticket => canDelete(ticket.status))
  if (validTickets.length === 0) {
    ElMessage.warning('没有可以删除的工单')
    return
  }

  batchAction.value = 'delete'
  showBatchDialog.value = true
}

const exportTickets = async () => {
  try {
    showExportDialog.value = true
    exportProgress.value = { current: 0, total: 0, status: 'processing' }

    // 导出当前筛选条件下的所有工单
    const params = {
      ...filters,
      page: 1,
      pageSize: 10000, // 获取所有数据
      sortBy: currentSort.prop,
      sortOrder: currentSort.order
    }

    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startDate = filters.dateRange[0]
      params.endDate = filters.dateRange[1]
    }

    const response = await ticketAPI.getTickets(params)
    exportProgress.value.total = response.data.length

    const exportData = []
    for (let i = 0; i < response.data.length; i++) {
      const ticket = response.data[i]
      const detailResponse = await ticketAPI.getTicketDetail(ticket.id)
      exportData.push(detailResponse.data)
      exportProgress.value.current = i + 1
    }

    await exportToExcel(exportData, `工单列表_${new Date().toISOString().slice(0, 10)}`)

    exportProgress.value.status = 'completed'
    ElMessage.success('导出成功')

    setTimeout(() => {
      showExportDialog.value = false
      exportProgress.value = { current: 0, total: 0, status: 'idle' }
    }, 1500)

  } catch (error) {
    exportProgress.value.status = 'error'
    ElMessage.error('导出失败: ' + error.message)
  }
}

// 对话框事件处理
const handleTicketSaved = (ticket) => {
  showCreateDialog.value = false
  editingTicket.value = null
  dialogMode.value = 'create'

  ElMessage.success(
    dialogMode.value === 'edit' ? '工单更新成功' :
    dialogMode.value === 'duplicate' ? '工单复制成功' : '工单创建成功'
  )

  loadTickets(false)
}

const handleDialogCancelled = () => {
  showCreateDialog.value = false
  editingTicket.value = null
  dialogMode.value = 'create'
}

const handleEditFromDetail = (ticket) => {
  showDetailDialog.value = false
  editTicket(ticket)
}

const handleActionFromDetail = (action, ticket) => {
  showDetailDialog.value = false
  handleAction(action, ticket)
}

const handleSortApply = (sortConfig) => {
  currentSort.prop = sortConfig.prop
  currentSort.order = sortConfig.order
  pagination.page = 1
  loadTickets()
  showSortDialog.value = false
}

const handleColumnSettingsApply = (settings) => {
  // 处理新的列设置格式
  if (settings.columns) {
    const newSettings = {}
    settings.columns.forEach(col => {
      switch (col.key) {
        case 'progress':
          newSettings.showProgress = col.visible
          break
        case 'notes':
          newSettings.showNotes = col.visible
          break
        case 'processing_time':
          newSettings.showProcessingTime = col.visible
          break
        case 'created_at':
          newSettings.showCreatedAt = col.visible
          break
        case 'updated_at':
          newSettings.showUpdatedAt = col.visible
          break
      }
    })
    Object.assign(columnSettings, newSettings)
  } else {
    Object.assign(columnSettings, settings)
  }

  showColumnSettings.value = false

  // 保存到本地存储
  localStorage.setItem('ticket-list-column-settings', JSON.stringify(columnSettings))
}

const handleBatchConfirm = async (action, tickets, options = {}) => {
  showBatchDialog.value = false

  try {
    switch (action) {
      case 'start':
        await batchUpdateStatus(tickets.map(t => t.id), '排队中', options.notes)
        break
      case 'suspend':
        await batchUpdateStatus(tickets.map(t => t.id), '已挂起', options.notes)
        break
      case 'delete':
        await batchDeleteTickets(tickets.map(t => t.id))
        break
    }
  } catch (error) {
    ElMessage.error('批量操作失败: ' + error.message)
  }
}

const batchUpdateStatus = async (ticketIds, status, notes = '') => {
  try {
    await ticketAPI.batchUpdateStatus({ ticketIds, status, notes })
    ElMessage.success(`成功更新 ${ticketIds.length} 个工单状态`)
    await loadTickets(false)
    clearSelection()

    // 发送WebSocket消息
    wsStore.send({
      type: 'batch_ticket_update',
      data: { ticketIds, status, notes }
    })
  } catch (error) {
    throw new Error('批量状态更新失败: ' + error.message)
  }
}

const batchDeleteTickets = async (ticketIds) => {
  try {
    await ticketAPI.batchDelete({ ticketIds })
    ElMessage.success(`成功删除 ${ticketIds.length} 个工单`)
    await loadTickets(false)
    clearSelection()
  } catch (error) {
    throw new Error('批量删除失败: ' + error.message)
  }
}



// 工具函数
const isSelectable = (row) => {
  // 处理中的工单不能被选择进行批量操作
  return row.status !== '处理中'
}

const getPriorityClass = (priority) => {
  const classMap = {
    2: 'priority-high',
    1: 'priority-medium',
    0: 'priority-low'
  }
  return classMap[priority] || ''
}

const getPriorityIconClass = (priority) => {
  const classMap = {
    2: 'priority-high-icon',
    1: 'priority-medium-icon',
    0: 'priority-low-icon'
  }
  return classMap[priority] || ''
}

const getProgressPercentage = (ticket) => {
  if (ticket.status === '已完成') return 100
  if (ticket.status === '处理失败') return 0
  if (ticket.status === '处理中') return 60
  if (ticket.status === '排队中') return 20
  return 0
}

const getProgressStatus = (status) => {
  if (status === '已完成') return 'success'
  if (status === '处理失败') return 'exception'
  return ''
}

const getProgressText = (ticket) => {
  if (ticket.tasks && ticket.tasks.length > 0) {
    const completed = ticket.tasks.filter(t => t.status === '已完成').length
    return `${completed}/${ticket.tasks.length}`
  }
  return ticket.status
}

const canEdit = (status) => {
  return ['待开始', '待补充信息', '已挂起'].includes(status)
}

const canStart = (status) => {
  return ['待开始', '已挂起'].includes(status)
}

const canSuspend = (status) => {
  return ['待开始', '排队中', '处理中'].includes(status)
}

const canDelete = (status) => {
  return ['待开始', '已挂起', '已完成', '处理失败'].includes(status)
}

// WebSocket事件处理
const handleWebSocketMessage = (message) => {
  switch (message.type) {
    case 'ticket_updated':
      // 实时更新工单状态
      const updatedTicket = message.data
      const index = ticketsStore.tickets.findIndex(t => t.id === updatedTicket.id)
      if (index !== -1) {
        // 更新store中的数据
        ticketsStore.tickets[index] = { ...ticketsStore.tickets[index], ...updatedTicket }

        // 显示状态变更通知
        ElNotification({
          title: '工单状态更新',
          message: `工单 ${updatedTicket.ticket_number} 状态已更新为 ${updatedTicket.status}`,
          type: 'success',
          duration: 3000
        })
      }
      break
    case 'ticket_created':
      // 新工单创建通知
      ElNotification({
        title: '新工单',
        message: `工单 ${message.data.ticket_number} 已创建`,
        type: 'info',
        duration: 3000
      })
      // 如果在第一页，自动刷新列表
      if (pagination.page === 1) {
        loadTickets(false)
      }
      break
    case 'task_updated':
      // 任务状态更新
      ElNotification({
        title: '任务更新',
        message: `任务状态已更新`,
        type: 'info',
        duration: 2000
      })
      break
    case 'system_status_updated':
      // 系统状态更新
      loadStats()
      break
  }
}

// 监听器
watch(() => filters.status, () => {
  pagination.page = 1
  loadTickets()
})

watch(() => filters.priority, () => {
  pagination.page = 1
  loadTickets()
})

// 生命周期
onMounted(async () => {
  // 加载列设置
  const savedColumnSettings = localStorage.getItem('ticket-list-column-settings')
  if (savedColumnSettings) {
    try {
      Object.assign(columnSettings, JSON.parse(savedColumnSettings))
    } catch (error) {
      console.warn('Failed to parse saved column settings:', error)
    }
  }

  // 初始加载数据
  await loadTickets()

  // 移除定时刷新，改为WebSocket实时更新
  // const refreshInterval = setInterval(() => {
  //   if (!loading.value) {
  //     loadTickets(false)
  //   }
  // }, 30000) // 每30秒刷新一次

  // 监听WebSocket消息
  wsStore.on('message', handleWebSocketMessage)

  // 清理函数
  onUnmounted(() => {
    // clearInterval(refreshInterval) // 已移除定时刷新
    wsStore.off('message', handleWebSocketMessage)
  })
})
</script>

<style scoped>
.ticket-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stats-badges {
  display: flex;
  gap: 12px;
}

.badge-item {
  margin-right: 0;
}

.filter-section {
  flex-shrink: 0;
}

.filter-card {
  border: 1px solid #e4e7ed;
}

.compact-filter {
  margin: 0;
  padding: 12px 16px;
}

.compact-filter .el-form-item {
  margin-bottom: 0;
  margin-right: 12px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-title {
  font-weight: 600;
  color: #303133;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.advanced-filter {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.status-option {
  display: flex;
  align-items: center;
}

.table-section {
  flex: 1;
  overflow: hidden;
}

.table-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
}

.table-card :deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-card :deep(.el-table) {
  flex: 1;
}

.table-card :deep(.el-table__body-wrapper) {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #303133;
}

.ticket-number-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ticket-link {
  font-weight: 600;
}

.assigned-icon {
  color: #409eff;
  font-size: 16px;
}

.title-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-text {
  font-weight: 500;
  color: #303133;
}

.title-notes {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.notes-icon {
  font-size: 12px;
}

.notes-text {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.processing-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.worker-info {
  font-size: 11px;
  color: #909399;
}

.worker-id {
  font-family: monospace;
}

.priority-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.priority-icon {
  font-size: 14px;
}

.priority-high {
  color: #f56c6c;
  font-weight: 600;
}

.priority-medium {
  color: #e6a23c;
  font-weight: 500;
}

.priority-low {
  color: #67c23a;
  font-weight: 400;
}

.priority-high-icon {
  color: #f56c6c;
}

.priority-medium-icon {
  color: #e6a23c;
}

.priority-low-icon {
  color: #67c23a;
}

.progress-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.time-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-main {
  font-size: 13px;
  color: #303133;
}

.time-relative {
  font-size: 11px;
  color: #909399;
}

.processing-time-cell {
  display: flex;
  align-items: center;
  gap: 4px;
}

.time-calculating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #409eff;
}

.calculating-icon {
  animation: rotate 1s linear infinite;
}

.time-empty {
  color: #c0c4cc;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons-compact {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: stretch;
}

.action-row-primary,
.action-row-secondary {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-row-primary .el-button,
.action-row-secondary .el-button {
  flex: 1;
  min-width: 0;
  padding: 4px 8px;
  font-size: 12px;
}

.action-row-secondary .el-button {
  padding: 2px 6px;
}

.batch-actions-bar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.batch-card {
  min-width: 600px;
  border: 1px solid #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.batch-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.batch-icon {
  color: #409eff;
  font-size: 18px;
}

.batch-text {
  font-size: 14px;
  color: #303133;
}

.clear-selection {
  color: #909399;
  padding: 0;
}

/* 新设计样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-subtitle {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.stats-section {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stats-number.orange {
  color: #E6A23C;
}

.stats-number.blue {
  color: #409EFF;
}

.stats-number.green {
  color: #67C23A;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.action-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.result-count {
  font-size: 14px;
  color: #909399;
}

.work-list-section {
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.list-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.list-count {
  font-size: 14px;
  color: #909399;
}

.list-actions {
  display: flex;
  gap: 8px;
}

.work-table {
  border: none;
}

.work-table :deep(.el-table__header) {
  background: #f8f9fa;
}

.work-table :deep(.el-table__header th) {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #303133;
}

.work-table :deep(.el-table__row) {
  border-bottom: 1px solid #f0f0f0;
}

.work-table :deep(.el-table__row:hover) {
  background: #f8f9fa;
}

.ticket-link {
  font-weight: 600;
  text-decoration: none;
}

.title-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-text {
  font-weight: 500;
  color: #303133;
}

.title-notes {
  font-size: 12px;
  color: #909399;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  min-width: 30px;
}

.assignee-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-avatar {
  flex-shrink: 0;
}

.assignee-name {
  font-size: 12px;
  color: #303133;
}

.time-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-date {
  font-size: 13px;
  color: #303133;
}

.time-time {
  font-size: 11px;
  color: #909399;
}

.action-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-row {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.pagination-section {
  margin-top: 16px;
}

.pagination-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.pagination-summary {
  display: flex;
  align-items: center;
}

.info-text,
.summary-text {
  font-size: 14px;
  color: #606266;
}

.pagination-section {
  flex-shrink: 0;
}

.pagination-card {
  border: 1px solid #e4e7ed;
}

.pagination-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.info-text {
  white-space: nowrap;
}

.pagination-component {
  flex-shrink: 0;
}

/* 动画效果 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateX(-50%) translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateX(-50%) translateY(100%);
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .batch-card {
    min-width: 90vw;
  }

  .batch-content {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .ticket-list-container {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .stats-badges {
    flex-wrap: wrap;
  }
}
</style>