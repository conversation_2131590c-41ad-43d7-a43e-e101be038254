<template>
  <div class="ticket-detail-page">
    <el-page-header @back="goBack" title="返回工单列表">
      <template #content>
        <span class="page-title">工单详情</span>
      </template>
    </el-page-header>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="ticket" class="ticket-content">
      <el-card class="ticket-card">
        <template #header>
          <div class="card-header">
            <h2>{{ ticket.title }}</h2>
            <el-tag :type="getStatusType(ticket.status)" size="large">
              {{ ticket.status }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="工单号">
            {{ ticket.ticket_number }}
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(ticket.priority)">
              {{ getPriorityText(ticket.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(ticket.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(ticket.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="内容" :span="2">
            <div class="content-text">
              {{ ticket.content }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item v-if="ticket.notes" label="备注" :span="2">
            <div class="notes-section">
              <div v-if="parsedNotes.type === 'supplement_request'" class="supplement-request">
                <div class="request-header">
                  <el-icon class="request-icon"><InfoFilled /></el-icon>
                  <span class="request-title">需要补充以下信息才能继续处理：</span>
                </div>
                <ul class="supplement-list">
                  <li v-for="(item, index) in parsedNotes.items" :key="index" class="supplement-item">
                    <el-icon class="item-icon"><ArrowRight /></el-icon>
                    <span>{{ item }}</span>
                  </li>
                </ul>
                <div v-if="parsedNotes.note" class="additional-note">
                  <strong>备注：</strong>{{ parsedNotes.note }}
                </div>
              </div>
              <div v-else-if="parsedNotes.type === 'completion_summary'" class="completion-summary">
                <div class="summary-header">
                  <el-icon class="summary-icon"><SuccessFilled /></el-icon>
                  <span class="summary-title">任务执行完成</span>
                </div>
                <div class="summary-content">{{ parsedNotes.content }}</div>
              </div>
              <div v-else-if="parsedNotes.type === 'structured'" class="structured-notes">
                <div class="structured-header">
                  <el-icon class="structured-icon"><InfoFilled /></el-icon>
                  <span class="structured-title">结构化信息</span>
                </div>
                <div class="structured-content">
                  <pre>{{ JSON.stringify(parsedNotes.data, null, 2) }}</pre>
                </div>
              </div>
              <div v-else class="notes-text">
                {{ ticket.notes }}
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <div class="action-buttons">
          <el-button
            v-if="canEditTicket"
            type="primary"
            @click="editTicket"
          >
            编辑工单
          </el-button>
          <el-button
            v-if="!canEditTicket && ticket.status !== '已完成'"
            type="info"
            disabled
          >
            {{ getEditDisabledReason }}
          </el-button>
          <el-button @click="goBack">返回列表</el-button>
        </div>
      </el-card>

      <!-- 任务执行进度 -->
      <el-card v-if="processedTasks.length > 0" class="tasks-card">
        <template #header>
          <div class="card-header">
            <h3>任务执行进度</h3>
            <div class="progress-info">
              <span class="progress-text">{{ completedTasksCount }}/{{ processedTasks.length }} 已完成</span>
              <el-tag :type="progressTagType" size="large">{{ progressPercentage }}%</el-tag>
            </div>
          </div>
        </template>

        <!-- 整体进度条 -->
        <div class="overall-progress">
          <el-progress
            :percentage="progressPercentage"
            :status="progressStatus"
            :stroke-width="8"
            :show-text="false"
          />
        </div>

        <!-- 任务列表 -->
        <div class="tasks-container">
          <div v-for="(task, index) in processedTasks" :key="task.id" class="task-item">
            <div class="task-main">
              <div class="task-checkbox">
                <el-icon v-if="task.isCompleted" class="task-icon completed">
                  <Check />
                </el-icon>
                <el-icon v-else-if="task.isRunning" class="task-icon running">
                  <Loading />
                </el-icon>
                <div v-else class="task-icon pending"></div>
              </div>

              <div class="task-content">
                <div class="task-header">
                  <span class="task-title">{{ task.friendlyTitle }}</span>
                  <div class="task-meta">
                    <el-tag :type="getTaskStatusType(task.actualStatus)" size="small">
                      {{ task.actualStatus }}
                    </el-tag>
                    <span v-if="task.executionTime" class="execution-time">
                      耗时: {{ formatExecutionTime(task.executionTime) }}
                    </span>
                  </div>
                </div>

                <div v-if="task.friendlyDescription" class="task-description">
                  {{ task.friendlyDescription }}
                </div>

                <!-- 展开/收起技术详情 -->
                <div v-if="task.hasDetails" class="task-details">
                  <el-button
                    text
                    size="small"
                    @click="toggleTaskDetails(task.id)"
                    class="details-toggle"
                  >
                    <el-icon><CaretRight :class="{ 'rotated': task.showDetails }" /></el-icon>
                    {{ task.showDetails ? '收起' : '查看' }}技术详情
                  </el-button>

                  <el-collapse-transition>
                    <div v-show="task.showDetails" class="technical-details">
                      <div class="detail-section">
                        <h5>原始任务信息</h5>
                        <div class="detail-item">
                          <strong>任务ID:</strong> {{ task.originalTitle }}
                        </div>
                        <div class="detail-item">
                          <strong>创建时间:</strong> {{ formatDateTime(task.created_at) }}
                        </div>
                        <div v-if="task.originalDescription" class="detail-item">
                          <strong>原始描述:</strong> {{ task.originalDescription }}
                        </div>
                      </div>

                      <div v-if="task.action_data" class="detail-section">
                        <h5>执行配置</h5>
                        <pre class="action-data">{{ formatActionData(task.action_data) }}</pre>
                      </div>

                      <div v-if="task.executionResult" class="detail-section">
                        <h5>执行结果</h5>
                        <pre class="execution-result">{{ formatActionData(task.executionResult) }}</pre>
                      </div>
                    </div>
                  </el-collapse-transition>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="processedTasks.length === 0" class="empty-tasks">
          <el-empty description="暂无任务信息" />
        </div>
      </el-card>

      <!-- 结单总结 - 仅已完成工单显示 -->
      <el-card v-if="ticket.status === '已完成'" class="completion-summary-card">
        <template #header>
          <div class="card-header">
            <h3>
              <el-icon class="summary-header-icon"><SuccessFilled /></el-icon>
              结单总结
            </h3>
            <el-tag type="success" size="large">任务完成</el-tag>
          </div>
        </template>

        <div class="completion-content">
          <!-- 执行概要 -->
          <div class="summary-section">
            <h4 class="section-title">
              <el-icon><Document /></el-icon>
              执行概要
            </h4>
            <div class="summary-overview">
              <div class="overview-item">
                <span class="label">工单标题：</span>
                <span class="value">{{ ticket.title }}</span>
              </div>
              <div class="overview-item">
                <span class="label">完成时间：</span>
                <span class="value">{{ formatDateTime(ticket.completed_at) }}</span>
              </div>
              <div class="overview-item">
                <span class="label">处理时长：</span>
                <span class="value">{{ formatProcessingTime(ticket.processing_time) }}</span>
              </div>
              <div class="overview-item">
                <span class="label">任务总数：</span>
                <span class="value">{{ processedTasks.length }} 个</span>
              </div>
              <div class="overview-item">
                <span class="label">完成率：</span>
                <span class="value">{{ progressPercentage }}%</span>
              </div>
            </div>
          </div>

          <!-- 关键步骤 -->
          <div class="summary-section">
            <h4 class="section-title">
              <el-icon><List /></el-icon>
              关键执行步骤
            </h4>
            <div class="key-steps">
              <div v-for="(task, index) in processedTasks" :key="task.id" class="step-item">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-content">
                  <div class="step-title">{{ task.friendlyTitle }}</div>
                  <div class="step-description">{{ task.friendlyDescription }}</div>
                  <div v-if="task.executionTime" class="step-time">
                    耗时: {{ formatExecutionTime(task.executionTime) }}
                  </div>
                </div>
                <div class="step-status">
                  <el-icon class="step-check"><Check /></el-icon>
                </div>
              </div>
            </div>
          </div>

          <!-- 执行截图 -->
          <div v-if="completionScreenshots.length > 0" class="summary-section">
            <h4 class="section-title">
              <el-icon><Picture /></el-icon>
              执行截图
            </h4>
            <div class="screenshots-grid">
              <div v-for="(screenshot, index) in completionScreenshots" :key="index" class="screenshot-item">
                <el-image
                  :src="screenshot.url"
                  :alt="screenshot.description"
                  fit="cover"
                  class="screenshot-image"
                  :preview-src-list="completionScreenshots.map(s => s.url)"
                  :initial-index="index"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>图片加载失败</span>
                    </div>
                  </template>
                </el-image>
                <div class="screenshot-description">{{ screenshot.description }}</div>
              </div>
            </div>
          </div>

          <!-- 执行结果 -->
          <div class="summary-section">
            <h4 class="section-title">
              <el-icon><CircleCheck /></el-icon>
              执行结果
            </h4>
            <div class="execution-result">
              <div class="result-status success">
                <el-icon><SuccessFilled /></el-icon>
                <span>任务执行成功</span>
              </div>
              <div class="result-description">
                {{ getCompletionSummary() }}
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div v-else class="error-container">
      <el-result
        icon="warning"
        title="工单不存在"
        sub-title="请检查工单ID是否正确"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">返回列表</el-button>
        </template>
      </el-result>
    </div>

    <!-- 编辑工单对话框 -->
    <TicketFormDialog
      v-model="showEditDialog"
      :ticket="editingTicket"
      :mode="'edit'"
      @saved="handleTicketSaved"
      @cancelled="handleEditCancelled"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Check, Loading, CaretRight, InfoFilled, ArrowRight, SuccessFilled,
  Document, List, Picture, CircleCheck
} from '@element-plus/icons-vue'
import { ticketAPI } from '@/api/tickets'
import { formatDateTime } from '@/utils/date'
import TicketFormDialog from '@/components/TicketFormDialog.vue'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const ticket = ref(null)
const tasks = ref([])
const showEditDialog = ref(false)
const editingTicket = ref(null)

// 处理后的任务列表
const processedTasks = computed(() => {
  if (!tasks.value || tasks.value.length === 0) return []

  // 分离原始任务和执行结果任务
  const originalTasks = tasks.value.filter(task =>
    !task.title.includes('已完成任务') &&
    (task.title.startsWith('任务 task_') || task.title.includes('task_'))
  )

  const resultTasks = tasks.value.filter(task =>
    task.title.includes('已完成任务') ||
    task.description.includes('任务执行结果')
  )

  // 为每个原始任务匹配执行结果
  const processed = originalTasks.map(originalTask => {
    // 查找对应的执行结果
    const matchingResult = resultTasks.find(resultTask => {
      // 通过任务顺序或时间匹配
      return resultTask.task_order === originalTask.task_order ||
             Math.abs(new Date(resultTask.created_at) - new Date(originalTask.created_at)) < 10000
    })

    // 解析执行结果
    let executionResult = null
    let executionTime = null
    let isCompleted = false

    if (matchingResult && matchingResult.description.includes('任务执行结果')) {
      try {
        const resultMatch = matchingResult.description.match(/任务执行结果: (.+)/)
        if (resultMatch) {
          executionResult = JSON.parse(resultMatch[1])
          executionTime = executionResult.executionTime
          isCompleted = executionResult.status === 'completed'
        }
      } catch (error) {
        console.warn('解析执行结果失败:', error)
      }
    }

    return {
      ...originalTask,
      friendlyTitle: generateFriendlyTitle(originalTask),
      friendlyDescription: generateFriendlyDescription(originalTask),
      originalTitle: originalTask.title,
      originalDescription: originalTask.description,
      executionResult,
      executionTime,
      isCompleted,
      isRunning: !isCompleted && originalTask.status === '处理中',
      actualStatus: isCompleted ? '已完成' : (originalTask.status === '处理中' ? '进行中' : '待开始'),
      hasDetails: true,
      showDetails: originalTask.showDetails || false
    }
  })

  // 按task_order排序
  return processed.sort((a, b) => (a.task_order || 0) - (b.task_order || 0))
})

// 计算完成进度
const completedTasksCount = computed(() =>
  processedTasks.value.filter(task => task.isCompleted).length
)

const progressPercentage = computed(() => {
  if (processedTasks.value.length === 0) return 0
  return Math.round((completedTasksCount.value / processedTasks.value.length) * 100)
})

const progressStatus = computed(() => {
  if (progressPercentage.value === 100) return 'success'
  if (progressPercentage.value > 0) return 'active'
  return 'normal'
})

const progressTagType = computed(() => {
  if (progressPercentage.value === 100) return 'success'
  if (progressPercentage.value >= 50) return 'primary'
  if (progressPercentage.value > 0) return 'warning'
  return 'info'
})

// 解析备注内容
const parsedNotes = computed(() => {
  if (!ticket.value?.notes) return { type: 'plain', content: '' }

  const notes = ticket.value.notes

  try {
    // 首先尝试解析整个notes字段为JSON
    const jsonData = JSON.parse(notes)

    // 检查是否是补充信息请求
    if (jsonData.message && jsonData.message.includes('需要补充以下信息才能继续处理')) {
      return {
        type: 'supplement_request',
        items: jsonData.requiredInfo || jsonData.required_info || jsonData.items || [],
        note: jsonData.note || jsonData.additional_note || null,
        timestamp: jsonData.timestamp
      }
    }

    // 检查是否是任务完成总结
    if (jsonData.message && jsonData.message.includes('任务') && jsonData.message.includes('执行完成')) {
      return {
        type: 'completion_summary',
        content: jsonData.message || jsonData.content || notes
      }
    }

    // 其他JSON格式
    return {
      type: 'structured',
      data: jsonData
    }
  } catch (error) {
    // 如果不是JSON，按文本处理

    // 检查是否是补充信息请求
    if (notes.includes('需要补充以下信息才能继续处理')) {
      try {
        // 尝试从文本中提取JSON部分
        const jsonMatch = notes.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          const jsonData = JSON.parse(jsonMatch[0])
          return {
            type: 'supplement_request',
            items: jsonData.requiredInfo || jsonData.required_info || jsonData.items || [],
            note: jsonData.note || jsonData.additional_note
          }
        }

        // 如果不是JSON，尝试解析文本格式
        const lines = notes.split('\n').filter(line => line.trim())
        const items = lines.slice(1).map(line => line.replace(/^[-*•]\s*/, '').trim()).filter(Boolean)

        return {
          type: 'supplement_request',
          items: items.length > 0 ? items : ['请联系管理员获取详细信息'],
          note: null
        }
      } catch (parseError) {
        console.warn('解析补充信息失败:', parseError)
        return {
          type: 'supplement_request',
          items: ['请联系管理员获取详细信息'],
          note: null
        }
      }
    }

    // 检查是否是任务完成总结
    if (notes.includes('任务') && notes.includes('执行完成')) {
      return {
        type: 'completion_summary',
        content: notes
      }
    }

    // 默认为普通文本
    return {
      type: 'plain',
      content: notes
    }
  }
})

// 编辑权限控制
const canEditTicket = computed(() => {
  if (!ticket.value) return false

  // 可以编辑的状态：待开始、排队中、待补充信息、已挂起
  const editableStatuses = ['待开始', '排队中', '待补充信息', '已挂起']
  return editableStatuses.includes(ticket.value.status)
})

// 编辑禁用原因
const getEditDisabledReason = computed(() => {
  if (!ticket.value) return '加载中...'

  const status = ticket.value.status
  const reasonMap = {
    '处理中': '工单处理中，无法编辑',
    '已完成': '工单已完成',
    '处理失败': '工单处理失败，请联系管理员'
  }

  return reasonMap[status] || '当前状态不允许编辑'
})

// 完成截图
const completionScreenshots = computed(() => {
  if (!processedTasks.value) return []

  const screenshots = []
  processedTasks.value.forEach(task => {
    if (task.executionResult && task.executionResult.screenshots) {
      task.executionResult.screenshots.forEach((screenshot, index) => {
        screenshots.push({
          url: screenshot.url || screenshot,
          description: screenshot.description || `${task.friendlyTitle} - 截图${index + 1}`
        })
      })
    }
  })

  return screenshots
})

// 获取完成总结
const getCompletionSummary = () => {
  if (!ticket.value) return ''

  const taskCount = processedTasks.value.length
  const completedCount = completedTasksCount.value

  return `成功完成 ${completedCount}/${taskCount} 个任务。${ticket.value.content}的自动化处理已全部完成，所有操作均按预期执行。`
}

// 格式化处理时长
const formatProcessingTime = (time) => {
  if (!time) return '未知'

  const hours = Math.floor(time)
  const minutes = Math.round((time - hours) * 60)

  if (hours > 0) {
    return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`
  } else {
    return `${minutes}分钟`
  }
}

const getStatusType = (status) => {
  const statusMap = {
    '待开始': 'info',
    '排队中': 'warning',
    '处理中': 'primary',
    '已完成': 'success',
    '失败': 'danger',
    '已暂停': 'warning'
  }
  return statusMap[status] || 'info'
}

const getPriorityType = (priority) => {
  const priorityMap = {
    0: 'info',
    1: 'warning',
    2: 'danger'
  }
  return priorityMap[priority] || 'info'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    0: '低',
    1: '中',
    2: '高'
  }
  return priorityMap[priority] || '未知'
}

const loadTicket = async () => {
  try {
    loading.value = true
    const response = await ticketAPI.getTicketDetail(route.params.id)
    ticket.value = response.data

    // 从工单详情中获取任务列表
    tasks.value = response.data.tasks || []
  } catch (error) {
    ElMessage.error('加载工单详情失败: ' + error.message)
    console.error('Load ticket detail error:', error)
  } finally {
    loading.value = false
  }
}

const editTicket = () => {
  if (!ticket.value || !canEditTicket.value) return

  editingTicket.value = { ...ticket.value }
  showEditDialog.value = true
}

const goBack = () => {
  router.push('/tickets')
}

const getTaskStatusType = (status) => {
  const statusMap = {
    '待开始': 'info',
    '进行中': 'primary',
    '已完成': 'success',
    '失败': 'danger',
    '已暂停': 'warning'
  }
  return statusMap[status] || 'info'
}

const formatActionData = (actionData) => {
  try {
    const data = typeof actionData === 'string' ? JSON.parse(actionData) : actionData
    return JSON.stringify(data, null, 2)
  } catch (error) {
    return actionData
  }
}

// 生成用户友好的任务标题
const generateFriendlyTitle = (task) => {
  const title = task.title || task.description || ''

  // 根据任务内容生成友好标题
  if (title.includes('导航') || title.includes('登录')) {
    return '登录商户后台'
  } else if (title.includes('搜索') && title.includes('门店')) {
    return '搜索并进入目标门店'
  } else if (title.includes('门店管理') || title.includes('管理后台')) {
    return '进入门店管理后台'
  } else if (title.includes('商品管理') || title.includes('外卖')) {
    return '进入商品管理页面'
  } else if (title.includes('搜索') && title.includes('商品')) {
    return '搜索目标商品'
  } else if (title.includes('下架') || title.includes('unlist')) {
    return '执行商品下架操作'
  } else if (title.includes('task_1')) {
    return '登录商户后台'
  } else if (title.includes('task_2')) {
    return '搜索并进入目标门店'
  } else if (title.includes('task_3')) {
    return '进入门店管理后台'
  } else if (title.includes('task_4')) {
    return '进入商品管理页面'
  } else if (title.includes('task_5')) {
    return '搜索并下架目标商品'
  }

  // 默认处理
  return title.replace(/任务\s+task_\d+/, '自动化任务').replace(/已完成任务\s+\w+/, '已完成任务')
}

// 生成用户友好的任务描述
const generateFriendlyDescription = (task) => {
  const description = task.description || ''

  if (description.includes('导航到BD商户后台')) {
    return '自动打开商户后台登录页面，等待用户完成登录验证'
  } else if (description.includes('搜索并进入目标门店')) {
    return '在系统中搜索指定门店并进入门店管理界面'
  } else if (description.includes('进入门店管理后台')) {
    return '导航到门店的管理后台页面'
  } else if (description.includes('进入外卖商品管理页面')) {
    return '打开外卖商品管理功能模块'
  } else if (description.includes('搜索目标商品并下架')) {
    return '搜索指定商品并执行下架操作，完成后截图确认'
  }

  return description.length > 100 ? description.substring(0, 100) + '...' : description
}

// 切换任务详情显示
const toggleTaskDetails = (taskId) => {
  const taskIndex = processedTasks.value.findIndex(t => t.id === taskId)
  if (taskIndex !== -1) {
    // 直接修改原始tasks数组中的数据
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      if (!task.showDetails) {
        task.showDetails = true
      } else {
        task.showDetails = false
      }
      // 强制重新计算computed
      tasks.value = [...tasks.value]
    }
  }
}

// 格式化执行时间
const formatExecutionTime = (time) => {
  if (!time) return '未知'
  if (time < 1000) return `${time}ms`
  if (time < 60000) return `${(time / 1000).toFixed(1)}s`
  return `${(time / 60000).toFixed(1)}min`
}

// 编辑对话框处理
const handleTicketSaved = async (savedTicket) => {
  showEditDialog.value = false
  editingTicket.value = null

  // 重新加载工单详情
  await loadTicket()
  ElMessage.success('工单更新成功')
}

const handleEditCancelled = () => {
  showEditDialog.value = false
  editingTicket.value = null
}

onMounted(() => {
  loadTicket()
})
</script>

<style scoped>
.ticket-detail-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.loading-container {
  margin-top: 20px;
}

.ticket-content {
  margin-top: 20px;
}

.ticket-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.content-text,
.notes-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  color: #606266;
}

.action-buttons {
  margin-top: 20px;
  text-align: right;
}

.action-buttons .el-button {
  margin-left: 10px;
}

.error-container {
  margin-top: 50px;
}

/* 任务列表样式 */
.tasks-card {
  margin-top: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.tasks-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tasks-card .card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.overall-progress {
  margin-bottom: 24px;
}

.tasks-container {
  max-height: 600px;
  overflow-y: auto;
}

.task-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 16px;
  background: #ffffff;
  transition: all 0.3s ease;
  overflow: hidden;
}

.task-item:hover {
  border-color: #c6e2ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.task-main {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  gap: 16px;
}

.task-checkbox {
  flex-shrink: 0;
  margin-top: 2px;
}

.task-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.task-icon.completed {
  background: #67c23a;
  color: white;
}

.task-icon.running {
  background: #409eff;
  color: white;
  animation: spin 1s linear infinite;
}

.task-icon.pending {
  background: #f0f0f0;
  border: 2px solid #dcdfe6;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.task-content {
  flex: 1;
  min-width: 0;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 16px;
}

.task-title {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.execution-time {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.task-description {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 12px;
  font-size: 14px;
}

.task-details {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.details-toggle {
  padding: 4px 0;
  font-size: 13px;
}

.details-toggle .el-icon {
  transition: transform 0.3s ease;
}

.details-toggle .el-icon.rotated {
  transform: rotate(90deg);
}

.technical-details {
  margin-top: 12px;
  background: #fafbfc;
  border-radius: 6px;
  padding: 16px;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.detail-item {
  margin-bottom: 6px;
  font-size: 13px;
  line-height: 1.4;
}

.detail-item strong {
  color: #606266;
  margin-right: 8px;
}

.action-data,
.execution-result {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  color: #495057;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.empty-tasks {
  text-align: center;
  padding: 40px 20px;
}

/* 备注区域样式 */
.notes-section {
  line-height: 1.6;
}

.supplement-request {
  background: #fef7e0;
  border: 1px solid #f0d000;
  border-radius: 8px;
  padding: 16px;
}

.request-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
  color: #b25000;
}

.request-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #f0d000;
}

.request-title {
  font-size: 14px;
}

.supplement-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.supplement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 6px;
  border-left: 3px solid #f0d000;
}

.item-icon {
  margin-right: 8px;
  margin-top: 2px;
  font-size: 12px;
  color: #b25000;
  flex-shrink: 0;
}

.additional-note {
  margin-top: 12px;
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 6px;
  font-size: 13px;
  color: #666;
}

.completion-summary {
  background: #f0f9ff;
  border: 1px solid #67c23a;
  border-radius: 8px;
  padding: 16px;
}

.summary-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
  color: #529b2e;
}

.summary-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #67c23a;
}

.summary-title {
  font-size: 14px;
}

.summary-content {
  color: #606266;
  font-size: 14px;
}

.structured-notes {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
}

.structured-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
  color: #409eff;
}

.structured-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

.structured-title {
  font-size: 14px;
}

.structured-content {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
}

.structured-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 结单总结样式 */
.completion-summary-card {
  margin-top: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #67c23a;
}

.completion-summary-card .card-header h3 {
  margin: 0;
  color: #529b2e;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-header-icon {
  color: #67c23a;
  font-size: 20px;
}

.completion-content {
  padding: 0;
}

.summary-section {
  margin-bottom: 32px;
}

.summary-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.summary-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.overview-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #67c23a;
}

.overview-item .label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.overview-item .value {
  color: #303133;
  flex: 1;
}

.key-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #67c23a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.step-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 4px;
}

.step-time {
  font-size: 12px;
  color: #909399;
  background: #ffffff;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.step-status {
  flex-shrink: 0;
}

.step-check {
  color: #67c23a;
  font-size: 20px;
}

.screenshots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.screenshot-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
}

.screenshot-image {
  width: 100%;
  height: 150px;
  cursor: pointer;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: #909399;
  background: #f5f7fa;
}

.image-error .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.screenshot-description {
  padding: 8px 12px;
  font-size: 13px;
  color: #606266;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.execution-result {
  padding: 20px;
  background: #f0f9ff;
  border: 1px solid #67c23a;
  border-radius: 8px;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 16px;
}

.result-status.success {
  color: #529b2e;
}

.result-status .el-icon {
  font-size: 20px;
}

.result-description {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}
</style>