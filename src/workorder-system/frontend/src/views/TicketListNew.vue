<template>
  <div class="ticket-list-container">
    <!-- 页面头部 - 包含统计信息 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">工单管理</h2>
        <p class="page-subtitle">管理和跟踪所有自动化工单</p>
      </div>
      <div class="header-center">
        <!-- 统计信息移到标题栏 -->
        <div class="stats-inline">
          <div class="stats-item-inline">
            <span class="stats-number">{{ stats.total }}</span>
            <span class="stats-label">总计</span>
          </div>
          <div class="stats-item-inline">
            <span class="stats-number orange">{{ stats.pending }}</span>
            <span class="stats-label">待处理</span>
          </div>
          <div class="stats-item-inline">
            <span class="stats-number blue">{{ stats.processing }}</span>
            <span class="stats-label">处理中</span>
          </div>
          <div class="stats-item-inline">
            <span class="stats-number green">{{ stats.completed }}</span>
            <span class="stats-label">已完成</span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          新建工单
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 - 移到原统计信息位置 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="filters.search"
          placeholder="搜索工单号或标题内容..."
          clearable
          style="width: 300px"
          @keyup.enter="loadTickets"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-select
          v-model="filters.status"
          placeholder="全部状态"
          clearable
          style="width: 120px"
          @change="loadTickets"
        >
          <el-option label="待开始" value="待开始" />
          <el-option label="排队中" value="排队中" />
          <el-option label="处理中" value="处理中" />
          <el-option label="待补充信息" value="待补充信息" />
          <el-option label="已完成" value="已完成" />
          <el-option label="已挂起" value="已挂起" />
          <el-option label="处理失败" value="处理失败" />
          <el-option label="已挂起" value="已挂起" />
        </el-select>

        <el-select
          v-model="filters.priority"
          placeholder="全部优先级"
          clearable
          style="width: 120px"
          @change="loadTickets"
        >
          <el-option label="低" :value="0" />
          <el-option label="中" :value="1" />
          <el-option label="高" :value="2" />
          <el-option label="紧急" :value="3" />
        </el-select>

        <el-button :icon="Filter" @click="loadTickets">筛选</el-button>
      </div>

      <div class="filter-right">
        <el-button :icon="Refresh" @click="loadTickets" :loading="loading">刷新</el-button>
        <span class="result-count">显示 {{ tickets.length }} 条结果</span>
      </div>
    </div>

    <!-- 工单列表 -->
    <div class="work-list-section">
      <div class="list-header">
        <h3 class="list-title">工单列表</h3>
        <span class="list-count">共 {{ pagination.total }} 条记录</span>
      </div>

      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tickets"
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @row-click="handleRowClick"
        row-key="id"
        class="work-table"
      >
        <!-- 工单号列 -->
        <el-table-column
          prop="ticket_number"
          label="工单号"
          width="140"
          sortable="custom"
        >
          <template #default="{ row }">
            <el-link
              type="primary"
              @click.stop="viewTicketDetail(row)"
              class="ticket-link"
            >
              {{ row.ticket_number }}
            </el-link>
          </template>
        </el-table-column>

        <!-- 工单标题 -->
        <el-table-column
          prop="title"
          label="工单标题"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="title-cell">
              <div class="title-text">{{ row.title }}</div>
              <div v-if="row.notes" class="title-notes">
                <el-tooltip
                  :content="getFullNotes(row.notes)"
                  placement="top"
                  :disabled="!row.notes"
                >
                  <span>{{ formatNotes(row.notes) }}</span>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column
          prop="status"
          label="状态"
          width="100"
        >
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 优先级 -->
        <el-table-column
          prop="priority"
          label="优先级"
          width="80"
        >
          <template #default="{ row }">
            <el-tag
              :type="getPriorityType(row.priority)"
              size="small"
            >
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 进度 -->
        <el-table-column
          label="进度"
          width="100"
        >
          <template #default="{ row }">
            <div class="progress-cell">
              <el-progress
                :percentage="getProgress(row.status)"
                :stroke-width="6"
                :show-text="false"
                :color="getProgressColor(row.status)"
              />
              <span class="progress-text">{{ getProgress(row.status) }}%</span>
            </div>
          </template>
        </el-table-column>

        <!-- 处理时长 -->
        <el-table-column
          prop="processing_time"
          label="处理时长"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            <div class="processing-time-cell">
              <span v-if="row.processing_time" class="time-value">
                {{ formatProcessingTime(row.processing_time) }}
              </span>
              <span v-else-if="row.status === '处理中'" class="time-running">
                {{ getRunningTime(row.created_at) }}
              </span>
              <span v-else class="time-empty">-</span>
            </div>
          </template>
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column
          prop="created_at"
          label="创建时间"
          width="140"
          sortable="custom"
        >
          <template #default="{ row }">
            <div class="time-cell">
              <div class="time-date">{{ formatDate(row.created_at) }}</div>
              <div class="time-time">{{ formatTime(row.created_at) }}</div>
            </div>
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column
          label="操作"
          width="120"
          fixed="right"
        >
          <template #default="{ row }">
            <div class="action-cell">
              <div class="action-row">
                <el-button :icon="View" circle size="small" @click.stop="viewTicketDetail(row)" />
                <el-button :icon="Edit" circle size="small" @click.stop="editTicket(row)" />
                <el-button :icon="CaretRight" circle size="small" @click.stop="startTicket(row)" />
              </div>
              <div class="action-row">
                <el-dropdown @command="(command) => handleCommand(command, row)" trigger="click">
                  <el-button :icon="MoreFilled" circle size="small" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="copy" :icon="DocumentCopy">复制工单</el-dropdown-item>
                      <el-dropdown-item command="assign" :icon="User">分配任务</el-dropdown-item>
                      <el-dropdown-item
                        command="suspend"
                        :icon="VideoPlay"
                        :disabled="!canSuspend(row.status)"
                        divided
                      >
                        {{ getSuspendText(row.status) }}
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="delete"
                        :icon="Delete"
                        :disabled="!canDelete(row.status)"
                      >
                        删除工单
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 - 紧凑设计 -->
    <div class="pagination-section">
      <div class="pagination-content">
        <div class="pagination-info">
          <span class="info-text">每页显示</span>
          <el-select
            v-model="pagination.pageSize"
            size="small"
            style="width: 60px"
            @change="handlePageSizeChange"
          >
            <el-option label="20" :value="20" />
            <el-option label="50" :value="50" />
            <el-option label="100" :value="100" />
          </el-select>
          <span class="info-text">条</span>
        </div>

        <div class="pagination-nav">
          <el-button-group>
            <el-button size="small" @click="handlePageChange(pagination.page - 1)" :disabled="pagination.page <= 1">上一页</el-button>
            <el-button size="small" @click="handlePageChange(pagination.page + 1)" :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)">下一页</el-button>
          </el-button-group>
        </div>

        <div class="pagination-summary">
          <span class="summary-text">
            第 {{ (pagination.page - 1) * pagination.pageSize + 1 }}-{{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，共 {{ pagination.total }} 条
          </span>
        </div>
      </div>
    </div>

    <!-- 创建/编辑工单对话框 -->
    <TicketFormDialog
      v-model="showCreateDialog"
      :ticket="editingTicket"
      :mode="dialogMode"
      @saved="handleTicketSaved"
      @cancelled="handleDialogCancelled"
    />


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Refresh, Filter, View, MoreFilled, Edit, CaretRight,
  User, DocumentCopy, Delete, VideoPlay
} from '@element-plus/icons-vue'

// 导入组件
import TicketFormDialog from '@/components/TicketFormDialog.vue'
import { useWebSocketStore } from '@/store/websocket'

// 导入API和工具函数
import { ticketAPI } from '@/api/tickets'
import { useTicketsStore } from '@/store/tickets'

const router = useRouter()
const wsStore = useWebSocketStore()
const ticketsStore = useTicketsStore()

// 响应式数据
const tableRef = ref()
const loading = ref(false)
const showCreateDialog = ref(false)
const editingTicket = ref(null)
const dialogMode = ref('create')

// 筛选条件
const filters = reactive({
  search: '',
  status: '',
  priority: ''
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 工单数据
const tickets = ref([])

// 统计数据
const stats = computed(() => ({
  total: tickets.value.length,
  pending: tickets.value.filter(t => ['待开始', '排队中', '待补充信息', '已挂起'].includes(t.status)).length,
  processing: tickets.value.filter(t => t.status === '处理中').length,
  completed: tickets.value.filter(t => t.status === '已完成').length
}))

// 辅助方法
const getStatusType = (status) => {
  const statusMap = {
    '待开始': 'info',
    '等待中': 'warning',
    '排队中': 'warning',
    '处理中': 'primary',
    '待补充信息': 'warning',
    '已完成': 'success',
    '处理失败': 'danger',
    '已挂起': 'info'
  }
  return statusMap[status] || 'info'
}

const getPriorityType = (priority) => {
  const priorityMap = {
    0: 'info',    // 低
    1: 'primary', // 中
    2: 'warning', // 高
    3: 'danger'   // 紧急
  }
  return priorityMap[priority] || 'primary'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    0: '低',
    1: '中',
    2: '高',
    3: '紧急'
  }
  return priorityMap[priority] || '中'
}

const getProgress = (status) => {
  const progressMap = {
    '待开始': 0,
    '排队中': 25,
    '处理中': 50,
    '待补充信息': 75,
    '已完成': 100,
    '处理失败': 0,
    '已挂起': 50
  }
  return progressMap[status] || 0
}

const getProgressColor = (status) => {
  const colorMap = {
    '待开始': '#909399',
    '排队中': '#E6A23C',
    '处理中': '#409EFF',
    '待补充信息': '#E6A23C',
    '已完成': '#67C23A',
    '处理失败': '#F56C6C',
    '已挂起': '#909399'
  }
  return colorMap[status] || '#409EFF'
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

// 格式化处理时长
const formatProcessingTime = (hours) => {
  if (!hours || hours <= 0) return '0分钟'

  if (hours < 1) {
    const minutes = Math.round(hours * 60)
    return `${minutes}分钟`
  }

  if (hours < 24) {
    const h = Math.floor(hours)
    const m = Math.round((hours - h) * 60)
    return m > 0 ? `${h}小时${m}分钟` : `${h}小时`
  }

  const days = Math.floor(hours / 24)
  const remainingHours = Math.floor(hours % 24)

  let result = `${days}天`
  if (remainingHours > 0) {
    result += `${remainingHours}小时`
  }

  return result
}

// 获取运行时间（用于处理中的工单）
const getRunningTime = (createdAt) => {
  if (!createdAt) return '-'

  const now = new Date()
  const created = new Date(createdAt)
  const diffMs = now - created
  const diffHours = diffMs / (1000 * 60 * 60)

  return formatProcessingTime(diffHours)
}

// 格式化备注信息
const formatNotes = (notes) => {
  if (!notes) return ''

  // 如果是字符串，直接返回
  if (typeof notes === 'string') {
    // 检查是否是JSON字符串
    try {
      const parsed = JSON.parse(notes)
      if (typeof parsed === 'object' && parsed !== null) {
        return formatNotesObject(parsed)
      }
    } catch (e) {
      // 不是JSON，直接返回字符串
      return notes
    }
    return notes
  }

  // 如果是对象，格式化显示
  if (typeof notes === 'object' && notes !== null) {
    return formatNotesObject(notes)
  }

  // 如果是其他类型，转换为字符串
  return String(notes || '')
}

// 格式化备注对象
const formatNotesObject = (notesObj) => {
  // 确保输入是对象
  if (!notesObj || typeof notesObj !== 'object') {
    return '处理备注信息'
  }

  // 如果有message字段，优先显示
  if (notesObj.message && typeof notesObj.message === 'string') {
    return notesObj.message
  }

  // 如果有summary字段，显示摘要
  if (notesObj.summary && typeof notesObj.summary === 'string') {
    return notesObj.summary
  }

  // 如果有requiredInfo字段，显示需要补充的信息
  if (notesObj.requiredInfo && Array.isArray(notesObj.requiredInfo)) {
    return `需要补充信息：${notesObj.requiredInfo.join('；')}`
  }

  // 如果有content字段，显示内容
  if (notesObj.content && typeof notesObj.content === 'string') {
    return notesObj.content
  }

  // 如果有description字段，显示描述
  if (notesObj.description && typeof notesObj.description === 'string') {
    return notesObj.description
  }

  // 如果有text字段，显示文本
  if (notesObj.text && typeof notesObj.text === 'string') {
    return notesObj.text
  }

  // 遍历所有属性，寻找第一个有意义的字符串值
  for (const [key, value] of Object.entries(notesObj)) {
    if (typeof value === 'string' && value.trim()) {
      return value
    }
  }

  // 如果对象有属性但都不是字符串，尝试显示对象的键
  const keys = Object.keys(notesObj)
  if (keys.length > 0) {
    return `包含信息：${keys.join('、')}`
  }

  // 最后返回简化的对象描述
  return '需要补充相关信息'
}

// 获取完整备注信息用于tooltip显示
const getFullNotes = (notes) => {
  if (!notes) return ''

  // 如果是字符串，直接返回
  if (typeof notes === 'string') {
    // 检查是否是JSON字符串
    try {
      const parsed = JSON.parse(notes)
      if (typeof parsed === 'object' && parsed !== null) {
        return getFullNotesFromObject(parsed)
      }
    } catch (e) {
      // 不是JSON，直接返回字符串
      return notes
    }
    return notes
  }

  // 如果是对象，格式化显示
  if (typeof notes === 'object' && notes !== null) {
    return getFullNotesFromObject(notes)
  }

  return String(notes)
}

// 从对象中获取完整备注信息
const getFullNotesFromObject = (notesObj) => {
  const parts = []

  // 收集所有有用的信息
  if (notesObj.message) {
    parts.push(`消息：${notesObj.message}`)
  }

  if (notesObj.summary) {
    parts.push(`摘要：${notesObj.summary}`)
  }

  if (notesObj.requiredInfo && Array.isArray(notesObj.requiredInfo)) {
    parts.push(`需要补充信息：${notesObj.requiredInfo.join('；')}`)
  }

  if (notesObj.content) {
    parts.push(`内容：${notesObj.content}`)
  }

  if (notesObj.description) {
    parts.push(`描述：${notesObj.description}`)
  }

  if (notesObj.text) {
    parts.push(`文本：${notesObj.text}`)
  }

  // 如果没有找到任何有用信息，返回JSON字符串
  if (parts.length === 0) {
    return JSON.stringify(notesObj, null, 2)
  }

  return parts.join('\n')
}

// 事件处理方法
const loadTickets = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      search: filters.search,
      status: filters.status,
      priority: filters.priority
    }

    console.log('发送API请求参数:', params)
    const response = await ticketAPI.getTickets(params)
    console.log('API响应数据:', response)

    if (response && response.success) {
      tickets.value = response.data || []
      pagination.total = response.pagination?.total || 0
    } else {
      console.error('API响应格式错误:', response)
      tickets.value = []
      pagination.total = 0
    }

    console.log('最终工单数据:', tickets.value)
    console.log('分页信息:', pagination)
  } catch (error) {
    console.error('加载工单失败:', error)
    ElMessage.error('加载工单数据失败: ' + error.message)
    tickets.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  console.log('Selection changed:', selection)
}

const handleSortChange = ({ prop, order }) => {
  console.log('Sort changed:', prop, order)
}

const handleRowClick = (row) => {
  console.log('Row clicked:', row)
}

const handlePageChange = (page) => {
  pagination.page = page
  loadTickets()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadTickets()
}

const viewTicketDetail = (ticket) => {
  // 跳转到独立的工单详情页面
  router.push(`/tickets/${ticket.id}`)
}

const editTicket = (ticket) => {
  editingTicket.value = { ...ticket }
  dialogMode.value = 'edit'
  showCreateDialog.value = true
}

const startTicket = (ticket) => {
  ElMessage.success(`开始处理工单: ${ticket.ticket_number}`)
}

// 检查是否可以删除工单
const canDelete = (status) => {
  // 只有待开始、排队中、已挂起、处理失败的工单可以删除
  return ['待开始', '排队中', '已挂起', '处理失败'].includes(status)
}

// 检查是否可以挂起/恢复工单
const canSuspend = (status) => {
  // 待开始、排队中、处理中的工单可以挂起，已挂起的工单可以恢复
  return ['待开始', '排队中', '处理中', '已挂起'].includes(status)
}

// 获取挂起操作的文本
const getSuspendText = (status) => {
  return status === '已挂起' ? '恢复工单' : '挂起工单'
}

// 处理下拉菜单命令
const handleCommand = async (command, ticket) => {
  switch (command) {
    case 'delete':
      await deleteTicket(ticket)
      break
    case 'copy':
      await copyTicket(ticket)
      break
    case 'assign':
      ElMessage.info(`分配任务: ${ticket.ticket_number}`)
      break
    case 'suspend':
      await suspendTicket(ticket)
      break
    default:
      ElMessage.info(`执行操作: ${command}`)
  }
}

// 删除工单
const deleteTicket = async (ticket) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工单 "${ticket.ticket_number}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const response = await fetch(`http://localhost:3001/api/tickets/${ticket.id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`删除失败: ${response.status}`)
    }

    ElMessage.success(`工单 ${ticket.ticket_number} 删除成功`)
    await loadTickets() // 重新加载工单列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除工单失败:', error)
      ElMessage.error('删除工单失败，请重试')
    }
  }
}

// 复制工单
const copyTicket = async (ticket) => {
  try {
    // 创建新工单数据，复制原工单内容但生成新的工单号
    const newTicketData = {
      title: `${ticket.title} (副本)`,
      description: ticket.description,
      priority: ticket.priority,
      category: ticket.category,
      status: '待开始'
    }

    const response = await fetch('http://localhost:3001/api/tickets', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newTicketData)
    })

    if (!response.ok) {
      throw new Error(`复制失败: ${response.status}`)
    }

    const newTicket = await response.json()
    ElMessage.success(`工单复制成功，新工单号: ${newTicket.ticket_number}`)
    await loadTickets() // 重新加载工单列表
  } catch (error) {
    console.error('复制工单失败:', error)
    ElMessage.error('复制工单失败，请重试')
  }
}

// 挂起/恢复工单
const suspendTicket = async (ticket) => {
  try {
    const isSuspended = ticket.status === '已挂起'
    const newStatus = isSuspended ? '待开始' : '已挂起'
    const actionText = isSuspended ? '恢复' : '挂起'

    await ElMessageBox.confirm(
      `确定要${actionText}工单 "${ticket.ticket_number}" 吗？`,
      `${actionText}确认`,
      {
        confirmButtonText: `确定${actionText}`,
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`http://localhost:3001/api/tickets/${ticket.id}/status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: newStatus,
        notes: `工单${actionText}操作`
      })
    })

    if (!response.ok) {
      throw new Error(`${actionText}失败: ${response.status}`)
    }

    ElMessage.success(`工单 ${ticket.ticket_number} ${actionText}成功`)
    await loadTickets() // 重新加载工单列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('挂起/恢复工单失败:', error)
      ElMessage.error('操作失败，请重试')
    }
  }
}

const handleTicketSaved = (ticket) => {
  showCreateDialog.value = false
  editingTicket.value = null
  dialogMode.value = 'create'
  ElMessage.success('工单保存成功')
  loadTickets()
}

// WebSocket事件处理
const handleWebSocketMessage = (message) => {
  switch (message.type) {
    case 'ticket_created':
    case 'ticket_updated':
    case 'ticket_deleted':
      // 工单有变化时自动刷新列表
      loadTickets()
      break
    case 'ticket_status_changed':
      // 状态变化时也刷新
      loadTickets()
      break
  }
}

const handleDialogCancelled = () => {
  showCreateDialog.value = false
  editingTicket.value = null
  dialogMode.value = 'create'
}



// 生命周期
onMounted(() => {
  loadTickets()

  // 监听WebSocket消息实现自动刷新
  wsStore.on('message', handleWebSocketMessage)
})

onUnmounted(() => {
  // 清理WebSocket监听器
  wsStore.off('message', handleWebSocketMessage)
})
</script>

<style scoped>
.ticket-list-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
  overflow-y: auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-subtitle {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

/* 内联统计信息 */
.stats-inline {
  display: flex;
  gap: 32px;
  align-items: center;
}

.stats-item-inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-item-inline .stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.stats-item-inline .stats-number.orange {
  color: #E6A23C;
}

.stats-item-inline .stats-number.blue {
  color: #409EFF;
}

.stats-item-inline .stats-number.green {
  color: #67C23A;
}

.stats-item-inline .stats-label {
  font-size: 12px;
  color: #909399;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.filter-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.result-count {
  font-size: 14px;
  color: #909399;
}

/* 工单列表 */
.work-list-section {
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
  flex-shrink: 0;
}

.list-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.list-count {
  font-size: 14px;
  color: #909399;
}

/* 表格样式 */
.work-table {
  border: none;
  flex: 1;
}

.work-table :deep(.el-table__header) {
  background: #f8f9fa;
}

.work-table :deep(.el-table__header th) {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #303133;
}

.work-table :deep(.el-table__row) {
  border-bottom: 1px solid #f0f0f0;
}

.work-table :deep(.el-table__row:hover) {
  background: #f8f9fa;
}

.ticket-link {
  font-weight: 600;
  text-decoration: none;
}

.title-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-text {
  font-weight: 500;
  color: #303133;
}

.title-notes {
  font-size: 12px;
  color: #909399;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  min-width: 30px;
}

.processing-time-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
}

.time-value {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

.time-running {
  font-size: 13px;
  color: #409EFF;
  font-weight: 500;
}

.time-empty {
  font-size: 13px;
  color: #C0C4CC;
}

.assignee-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-avatar {
  flex-shrink: 0;
}

.assignee-name {
  font-size: 12px;
  color: #303133;
}

.time-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-date {
  font-size: 13px;
  color: #303133;
}

.time-time {
  font-size: 11px;
  color: #909399;
}

.action-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-row {
  display: flex;
  gap: 4px;
  justify-content: center;
}

/* 分页 */
.pagination-section {
  margin-top: 16px;
}

.pagination-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.pagination-summary {
  display: flex;
  align-items: center;
}

.info-text,
.summary-text {
  font-size: 14px;
  color: #606266;
}
</style>
