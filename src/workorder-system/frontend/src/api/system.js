import request from './request'

export const systemAPI = {
  // 获取系统状态
  getStatus() {
    return request({
      url: '/system/status',
      method: 'get'
    })
  },

  // 获取系统健康检查
  getHealth() {
    return request({
      url: '/health',
      method: 'get'
    })
  },

  // 获取系统信息
  getInfo() {
    return request({
      url: '/system/info',
      method: 'get'
    })
  },

  // 获取系统统计
  getStats() {
    return request({
      url: '/system/stats',
      method: 'get'
    })
  },

  // 获取系统日志
  getLogs(params = {}) {
    return request({
      url: '/system/logs',
      method: 'get',
      params
    })
  },

  // 清理系统缓存
  clearCache() {
    return request({
      url: '/system/cache/clear',
      method: 'post'
    })
  },

  // 重启系统服务
  restartService(serviceName) {
    return request({
      url: `/system/services/${serviceName}/restart`,
      method: 'post'
    })
  },

  // 获取配置信息
  getConfig() {
    return request({
      url: '/system/config',
      method: 'get'
    })
  },

  // 更新配置
  updateConfig(config) {
    return request({
      url: '/system/config',
      method: 'put',
      data: config
    })
  },

  // 备份数据库
  backupDatabase() {
    return request({
      url: '/system/database/backup',
      method: 'post'
    })
  },

  // 恢复数据库
  restoreDatabase(backupFile) {
    return request({
      url: '/system/database/restore',
      method: 'post',
      data: { backupFile }
    })
  },

  // 启动AI助手
  startAIAssistant() {
    return request({
      url: '/system/ai-assistant/start',
      method: 'post'
    })
  },

  // 停止AI助手
  stopAIAssistant() {
    return request({
      url: '/system/ai-assistant/stop',
      method: 'post'
    })
  }
}
