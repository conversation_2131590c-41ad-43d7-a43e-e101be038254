import request from './request'

export const ticketAPI = {
  /**
   * 获取工单列表
   */
  getTickets(params = {}) {
    return request({
      url: '/tickets',
      method: 'get',
      params
    })
  },

  /**
   * 获取工单详情
   */
  getTicketDetail(id) {
    return request({
      url: `/tickets/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建工单
   */
  createTicket(data) {
    return request({
      url: '/tickets',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 更新工单
   */
  updateTicket(id, data) {
    return request({
      url: `/tickets/${id}`,
      method: 'put',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 更新工单状态
   */
  updateStatus(id, data) {
    return request({
      url: `/tickets/${id}/status`,
      method: 'patch',
      data
    })
  },

  /**
   * 批量更新工单状态
   */
  batchUpdateStatus(data) {
    return request({
      url: '/tickets/batch/status',
      method: 'patch',
      data
    })
  },

  /**
   * 删除工单
   */
  deleteTicket(id) {
    return request({
      url: `/tickets/${id}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除工单
   */
  batchDelete(data) {
    return request({
      url: '/tickets/batch',
      method: 'delete',
      data
    })
  },

  /**
   * 搜索工单
   */
  searchTickets(data) {
    return request({
      url: '/tickets/search',
      method: 'post',
      data
    })
  },

  /**
   * 获取工单统计信息
   */
  getStatistics() {
    return request({
      url: '/tickets/stats/overview',
      method: 'get'
    })
  },

  /**
   * 获取工单任务列表
   */
  getTicketTasks(id) {
    return request({
      url: `/tickets/${id}/tasks`,
      method: 'get'
    })
  },

  /**
   * 获取工单进度
   */
  getTicketProgress(id) {
    return request({
      url: `/tickets/${id}/progress`,
      method: 'get'
    })
  }
}