<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 主体内容区域 -->
      <el-container class="main-container">
        <!-- 左侧工单系统 -->
        <el-main class="workorder-section">
          <router-view />
        </el-main>

        <!-- 右侧AI助手 -->
        <el-aside class="ai-assistant-section" width="400px">
          <AIAssistantPanel
            :running="aiAssistantRunning"
            :stats="systemStats"
            @toggle="toggleAIAssistant"
          />
        </el-aside>
      </el-container>
    </el-container>

    <!-- 系统设置对话框 -->
    <SystemSettingsDialog
      v-model="showSettings"
      @updated="handleSettingsUpdated"
    />

    <!-- 关于对话框 -->
    <AboutDialog v-model="showAbout" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CaretRight, VideoPause, Setting, User, ArrowDown } from '@element-plus/icons-vue'
import AIAssistantPanel from '@/components/AIAssistantPanel.vue'
import SystemSettingsDialog from '@/components/SystemSettingsDialog.vue'
import AboutDialog from '@/components/AboutDialog.vue'
import { useSystemStore } from '@/store/system'
import { useWebSocketStore } from '@/store/websocket'

const systemStore = useSystemStore()
const wsStore = useWebSocketStore()

const aiAssistantRunning = ref(false)
const systemStats = ref({
  processing_count: 0,
  queue_length: 0,
  completed_today: 0
})
const showSettings = ref(false)
const showAbout = ref(false)

// 切换AI助手状态
const toggleAIAssistant = async () => {
  try {
    if (aiAssistantRunning.value) {
      await systemStore.stopAIAssistant()
      ElMessage.success('AI助手已暂停')
    } else {
      await systemStore.startAIAssistant()
      ElMessage.success('AI助手已启动')
    }

    // 刷新系统状态和统计信息
    await systemStore.loadSystemStatus()
    await loadSystemStats()

    // 更新本地状态
    aiAssistantRunning.value = !aiAssistantRunning.value
  } catch (error) {
    ElMessage.error('操作失败: ' + error.message)
  }
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'settings':
      showSettings.value = true
      break
    case 'about':
      showAbout.value = true
      break
  }
}

// 处理设置更新
const handleSettingsUpdated = () => {
  ElMessage.success('设置已更新')
  loadSystemStats()
}

// 加载系统统计信息
const loadSystemStats = async () => {
  try {
    const stats = await systemStore.getSystemStatus()
    systemStats.value = {
      processing_count: stats.processing?.active_tickets || 0,
      queue_length: stats.tickets?.by_status?.find(s => s.status === '排队中')?.count || 0,
      completed_today: stats.tickets?.by_status?.find(s => s.status === '已完成')?.count || 0
    }
  } catch (error) {
    console.error('加载系统统计失败:', error)
  }
}

// 初始化
onMounted(async () => {
  // 初始化系统store
  systemStore.initialize()

  // 连接WebSocket
  wsStore.connect()

  // 加载初始数据
  await loadSystemStats()

  // 定期更新统计信息
  const statsInterval = setInterval(loadSystemStats, 30000)

  onUnmounted(() => {
    clearInterval(statsInterval)
    wsStore.disconnect()
  })
})
</script>

<style scoped>
.app-container {
  height: 100vh;
  background: #f5f7fa;
}

.main-container {
  height: 100vh;
}

.workorder-section {
  background: #f5f7fa;
  margin: 0;
  padding: 0;
  overflow-y: auto;
}

.ai-assistant-section {
  background: #fff;
  margin: 16px 16px 16px 8px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
</style>