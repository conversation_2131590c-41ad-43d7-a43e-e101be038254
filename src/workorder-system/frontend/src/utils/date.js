import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 格式化日期时间
 * @param {string|Date} date 日期
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的日期字符串
 */
export const formatDateTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化时间
 * @param {string|Date} date 日期
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的时间字符串
 */
export const formatTime = (date, format = 'HH:mm:ss') => {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 获取相对时间
 * @param {string|Date} date 日期
 * @returns {string} 相对时间字符串
 */
export const getRelativeTime = (date) => {
  if (!date) return ''
  return dayjs(date).fromNow()
}

/**
 * 格式化处理时长
 * @param {number} hours 小时数
 * @returns {string} 格式化后的时长字符串
 */
export const formatProcessingTime = (hours) => {
  if (!hours || hours <= 0) return '0分钟'

  if (hours < 1) {
    const minutes = Math.round(hours * 60)
    return `${minutes}分钟`
  }

  if (hours < 24) {
    const h = Math.floor(hours)
    const m = Math.round((hours - h) * 60)
    return m > 0 ? `${h}小时${m}分钟` : `${h}小时`
  }

  const days = Math.floor(hours / 24)
  const remainingHours = Math.floor(hours % 24)
  const minutes = Math.round(((hours % 24) - remainingHours) * 60)

  let result = `${days}天`
  if (remainingHours > 0) {
    result += `${remainingHours}小时`
  }
  if (minutes > 0 && days === 0) {
    result += `${minutes}分钟`
  }

  return result
}

/**
 * 计算两个日期之间的时长（小时）
 * @param {string|Date} startDate 开始日期
 * @param {string|Date} endDate 结束日期
 * @returns {number} 时长（小时）
 */
export const calculateDuration = (startDate, endDate = new Date()) => {
  if (!startDate) return 0

  const start = dayjs(startDate)
  const end = dayjs(endDate)

  return end.diff(start, 'hour', true)
}

/**
 * 判断日期是否为今天
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为今天
 */
export const isToday = (date) => {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断日期是否为昨天
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为昨天
 */
export const isYesterday = (date) => {
  if (!date) return false
  return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 判断日期是否为本周
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为本周
 */
export const isThisWeek = (date) => {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 判断日期是否为本月
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为本月
 */
export const isThisMonth = (date) => {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 获取日期范围的描述文本
 * @param {Array} dateRange 日期范围 [startDate, endDate]
 * @returns {string} 描述文本
 */
export const getDateRangeText = (dateRange) => {
  if (!dateRange || dateRange.length !== 2) return ''

  const [start, end] = dateRange
  const startDay = dayjs(start)
  const endDay = dayjs(end)

  if (startDay.isSame(endDay, 'day')) {
    return `${formatDate(start)}`
  }

  if (startDay.isSame(endDay, 'month')) {
    return `${startDay.format('MM-DD')} 至 ${endDay.format('MM-DD')}`
  }

  if (startDay.isSame(endDay, 'year')) {
    return `${startDay.format('MM-DD')} 至 ${endDay.format('MM-DD')}`
  }

  return `${formatDate(start)} 至 ${formatDate(end)}`
}

/**
 * 获取预设的日期范围
 * @returns {Object} 预设日期范围对象
 */
export const getPresetDateRanges = () => {
  const now = dayjs()

  return {
    '今天': [now.startOf('day').toDate(), now.endOf('day').toDate()],
    '昨天': [
      now.subtract(1, 'day').startOf('day').toDate(),
      now.subtract(1, 'day').endOf('day').toDate()
    ],
    '最近7天': [now.subtract(6, 'day').startOf('day').toDate(), now.endOf('day').toDate()],
    '最近30天': [now.subtract(29, 'day').startOf('day').toDate(), now.endOf('day').toDate()],
    '本月': [now.startOf('month').toDate(), now.endOf('month').toDate()],
    '上月': [
      now.subtract(1, 'month').startOf('month').toDate(),
      now.subtract(1, 'month').endOf('month').toDate()
    ]
  }
}

/**
 * 验证日期格式
 * @param {string} dateString 日期字符串
 * @param {string} format 期望的格式
 * @returns {boolean} 是否有效
 */
export const isValidDate = (dateString, format = 'YYYY-MM-DD') => {
  if (!dateString) return false
  return dayjs(dateString, format, true).isValid()
}

/**
 * 获取时间段描述
 * @param {string|Date} date 日期
 * @returns {string} 时间段描述
 */
export const getTimeOfDay = (date) => {
  if (!date) return ''

  const hour = dayjs(date).hour()

  if (hour >= 5 && hour < 12) return '上午'
  if (hour >= 12 && hour < 18) return '下午'
  if (hour >= 18 && hour < 22) return '晚上'
  return '深夜'
}

export default {
  formatDateTime,
  formatDate,
  formatTime,
  getRelativeTime,
  formatProcessingTime,
  calculateDuration,
  isToday,
  isYesterday,
  isThisWeek,
  isThisMonth,
  getDateRangeText,
  getPresetDateRanges,
  isValidDate,
  getTimeOfDay
}