import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    redirect: '/tickets'
  },
  {
    path: '/tickets',
    name: 'TicketList',
    component: () => import('@/views/TicketListNew.vue'),
    meta: {
      title: '工单列表'
    }
  },
  {
    path: '/tickets/:id',
    name: 'TicketDetail',
    component: () => import('@/views/TicketDetail.vue'),
    meta: {
      title: '工单详情'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - RPA自动化平台`
  }
  next()
})

export default router