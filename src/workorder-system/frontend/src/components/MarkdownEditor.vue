<template>
  <div class="markdown-editor">
    <MdEditor
      v-model="content"
      :height="height"
      :preview="true"
      :preview-only="readonly"
      :toolbars="toolbars"
      :placeholder="placeholder"
      @upload-img="handleUploadImg"
      @save="handleSave"
      language="zh-CN"
    />
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { MdEditor } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '400px'
  },
  placeholder: {
    type: String,
    default: '请输入内容，支持Markdown格式...'
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const content = ref(props.modelValue)

// 工具栏配置
const toolbars = computed(() => {
  if (props.readonly) return []

  return [
    'bold',
    'underline',
    'italic',
    '-',
    'title',
    'strikeThrough',
    'sub',
    'sup',
    'quote',
    'unorderedList',
    'orderedList',
    'task',
    '-',
    'codeRow',
    'code',
    'link',
    'image',
    'table',
    '-',
    'revoke',
    'next',
    'save',
    '=',
    'pageFullscreen',
    'fullscreen',
    'preview',
    'htmlPreview',
    'catalog'
  ]
})

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal !== content.value) {
    content.value = newVal
  }
})

watch(content, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理图片上传
const handleUploadImg = async (files, callback) => {
  const res = files.map((file) => ({
    url: URL.createObjectURL(file),
    alt: file.name,
    title: file.name
  }))
  callback(res)
}

// 处理保存
const handleSave = (value) => {
  emit('save', value)
}
</script>

<style scoped>
.markdown-editor {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.markdown-editor :deep(.md-editor) {
  border: none;
  border-radius: 0;
}
</style>
