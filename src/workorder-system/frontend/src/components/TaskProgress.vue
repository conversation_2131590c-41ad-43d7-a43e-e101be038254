<template>
  <div class="task-progress">
    <div class="progress-header">
      <div class="progress-title">
        <el-icon><List /></el-icon>
        <span>任务进度</span>
      </div>
      <div class="progress-summary">
        <el-tag :type="summaryTagType" size="small">
          {{ completedCount }}/{{ totalCount }}
        </el-tag>
      </div>
    </div>

    <div class="progress-bar-container">
      <el-progress
        :percentage="progressPercentage"
        :color="progressColor"
        :stroke-width="8"
        :show-text="false"
      />
      <div class="progress-text">
        {{ progressPercentage }}% 完成
      </div>
    </div>

    <div v-if="showDetails" class="task-list">
      <div
        v-for="(task, index) in tasks"
        :key="index"
        class="task-item"
        :class="{ 'completed': task.completed }"
      >
        <el-icon class="task-icon">
          <Check v-if="task.completed" />
          <Clock v-else />
        </el-icon>
        <span class="task-text">{{ task.text }}</span>
        <el-tag
          v-if="task.priority"
          :type="getPriorityType(task.priority)"
          size="small"
          class="task-priority"
        >
          {{ task.priority }}
        </el-tag>
      </div>
    </div>

    <div v-if="tasks.length > 3" class="toggle-details">
      <el-button
        type="text"
        size="small"
        @click="showDetails = !showDetails"
      >
        {{ showDetails ? '收起详情' : '展开详情' }}
        <el-icon>
          <ArrowDown v-if="!showDetails" />
          <ArrowUp v-else />
        </el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { List, Check, Clock, ArrowDown, ArrowUp } from '@element-plus/icons-vue'

const props = defineProps({
  tasks: {
    type: Array,
    default: () => []
  },
  autoExpand: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const showDetails = ref(props.autoExpand || props.tasks.length <= 3)

// 计算属性
const totalCount = computed(() => props.tasks.length)

const completedCount = computed(() => 
  props.tasks.filter(task => task.completed).length
)

const progressPercentage = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.round((completedCount.value / totalCount.value) * 100)
})

const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage === 100) return '#67c23a'
  if (percentage >= 80) return '#409eff'
  if (percentage >= 60) return '#e6a23c'
  if (percentage >= 40) return '#f56c6c'
  return '#909399'
})

const summaryTagType = computed(() => {
  const percentage = progressPercentage.value
  if (percentage === 100) return 'success'
  if (percentage >= 80) return 'primary'
  if (percentage >= 60) return 'warning'
  return 'info'
})

// 方法
const getPriorityType = (priority) => {
  switch (priority) {
    case '高':
    case 'high':
      return 'danger'
    case '中':
    case 'medium':
      return 'warning'
    case '低':
    case 'low':
      return 'info'
    default:
      return 'info'
  }
}

// 解析任务文本，提取任务列表
const parseTasksFromContent = (content) => {
  if (!content) return []
  
  const tasks = []
  const lines = content.split('\n')
  
  for (const line of lines) {
    // 匹配 Markdown 任务列表格式
    const taskMatch = line.match(/^\s*[-*+]\s*\[([x\s])\]\s*(.+)$/)
    if (taskMatch) {
      const [, status, text] = taskMatch
      tasks.push({
        completed: status.toLowerCase() === 'x',
        text: text.trim()
      })
    }
    
    // 匹配数字列表格式
    const numberedTaskMatch = line.match(/^\s*\d+\.\s*\[([x\s])\]\s*(.+)$/)
    if (numberedTaskMatch) {
      const [, status, text] = numberedTaskMatch
      tasks.push({
        completed: status.toLowerCase() === 'x',
        text: text.trim()
      })
    }
  }
  
  return tasks
}

// 暴露方法
defineExpose({
  parseTasksFromContent
})
</script>

<style scoped>
.task-progress {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #333;
}

.progress-summary {
  font-size: 14px;
}

.progress-bar-container {
  position: relative;
  margin-bottom: 16px;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.task-list {
  max-height: 200px;
  overflow-y: auto;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item.completed {
  opacity: 0.6;
}

.task-item.completed .task-text {
  text-decoration: line-through;
  color: #999;
}

.task-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.task-item.completed .task-icon {
  color: #67c23a;
}

.task-item:not(.completed) .task-icon {
  color: #e6a23c;
}

.task-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

.task-priority {
  flex-shrink: 0;
}

.toggle-details {
  text-align: center;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.toggle-details .el-button {
  font-size: 12px;
}

/* 滚动条样式 */
.task-list::-webkit-scrollbar {
  width: 4px;
}

.task-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.task-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.task-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
