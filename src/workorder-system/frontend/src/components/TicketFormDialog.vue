<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑工单' : '新建工单'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item v-if="isEdit" label="工单号">
        <el-input v-model="formData.ticket_number" disabled />
      </el-form-item>

      <el-form-item label="工单标题" prop="title">
        <div class="title-input-group">
          <el-input
            v-model="formData.title"
            placeholder="留空将自动生成标题"
            :readonly="isReadonly"
          />
          <el-button
            v-if="!isReadonly && hasContent"
            type="primary"
            :icon="MagicStick"
            @click="generateTitle"
            :loading="titleGenerating"
            size="small"
          >
            AI生成
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="优先级" prop="priority">
        <el-select
          v-model="formData.priority"
          placeholder="请选择优先级"
          :disabled="isReadonly"
        >
          <el-option label="低" :value="0" />
          <el-option label="中" :value="1" />
          <el-option label="高" :value="2" />
          <el-option label="紧急" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="isEdit" label="工单状态" prop="status">
        <el-select
          v-model="formData.status"
          placeholder="请选择状态"
          :disabled="isReadonly"
        >
          <el-option label="待开始" value="待开始" />
          <el-option label="待补充信息" value="待补充信息" />
          <el-option label="已挂起" value="已挂起" />
        </el-select>
      </el-form-item>

      <el-form-item label="工单内容" prop="content" required>
        <WysiwygEditor
          v-model="formData.content"
          :height="'400px'"
          :readonly="isReadonly"
          :placeholder="'请详细描述需要处理的工单内容...'"
          @save="handleContentSave"
        />
      </el-form-item>



      <!-- 任务进度显示 -->
      <el-form-item v-if="taskList.length > 0" label="任务进度">
        <TaskProgress
          :tasks="taskList"
          :auto-expand="taskList.length <= 5"
        />
      </el-form-item>

      <el-form-item v-if="isEdit && formData.notes" label="备注">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          placeholder="处理备注"
        />
      </el-form-item>

      <el-form-item v-if="isEdit" label="附件">
        <div class="attachment-section">
          <el-upload
            ref="uploadRef"
            :file-list="fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :auto-upload="false"
            multiple
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持jpg/png/gif/pdf/doc/docx文件，且不超过10MB
              </div>
            </template>
          </el-upload>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, MagicStick } from '@element-plus/icons-vue'
import { ticketAPI } from '@/api/tickets'
import TaskProgress from './TaskProgress.vue'
import WysiwygEditor from './WysiwygEditor.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ticket: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const loading = ref(false)
const titleGenerating = ref(false)
const fileList = ref([])

// 表单数据
const formData = reactive({
  title: '',
  content: '',
  priority: 1,
  status: 'pending',
  notes: '',
  ticket_number: '',

  processing_time: null // 处理时长
})

// 任务列表
const taskList = ref([])

// 表单验证规则
const formRules = {
  content: [
    { required: true, message: '请输入工单内容', trigger: 'blur' },
    { min: 10, message: '内容至少10个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 检查是否可以编辑
const canEdit = computed(() => {
  if (!props.isEdit) return true // 新建工单总是可以编辑
  const status = props.ticket?.status
  return ['待开始', '待补充信息', '已挂起'].includes(status)
})

// 表单是否只读
const isReadonly = computed(() => {
  return props.isEdit && !canEdit.value
})

// 检查是否有内容（去除HTML标签后检查）
const hasContent = computed(() => {
  if (!formData.content) return false
  // 去除HTML标签，只保留文本内容
  const textContent = formData.content.replace(/<[^>]*>/g, '').trim()
  return textContent.length > 0
})

// 监听器
watch(() => props.ticket, (newTicket) => {
  if (newTicket && Object.keys(newTicket).length > 0) {
    Object.assign(formData, {
      title: newTicket.title || '',
      content: newTicket.content || '',
      priority: newTicket.priority || 1,
      status: newTicket.status || '待开始',
      notes: newTicket.notes || '',
      ticket_number: newTicket.ticket_number || '',
      images: newTicket.images || [],
      created_at: newTicket.created_at,
      completed_at: newTicket.completed_at,
      processing_time: newTicket.processing_time
    })

    // 解析任务列表
    parseTasksFromContent(newTicket.content || '')
  }
}, { immediate: true, deep: true })

// 监听内容变化，实时解析任务
watch(() => formData.content, (newContent) => {
  parseTasksFromContent(newContent)
})

// 监听对话框打开/关闭，优化表单重置逻辑
watch(visible, (newVal) => {
  if (newVal && !props.isEdit) {
    // 只有在新建模式下打开对话框时才重置表单
    nextTick(() => {
      resetForm()
    })
  }
})

// 监听isEdit变化，确保模式切换时正确处理表单状态
watch(() => props.isEdit, (newIsEdit, oldIsEdit) => {
  // 从编辑模式切换到新建模式时，清空表单
  if (oldIsEdit && !newIsEdit && visible.value) {
    nextTick(() => {
      resetForm()
    })
  }
}, { flush: 'post' })

// 方法
const resetForm = () => {
  Object.assign(formData, {
    title: '',
    content: '',
    priority: 1,
    status: 'pending',
    notes: '',
    ticket_number: '',
    images: [],
    created_at: null,
    completed_at: null,
    processing_time: null
  })
  fileList.value = []
  taskList.value = []
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const insertMarkdown = (prefix, suffix) => {
  // Markdown格式插入逻辑
  const textarea = document.querySelector('.content-editor textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = formData.content.substring(start, end)
    const newText = prefix + selectedText + suffix

    formData.content = formData.content.substring(0, start) + newText + formData.content.substring(end)

    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + prefix.length, start + prefix.length + selectedText.length)
    })
  }
}

// 新增方法
const handleContentSave = ({ value, html }) => {
  // WYSIWYG编辑器保存事件
  formData.content = value || html
  parseTasksFromContent(value || html)
}

const handleImageUpload = (images) => {
  // 图片上传成功回调
  console.log('图片上传成功:', images)
}

// AI生成标题
const generateTitle = async () => {
  if (!hasContent.value) {
    ElMessage.warning('请先输入工单内容')
    return
  }

  titleGenerating.value = true
  try {
    // 简单的标题生成逻辑，基于内容关键词
    // 去除HTML标签，只保留文本内容
    const content = formData.content.replace(/<[^>]*>/g, '').trim()
    let title = ''

    // 提取关键词生成标题
    if (content.includes('下架') || content.includes('删除')) {
      title = '商品下架处理'
    } else if (content.includes('上架') || content.includes('添加')) {
      title = '商品上架处理'
    } else if (content.includes('更新') || content.includes('修改')) {
      title = '信息更新处理'
    } else if (content.includes('价格')) {
      title = '价格调整处理'
    } else if (content.includes('库存')) {
      title = '库存管理处理'
    } else if (content.includes('门店')) {
      title = '门店管理处理'
    } else if (content.includes('客户') || content.includes('投诉')) {
      title = '客户服务处理'
    } else if (content.includes('促销') || content.includes('活动')) {
      title = '营销活动处理'
    } else {
      // 默认取前20个字符作为标题
      title = content.substring(0, 20) + (content.length > 20 ? '...' : '')
    }

    formData.title = title
    ElMessage.success('标题生成成功')
  } catch (error) {
    console.error('生成标题失败:', error)
    ElMessage.error('生成标题失败')
  } finally {
    titleGenerating.value = false
  }
}



const parseTasksFromContent = (content) => {
  // 解析内容中的任务列表
  if (!content) {
    taskList.value = []
    return
  }

  const tasks = []
  const lines = content.split('\n')

  for (const line of lines) {
    // 匹配 Markdown 任务列表格式: - [x] 或 - [ ]
    const taskMatch = line.match(/^\s*[-*+]\s*\[([x\s])\]\s*(.+)$/)
    if (taskMatch) {
      const [, status, text] = taskMatch
      tasks.push({
        completed: status.toLowerCase() === 'x',
        text: text.trim(),
        priority: extractPriority(text)
      })
    }

    // 匹配数字列表格式: 1. [x] 或 1. [ ]
    const numberedTaskMatch = line.match(/^\s*\d+\.\s*\[([x\s])\]\s*(.+)$/)
    if (numberedTaskMatch) {
      const [, status, text] = numberedTaskMatch
      tasks.push({
        completed: status.toLowerCase() === 'x',
        text: text.trim(),
        priority: extractPriority(text)
      })
    }
  }

  taskList.value = tasks
}

const extractPriority = (text) => {
  // 从任务文本中提取优先级标记
  if (text.includes('🔴') || text.includes('[高]') || text.includes('[紧急]')) {
    return '高'
  }
  if (text.includes('🟡') || text.includes('[中]')) {
    return '中'
  }
  if (text.includes('🟢') || text.includes('[低]')) {
    return '低'
  }
  return null
}

const calculateProcessingTime = () => {
  // 计算处理时长
  if (!formData.created_at) return null

  const startTime = new Date(formData.created_at)
  const endTime = formData.completed_at ? new Date(formData.completed_at) : new Date()

  const diffMs = endTime - startTime
  const diffHours = Math.round(diffMs / (1000 * 60 * 60) * 10) / 10

  return diffHours
}

const handleFileChange = (file, fileList) => {
  // 文件变化处理
  console.log('文件变化:', file, fileList)
}

const handleFileRemove = (file, fileList) => {
  // 文件移除处理
  console.log('文件移除:', file, fileList)
}

const handleClose = () => {
  visible.value = false
  // 只有在新建模式下才重置表单，避免编辑模式下的意外清空
  if (!props.isEdit) {
    nextTick(() => {
      resetForm()
    })
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 如果没有标题，自动生成
    if (!formData.title.trim()) {
      await generateTitle()
    }

    // 计算处理时长
    const processingTime = calculateProcessingTime()

    const submitData = {
      title: formData.title || '工单处理',
      content: formData.content,
      priority: formData.priority,
      images: formData.images || [], // 图片附件
      processing_time: processingTime // 处理时长
    }

    if (props.isEdit) {
      submitData.status = formData.status
      submitData.notes = formData.notes
      await ticketAPI.updateTicket(props.ticket.id, submitData)
      ElMessage.success('工单更新成功')
    } else {
      await ticketAPI.createTicket(submitData)
      ElMessage.success('工单创建成功')

      // 创建成功后自动清空表单，准备下一次创建
      resetForm()
    }

    emit('success') // 发送成功事件
    visible.value = false // 关闭对话框

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.content-editor {
  position: relative;
}

.editor-toolbar {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.attachment-section {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

.el-upload {
  width: 100%;
}

.title-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.title-input-group .el-input {
  flex: 1;
}
</style>