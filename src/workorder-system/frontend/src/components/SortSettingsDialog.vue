<template>
  <el-dialog
    v-model="visible"
    title="排序设置"
    width="400px"
    :before-close="handleClose"
  >
    <el-form :model="form" label-width="80px">
      <el-form-item label="排序字段">
        <el-select v-model="form.prop" placeholder="请选择排序字段">
          <el-option label="工单号" value="ticket_number" />
          <el-option label="标题" value="title" />
          <el-option label="状态" value="status" />
          <el-option label="优先级" value="priority" />
          <el-option label="创建时间" value="created_at" />
          <el-option label="更新时间" value="updated_at" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="排序方式">
        <el-radio-group v-model="form.order">
          <el-radio value="ascending">升序</el-radio>
          <el-radio value="descending">降序</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleApply">应用</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, reactive, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  currentSort: {
    type: Object,
    default: () => ({ prop: '', order: '' })
  }
})

const emit = defineEmits(['update:modelValue', 'apply'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const form = reactive({
  prop: '',
  order: 'descending'
})

const handleClose = () => {
  visible.value = false
}

const handleApply = () => {
  emit('apply', { ...form })
  handleClose()
}

// 监听对话框打开，初始化表单
watch(visible, (newVal) => {
  if (newVal) {
    form.prop = props.currentSort.prop || 'created_at'
    form.order = props.currentSort.order || 'descending'
  }
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
