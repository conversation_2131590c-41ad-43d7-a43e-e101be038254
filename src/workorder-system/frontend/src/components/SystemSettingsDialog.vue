<template>
  <el-dialog
    v-model="visible"
    title="系统设置"
    width="600px"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 基本设置 -->
      <el-tab-pane label="基本设置" name="basic">
        <el-form :model="settings" label-width="120px">
          <el-form-item label="主题">
            <el-radio-group v-model="settings.theme">
              <el-radio value="light">浅色主题</el-radio>
              <el-radio value="dark">深色主题</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="语言">
            <el-select v-model="settings.language" placeholder="请选择语言">
              <el-option label="简体中文" value="zh-CN" />
              <el-option label="English" value="en-US" />
            </el-select>
          </el-form-item>

          <el-form-item label="自动刷新">
            <el-switch v-model="settings.autoRefresh" />
          </el-form-item>

          <el-form-item v-if="settings.autoRefresh" label="刷新间隔">
            <el-input-number
              v-model="settings.refreshInterval"
              :min="5000"
              :max="300000"
              :step="5000"
              controls-position="right"
            />
            <span class="input-suffix">毫秒</span>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 通知设置 -->
      <el-tab-pane label="通知设置" name="notifications">
        <el-form :model="settings.notifications" label-width="120px">
          <el-form-item label="启用通知">
            <el-switch v-model="settings.notifications.enabled" />
          </el-form-item>

          <el-form-item label="声音提醒">
            <el-switch v-model="settings.notifications.sound" />
          </el-form-item>

          <el-form-item label="桌面通知">
            <el-switch v-model="settings.notifications.desktop" />
          </el-form-item>

          <el-form-item label="通知类型">
            <el-checkbox-group v-model="notificationTypes">
              <el-checkbox value="ticket_created">新工单创建</el-checkbox>
              <el-checkbox value="ticket_completed">工单完成</el-checkbox>
              <el-checkbox value="ticket_failed">工单失败</el-checkbox>
              <el-checkbox value="system_error">系统错误</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 表格设置 -->
      <el-tab-pane label="表格设置" name="table">
        <el-form :model="settings.table" label-width="120px">
          <el-form-item label="每页显示">
            <el-select v-model="settings.table.pageSize">
              <el-option label="10 条" :value="10" />
              <el-option label="20 条" :value="20" />
              <el-option label="50 条" :value="50" />
              <el-option label="100 条" :value="100" />
            </el-select>
          </el-form-item>

          <el-form-item label="显示边框">
            <el-switch v-model="settings.table.showBorder" />
          </el-form-item>

          <el-form-item label="斑马纹">
            <el-switch v-model="settings.table.stripe" />
          </el-form-item>

          <el-form-item label="紧凑模式">
            <el-switch v-model="settings.table.compact" />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 高级设置 -->
      <el-tab-pane label="高级设置" name="advanced">
        <el-form label-width="120px">
          <el-form-item label="调试模式">
            <el-switch v-model="debugMode" />
            <div class="form-item-tip">
              开启后将显示更多调试信息
            </div>
          </el-form-item>

          <el-form-item label="性能监控">
            <el-switch v-model="performanceMonitor" />
            <div class="form-item-tip">
              监控系统性能指标
            </div>
          </el-form-item>

          <el-form-item label="缓存管理">
            <el-button @click="clearCache">清除缓存</el-button>
            <div class="form-item-tip">
              清除浏览器缓存和本地存储
            </div>
          </el-form-item>

          <el-form-item label="数据导出">
            <el-button @click="exportSettings">导出设置</el-button>
            <el-button @click="importSettings">导入设置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSystemStore } from '@/store/system'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const systemStore = useSystemStore()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeTab = ref('basic')
const settings = ref({})
const notificationTypes = ref([])
const debugMode = ref(false)
const performanceMonitor = ref(false)

// 初始化设置
const initSettings = () => {
  settings.value = JSON.parse(JSON.stringify(systemStore.settings))
  notificationTypes.value = ['ticket_created', 'ticket_completed', 'system_error']
  debugMode.value = localStorage.getItem('debug_mode') === 'true'
  performanceMonitor.value = localStorage.getItem('performance_monitor') === 'true'
}

const handleClose = () => {
  visible.value = false
}

const handleSave = async () => {
  try {
    // 保存基本设置
    systemStore.updateSettings(settings.value)

    // 保存高级设置
    localStorage.setItem('debug_mode', debugMode.value.toString())
    localStorage.setItem('performance_monitor', performanceMonitor.value.toString())
    localStorage.setItem('notification_types', JSON.stringify(notificationTypes.value))

    ElMessage.success('设置保存成功')
    handleClose()
  } catch (error) {
    ElMessage.error('保存设置失败: ' + error.message)
  }
}

const handleReset = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有设置吗？', '确认重置', {
      type: 'warning'
    })

    systemStore.resetSettings()
    systemStore.resetPreferences()

    // 重置高级设置
    debugMode.value = false
    performanceMonitor.value = false
    notificationTypes.value = ['ticket_created', 'ticket_completed', 'system_error']

    localStorage.removeItem('debug_mode')
    localStorage.removeItem('performance_monitor')
    localStorage.removeItem('notification_types')

    initSettings()
    ElMessage.success('设置已重置')
  } catch (error) {
    // 用户取消
  }
}

const clearCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清除所有缓存吗？', '确认清除', {
      type: 'warning'
    })

    // 清除localStorage
    const keysToKeep = ['workorder_settings', 'workorder_preferences']
    const allKeys = Object.keys(localStorage)

    allKeys.forEach(key => {
      if (!keysToKeep.includes(key)) {
        localStorage.removeItem(key)
      }
    })

    // 清除sessionStorage
    sessionStorage.clear()

    ElMessage.success('缓存清除成功')
  } catch (error) {
    // 用户取消
  }
}

const exportSettings = () => {
  const exportData = {
    settings: settings.value,
    preferences: systemStore.userPreferences,
    notificationTypes: notificationTypes.value,
    debugMode: debugMode.value,
    performanceMonitor: performanceMonitor.value,
    exportTime: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(exportData, null, 2)], {
    type: 'application/json'
  })

  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `workorder-settings-${new Date().toISOString().split('T')[0]}.json`
  a.click()

  URL.revokeObjectURL(url)
  ElMessage.success('设置导出成功')
}

const importSettings = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'

  input.onchange = (event) => {
    const file = event.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importData = JSON.parse(e.target.result)

        if (importData.settings) {
          settings.value = importData.settings
        }

        if (importData.notificationTypes) {
          notificationTypes.value = importData.notificationTypes
        }

        if (typeof importData.debugMode === 'boolean') {
          debugMode.value = importData.debugMode
        }

        if (typeof importData.performanceMonitor === 'boolean') {
          performanceMonitor.value = importData.performanceMonitor
        }

        ElMessage.success('设置导入成功')
      } catch (error) {
        ElMessage.error('导入文件格式错误')
      }
    }

    reader.readAsText(file)
  }

  input.click()
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal) {
    initSettings()
  }
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.input-suffix {
  margin-left: 10px;
  color: #909399;
  font-size: 14px;
}

.form-item-tip {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

:deep(.el-tabs__content) {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}
</style>