<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="500px"
    :before-close="handleClose"
  >
    <div class="batch-action-content">
      <el-alert
        :title="`确定要对选中的 ${tickets.length} 个工单执行 ${actionText} 操作吗？`"
        type="warning"
        show-icon
        :closable="false"
      />

      <div v-if="action === 'updateStatus'" class="status-selection">
        <el-form :model="form" label-width="80px">
          <el-form-item label="新状态">
            <el-select v-model="form.status" placeholder="请选择状态">
              <el-option label="待开始" value="待开始" />
              <el-option label="排队中" value="排队中" />
              <el-option label="处理中" value="处理中" />
              <el-option label="已完成" value="已完成" />
              <el-option label="已暂停" value="已暂停" />
              <el-option label="失败" value="失败" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="form.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息（可选）"
            />
          </el-form-item>
        </el-form>
      </div>

      <div v-if="tickets.length > 0" class="ticket-list">
        <h4>影响的工单：</h4>
        <div class="ticket-items">
          <div
            v-for="ticket in tickets.slice(0, 5)"
            :key="ticket.id"
            class="ticket-item"
          >
            <span class="ticket-number">{{ ticket.ticket_number }}</span>
            <span class="ticket-title">{{ ticket.title }}</span>
            <el-tag :type="getStatusType(ticket.status)" size="small">
              {{ ticket.status }}
            </el-tag>
          </div>
          <div v-if="tickets.length > 5" class="more-tickets">
            还有 {{ tickets.length - 5 }} 个工单...
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="action === 'updateStatus' && !form.status"
          @click="handleConfirm"
        >
          确认{{ actionText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, reactive, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  action: {
    type: String,
    required: true
  },
  tickets: {
    type: Array,
    default: () => []
  },
  options: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const form = reactive({
  status: '',
  notes: ''
})

const actionTextMap = {
  start: '开始',
  suspend: '暂停',
  delete: '删除',
  copy: '复制',
  export: '导出',
  updateStatus: '更新状态'
}

const actionText = computed(() => actionTextMap[props.action] || '操作')

const dialogTitle = computed(() => `批量${actionText.value}`)

const getStatusType = (status) => {
  const statusMap = {
    '待开始': 'info',
    '排队中': 'warning',
    '处理中': 'primary',
    '已完成': 'success',
    '失败': 'danger',
    '已暂停': 'warning'
  }
  return statusMap[status] || 'info'
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleConfirm = () => {
  const options = { ...props.options }
  
  if (props.action === 'updateStatus') {
    options.status = form.status
    options.notes = form.notes
  }
  
  emit('confirm', props.action, props.tickets, options)
  handleClose()
}

const resetForm = () => {
  form.status = ''
  form.notes = ''
}

// 监听对话框打开，重置表单
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.batch-action-content {
  margin-bottom: 20px;
}

.status-selection {
  margin: 20px 0;
}

.ticket-list {
  margin-top: 20px;
}

.ticket-list h4 {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.ticket-items {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.ticket-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.ticket-item:last-child {
  border-bottom: none;
}

.ticket-number {
  font-weight: bold;
  color: #409eff;
  min-width: 100px;
}

.ticket-title {
  flex: 1;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-tickets {
  text-align: center;
  color: #909399;
  font-style: italic;
  margin-top: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
