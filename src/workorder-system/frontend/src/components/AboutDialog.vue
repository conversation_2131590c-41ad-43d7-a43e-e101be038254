<template>
  <el-dialog
    v-model="visible"
    title="关于系统"
    width="500px"
    :before-close="handleClose"
  >
    <div class="about-content">
      <!-- 系统信息 -->
      <div class="system-info">
        <div class="logo-section">
          <div class="logo">
            <el-icon size="48" color="#409eff">
              <Setting />
            </el-icon>
          </div>
          <h2>RPA自动化平台</h2>
          <p class="version">版本 {{ systemInfo.version }}</p>
        </div>

        <el-descriptions :column="1" border>
          <el-descriptions-item label="系统名称">
            {{ systemInfo.name }}
          </el-descriptions-item>
          <el-descriptions-item label="版本号">
            {{ systemInfo.version }}
          </el-descriptions-item>
          <el-descriptions-item label="构建时间">
            {{ systemInfo.buildTime }}
          </el-descriptions-item>
          <el-descriptions-item label="运行环境">
            {{ systemInfo.environment }}
          </el-descriptions-item>
          <el-descriptions-item label="Node.js版本">
            {{ systemInfo.nodeVersion }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库">
            {{ systemInfo.database }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 功能特性 -->
      <div class="features-section">
        <h3>主要功能</h3>
        <ul class="features-list">
          <li>
            <el-icon><Check /></el-icon>
            工单管理系统
          </li>
          <li>
            <el-icon><Check /></el-icon>
            AI智能助手
          </li>
          <li>
            <el-icon><Check /></el-icon>
            浏览器自动化
          </li>
          <li>
            <el-icon><Check /></el-icon>
            实时状态监控
          </li>
          <li>
            <el-icon><Check /></el-icon>
            任务队列管理
          </li>
          <li>
            <el-icon><Check /></el-icon>
            WebSocket实时通信
          </li>
        </ul>
      </div>

      <!-- 技术栈 -->
      <div class="tech-stack">
        <h3>技术栈</h3>
        <div class="tech-tags">
          <el-tag v-for="tech in techStack" :key="tech.name" :type="tech.type">
            {{ tech.name }} {{ tech.version }}
          </el-tag>
        </div>
      </div>

      <!-- 开发团队 -->
      <div class="team-section">
        <h3>开发团队</h3>
        <p>RPA开发团队</p>
        <p class="contact">
          <el-icon><Message /></el-icon>
          联系邮箱: <EMAIL>
        </p>
      </div>

      <!-- 许可证 -->
      <div class="license-section">
        <h3>许可证</h3>
        <p>本软件基于 MIT 许可证开源</p>
        <div class="license-links">
          <el-button link type="primary" @click="openLicense">
            查看许可证
          </el-button>
          <el-button link type="primary" @click="openSource">
            源代码
          </el-button>
          <el-button link type="primary" @click="openDocs">
            使用文档
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleClose">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Setting, Check, Message } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 系统信息
const systemInfo = ref({
  name: 'RPA自动化平台',
  version: '1.0.0',
  buildTime: '2024-01-15 10:30:00',
  environment: 'Production',
  nodeVersion: process.version || 'v18.17.0',
  database: 'SQLite 3.42.0'
})

// 技术栈
const techStack = ref([
  { name: 'Vue.js', version: '3.3.4', type: 'success' },
  { name: 'Element Plus', version: '2.4.1', type: 'primary' },
  { name: 'Node.js', version: '18.17.0', type: 'success' },
  { name: 'Express', version: '4.18.2', type: 'info' },
  { name: 'SQLite', version: '3.42.0', type: 'warning' },
  { name: 'Puppeteer', version: '21.5.0', type: 'danger' },
  { name: 'WebSocket', version: '8.14.2', type: 'primary' },
  { name: 'Pinia', version: '2.1.6', type: 'success' }
])

const handleClose = () => {
  visible.value = false
}

const openLicense = () => {
  window.open('https://opensource.org/licenses/MIT', '_blank')
}

const openSource = () => {
  window.open('https://github.com/your-org/rpa-platform', '_blank')
}

const openDocs = () => {
  window.open('https://docs.rpa-platform.com', '_blank')
}
</script>

<style scoped>
.about-content {
  max-height: 600px;
  overflow-y: auto;
}

.logo-section {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  margin-bottom: 15px;
}

.logo-section h2 {
  margin: 10px 0 5px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.version {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.system-info {
  margin-bottom: 30px;
}

.features-section,
.tech-stack,
.team-section,
.license-section {
  margin-bottom: 25px;
}

.features-section h3,
.tech-stack h3,
.team-section h3,
.license-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px 0;
  color: #606266;
}

.features-list li .el-icon {
  color: #67c23a;
  font-size: 16px;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.team-section p {
  margin: 5px 0;
  color: #606266;
}

.contact {
  display: flex;
  align-items: center;
  gap: 5px;
}

.license-section p {
  margin: 5px 0 15px 0;
  color: #606266;
}

.license-links {
  display: flex;
  gap: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>