<template>
  <el-dialog
    v-model="visible"
    title="列设置"
    width="500px"
    :before-close="handleClose"
  >
    <div class="column-settings">
      <el-alert
        title="拖拽调整列顺序，勾选控制列显示"
        type="info"
        show-icon
        :closable="false"
      />
      
      <div class="column-list">
        <div
          v-for="column in localColumns"
          :key="column.key"
          class="column-item"
          :class="{ disabled: !column.visible }"
        >
          <el-checkbox
            v-model="column.visible"
            :disabled="column.required"
          >
            {{ column.label }}
          </el-checkbox>
          <div class="column-controls">
            <el-tag v-if="column.required" size="small" type="info">必需</el-tag>
            <el-icon class="drag-handle">
              <Rank />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleApply">应用</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { Rank } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  columns: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'apply'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const localColumns = ref([])

const defaultColumns = [
  { key: 'selection', label: '选择', visible: true, required: true },
  { key: 'ticket_number', label: '工单号', visible: true, required: true },
  { key: 'title', label: '标题', visible: true, required: true },
  { key: 'status', label: '状态', visible: true, required: false },
  { key: 'priority', label: '优先级', visible: true, required: false },
  { key: 'progress', label: '进度', visible: true, required: false },
  { key: 'notes', label: '备注', visible: true, required: false },
  { key: 'processing_time', label: '处理时长', visible: true, required: false },
  { key: 'created_at', label: '创建时间', visible: true, required: false },
  { key: 'updated_at', label: '更新时间', visible: false, required: false },
  { key: 'actions', label: '操作', visible: true, required: true }
]

const handleClose = () => {
  visible.value = false
}

const handleApply = () => {
  const settings = {
    columns: localColumns.value.map(col => ({
      key: col.key,
      visible: col.visible
    }))
  }
  emit('apply', settings)
  handleClose()
}

const handleReset = () => {
  localColumns.value = defaultColumns.map(col => ({ ...col }))
}

// 监听对话框打开，初始化列设置
watch(visible, (newVal) => {
  if (newVal) {
    if (props.columns.length > 0) {
      localColumns.value = props.columns.map(col => ({ ...col }))
    } else {
      localColumns.value = defaultColumns.map(col => ({ ...col }))
    }
  }
})
</script>

<style scoped>
.column-settings {
  margin-bottom: 20px;
}

.column-list {
  margin-top: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.column-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  background: #fff;
  transition: all 0.3s;
}

.column-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.column-item.disabled {
  opacity: 0.6;
  background: #f5f7fa;
}

.column-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-handle {
  cursor: move;
  color: #909399;
}

.drag-handle:hover {
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
