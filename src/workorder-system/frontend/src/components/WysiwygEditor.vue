<template>
  <div class="wysiwyg-editor">
    <QuillEditor
      v-model:content="content"
      :options="editorOptions"
      :disabled="readonly"
      content-type="html"
      @update:content="handleContentChange"
      @ready="handleEditorReady"
    />
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '400px'
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const content = ref(props.modelValue)

// Quill编辑器配置
const editorOptions = computed(() => ({
  theme: 'snow',
  placeholder: props.placeholder,
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],        // 基本格式
      ['blockquote', 'code-block'],                     // 引用和代码块
      [{ 'header': 1 }, { 'header': 2 }],               // 标题
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],     // 列表
      [{ 'script': 'sub'}, { 'script': 'super' }],      // 上下标
      [{ 'indent': '-1'}, { 'indent': '+1' }],          // 缩进
      [{ 'direction': 'rtl' }],                         // 文本方向
      [{ 'size': ['small', false, 'large', 'huge'] }],  // 字体大小
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],        // 标题级别
      [{ 'color': [] }, { 'background': [] }],          // 字体颜色和背景色
      [{ 'font': [] }],                                 // 字体
      [{ 'align': [] }],                                // 对齐方式
      ['clean'],                                        // 清除格式
      ['link', 'image']                                 // 链接和图片
    ]
  },
  readOnly: props.readonly
}))

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal !== content.value) {
    content.value = newVal || ''
  }
}, { immediate: true })

watch(content, (newVal) => {
  emit('update:modelValue', newVal)
})

// 事件处理
const handleContentChange = (newContent) => {
  content.value = newContent
  emit('update:modelValue', newContent)
}

const handleEditorReady = (quill) => {
  // 设置编辑器高度
  const editorContainer = quill.container.parentElement
  if (editorContainer) {
    editorContainer.style.height = props.height
  }
  
  // 添加保存快捷键
  quill.keyboard.addBinding({
    key: 'S',
    ctrlKey: true
  }, () => {
    emit('save', { value: content.value, html: content.value })
    return false
  })
}
</script>

<style scoped>
.wysiwyg-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.wysiwyg-editor :deep(.ql-container) {
  font-size: 14px;
  line-height: 1.6;
}

.wysiwyg-editor :deep(.ql-editor) {
  min-height: 200px;
  padding: 12px 15px;
}

.wysiwyg-editor :deep(.ql-toolbar) {
  border-bottom: 1px solid #dcdfe6;
  background: #fafafa;
}

.wysiwyg-editor :deep(.ql-snow .ql-tooltip) {
  z-index: 9999;
}

/* 禁用状态样式 */
.wysiwyg-editor :deep(.ql-container.ql-disabled) {
  background: #f5f7fa;
}

.wysiwyg-editor :deep(.ql-toolbar.ql-disabled) {
  background: #f5f7fa;
  opacity: 0.6;
}
</style>
