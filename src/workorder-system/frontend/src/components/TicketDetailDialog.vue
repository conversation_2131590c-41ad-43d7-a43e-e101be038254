<template>
  <el-dialog
    v-model="visible"
    title="工单详情"
    width="1000px"
    :before-close="handleClose"
  >
    <div v-if="ticket" class="ticket-detail">
      <!-- 基本信息 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="工单号">
            {{ ticket.ticket_number }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(ticket.status)">
              {{ ticket.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(ticket.priority)">
              {{ getPriorityText(ticket.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(ticket.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(ticket.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="处理时长" v-if="ticket.processing_time">
            {{ ticket.processing_time }} 小时
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 工单内容 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span>工单内容</span>
          </div>
        </template>
        <div class="content-section">
          <h4>{{ ticket.title }}</h4>
          <div class="content-text" v-html="renderMarkdown(ticket.content)"></div>
        </div>
      </el-card>

      <!-- 备注信息 (仅当需要补充信息时显示) -->
      <el-card
        v-if="ticket.notes && ticket.status === '待补充信息'"
        class="detail-section"
        shadow="never"
      >
        <template #header>
          <div class="section-header">
            <span>需要补充的信息</span>
            <el-tag type="warning" size="small">待补充</el-tag>
          </div>
        </template>
        <div class="notes-section">
          <div class="notes-text">{{ formatNotes(ticket.notes) }}</div>
          <div class="notes-meta">
            <span>更新时间：{{ formatDateTime(ticket.updated_at) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 任务详情 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span>任务详情</span>
            <div class="task-progress">
              <span>进度：{{ getTaskProgress() }}</span>
              <el-progress
                :percentage="getTaskProgressPercentage()"
                :status="getTaskProgressStatus()"
                style="width: 200px; margin-left: 10px;"
              />
            </div>
          </div>
        </template>
        <div class="tasks-section">
          <div v-if="ticket.tasks && ticket.tasks.length > 0" class="task-list">
            <div
              v-for="(task, index) in ticket.tasks"
              :key="task.id"
              class="task-item"
              :class="{ completed: task.status === '已完成' }"
            >
              <div class="task-header">
                <div class="task-info">
                  <el-icon class="task-icon">
                    <Check v-if="task.status === '已完成'" />
                    <Loading v-else-if="task.status === '处理中'" />
                    <Clock v-else />
                  </el-icon>
                  <span class="task-title">{{ task.title }}</span>
                  <el-tag
                    :type="getTaskStatusType(task.status)"
                    size="small"
                  >
                    {{ task.status }}
                  </el-tag>
                </div>
                <div class="task-meta">
                  <span v-if="task.completed_at">
                    完成时间：{{ formatDateTime(task.completed_at) }}
                  </span>
                </div>
              </div>
              <div v-if="task.description" class="task-description">
                {{ task.description }}
              </div>
              <div v-if="task.screenshot_url" class="task-screenshot">
                <img :src="task.screenshot_url" alt="任务截图" @click="previewImage(task.screenshot_url)" />
              </div>
              <div v-if="task.summary" class="task-summary">
                <strong>小结：</strong>{{ task.summary }}
              </div>
            </div>
          </div>
          <div v-else class="no-tasks">
            <el-empty description="暂无任务信息" />
          </div>
        </div>
      </el-card>

      <!-- 完单总结 (仅当工单为已完成时显示) -->
      <el-card
        v-if="ticket.summary && ticket.status === '已完成'"
        class="detail-section"
        shadow="never"
      >
        <template #header>
          <div class="section-header">
            <span>完单总结</span>
            <el-tag type="success" size="small">已完成</el-tag>
          </div>
        </template>
        <div class="summary-section">
          <div class="summary-text" v-html="renderMarkdown(ticket.summary)"></div>
          <div class="summary-meta">
            <span>完成时间：{{ formatDateTime(ticket.completed_at) }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="canEdit(ticket?.status)"
          type="primary"
          @click="handleEdit"
        >
          编辑
        </el-button>
        <el-tooltip
          v-else-if="ticket?.status"
          content="已完成的工单不能编辑"
          placement="top"
        >
          <el-button type="primary" disabled>
            编辑
          </el-button>
        </el-tooltip>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref } from 'vue'
import { ElImageViewer } from 'element-plus'
import { Check, Loading, Clock } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
import { marked } from 'marked'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ticket: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'edit'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 格式化备注信息
const formatNotes = (notes) => {
  if (!notes) return ''

  // 如果是字符串，直接返回
  if (typeof notes === 'string') {
    // 检查是否是JSON字符串
    try {
      const parsed = JSON.parse(notes)
      if (typeof parsed === 'object' && parsed !== null) {
        return formatNotesObject(parsed)
      }
    } catch (e) {
      // 不是JSON，直接返回字符串
      return notes
    }
    return notes
  }

  // 如果是对象，格式化显示
  if (typeof notes === 'object' && notes !== null) {
    return formatNotesObject(notes)
  }

  return String(notes)
}

// 格式化备注对象
const formatNotesObject = (notesObj) => {
  // 确保输入是对象
  if (!notesObj || typeof notesObj !== 'object') {
    return '需要补充相关信息'
  }

  // 如果有message字段，优先显示
  if (notesObj.message && typeof notesObj.message === 'string') {
    return notesObj.message
  }

  // 如果有summary字段，显示摘要
  if (notesObj.summary && typeof notesObj.summary === 'string') {
    return notesObj.summary
  }

  // 如果有requiredInfo字段，显示需要补充的信息
  if (notesObj.requiredInfo && Array.isArray(notesObj.requiredInfo)) {
    return `需要补充以下信息：${notesObj.requiredInfo.join('；')}`
  }

  // 如果有content字段，显示内容
  if (notesObj.content && typeof notesObj.content === 'string') {
    return notesObj.content
  }

  // 如果有description字段，显示描述
  if (notesObj.description && typeof notesObj.description === 'string') {
    return notesObj.description
  }

  // 如果有text字段，显示文本
  if (notesObj.text && typeof notesObj.text === 'string') {
    return notesObj.text
  }

  // 遍历所有属性，寻找第一个有意义的字符串值
  for (const [key, value] of Object.entries(notesObj)) {
    if (typeof value === 'string' && value.trim()) {
      return value
    }
  }

  // 如果对象有属性但都不是字符串，尝试显示对象的键
  const keys = Object.keys(notesObj)
  if (keys.length > 0) {
    return `包含信息：${keys.join('、')}`
  }

  // 最后返回简化的对象描述
  return '需要补充相关信息'
}

// 工具函数
const getStatusType = (status) => {
  const statusMap = {
    '待开始': 'info',
    '排队中': 'warning',
    '处理中': 'primary',
    '待补充信息': 'info',
    '已完成': 'success',
    '处理失败': 'danger',
    '已挂起': 'warning'
  }
  return statusMap[status] || 'info'
}

const getPriorityType = (priority) => {
  const priorityMap = {
    0: 'info',
    1: 'warning',
    2: 'danger',
    3: 'danger'
  }
  return priorityMap[priority] || 'info'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    0: '低',
    1: '中',
    2: '高',
    3: '紧急'
  }
  return priorityMap[priority] || '未知'
}

const getTaskStatusType = (status) => {
  const statusMap = {
    '待开始': 'info',
    '处理中': 'primary',
    '已完成': 'success',
    '失败': 'danger'
  }
  return statusMap[status] || 'info'
}

const getTaskProgress = () => {
  if (!props.ticket?.tasks || props.ticket.tasks.length === 0) {
    return '0/0'
  }
  const completed = props.ticket.tasks.filter(t => t.status === '已完成').length
  return `${completed}/${props.ticket.tasks.length}`
}

const getTaskProgressPercentage = () => {
  if (!props.ticket?.tasks || props.ticket.tasks.length === 0) {
    return 0
  }
  const completed = props.ticket.tasks.filter(t => t.status === '已完成').length
  return Math.round((completed / props.ticket.tasks.length) * 100)
}

const getTaskProgressStatus = () => {
  const percentage = getTaskProgressPercentage()
  if (percentage === 100) return 'success'
  if (percentage === 0) return ''
  return ''
}

const canEdit = (status) => {
  return ['待开始', '待补充信息', '已挂起'].includes(status)
}

const renderMarkdown = (content) => {
  if (!content) return ''
  try {
    return marked(content)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return content
  }
}

const previewImage = (url) => {
  previewImageUrl.value = url
  imagePreviewVisible.value = true
}

const handleClose = () => {
  visible.value = false
}

const handleEdit = () => {
  emit('edit', props.ticket)
}
</script>

<style scoped>
.ticket-detail {
  margin-bottom: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.task-progress {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.content-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.content-text {
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

.notes-section {
  background: #fdf6ec;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #e6a23c;
}

.notes-text {
  color: #e6a23c;
  font-weight: 500;
  margin-bottom: 8px;
}

.notes-meta {
  font-size: 12px;
  color: #909399;
}

.tasks-section {
  max-height: 400px;
  overflow-y: auto;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.task-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
  background: #fafafa;
  transition: all 0.3s;
}

.task-item.completed {
  background: #f0f9ff;
  border-color: #67c23a;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-icon {
  font-size: 16px;
}

.task-title {
  font-weight: 500;
  color: #303133;
}

.task-meta {
  font-size: 12px;
  color: #909399;
}

.task-description {
  margin-bottom: 10px;
  color: #606266;
  line-height: 1.5;
}

.task-screenshot {
  margin-bottom: 10px;
}

.task-screenshot img {
  max-width: 200px;
  max-height: 150px;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.3s;
}

.task-screenshot img:hover {
  transform: scale(1.05);
}

.task-summary {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

.summary-section {
  background: #f0f9ff;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #67c23a;
}

.summary-text {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 10px;
}

.summary-meta {
  font-size: 12px;
  color: #909399;
}

.no-tasks {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .task-progress {
    width: 100%;
  }

  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
