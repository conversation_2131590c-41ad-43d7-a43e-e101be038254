import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ticketAPI } from '../api/tickets'

export const useTicketsStore = defineStore('tickets', () => {
  // 状态
  const tickets = ref([])
  const loading = ref(false)
  const error = ref(null)
  const selectedTickets = ref([])
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  
  // 筛选条件
  const filters = ref({
    status: '',
    priority: '',
    assignee: '',
    dateRange: [],
    keyword: ''
  })

  // 计算属性
  const stats = computed(() => {
    const statusCounts = tickets.value.reduce((acc, ticket) => {
      acc[ticket.status] = (acc[ticket.status] || 0) + 1
      return acc
    }, {})
    
    return {
      total: tickets.value.length,
      pending: statusCounts.pending || 0,
      processing: statusCounts.processing || 0,
      completed: statusCounts.completed || 0,
      failed: statusCounts.failed || 0
    }
  })

  const filteredTickets = computed(() => {
    let result = tickets.value

    // 状态筛选
    if (filters.value.status) {
      result = result.filter(ticket => ticket.status === filters.value.status)
    }

    // 优先级筛选
    if (filters.value.priority) {
      result = result.filter(ticket => ticket.priority === filters.value.priority)
    }

    // 负责人筛选
    if (filters.value.assignee) {
      result = result.filter(ticket => ticket.assignee === filters.value.assignee)
    }

    // 关键词搜索
    if (filters.value.keyword) {
      const keyword = filters.value.keyword.toLowerCase()
      result = result.filter(ticket => 
        ticket.title.toLowerCase().includes(keyword) ||
        ticket.description.toLowerCase().includes(keyword) ||
        ticket.id.toString().includes(keyword)
      )
    }

    // 日期范围筛选
    if (filters.value.dateRange && filters.value.dateRange.length === 2) {
      const [startDate, endDate] = filters.value.dateRange
      result = result.filter(ticket => {
        const ticketDate = new Date(ticket.created_at)
        return ticketDate >= startDate && ticketDate <= endDate
      })
    }

    return result
  })

  // 分页数据
  const paginatedTickets = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return filteredTickets.value.slice(start, end)
  })

  // Actions
  const loadTickets = async (params = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await ticketAPI.getTickets(params)
      tickets.value = response.data || []
      total.value = response.total || tickets.value.length
    } catch (err) {
      error.value = err.message || '加载工单失败'
      console.error('加载工单失败:', err)
    } finally {
      loading.value = false
    }
  }

  const createTicket = async (ticketData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await ticketAPI.createTicket(ticketData)
      tickets.value.unshift(response.data)
      return response.data
    } catch (err) {
      error.value = err.message || '创建工单失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTicket = async (id, ticketData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await ticketAPI.updateTicket(id, ticketData)
      const index = tickets.value.findIndex(ticket => ticket.id === id)
      if (index !== -1) {
        tickets.value[index] = response.data
      }
      return response.data
    } catch (err) {
      error.value = err.message || '更新工单失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTicketStatus = async (id, status) => {
    try {
      const response = await ticketAPI.updateTicketStatus(id, status)
      const index = tickets.value.findIndex(ticket => ticket.id === id)
      if (index !== -1) {
        tickets.value[index].status = status
        tickets.value[index].updated_at = new Date().toISOString()
      }
      return response.data
    } catch (err) {
      error.value = err.message || '更新工单状态失败'
      throw err
    }
  }

  const deleteTicket = async (id) => {
    loading.value = true
    error.value = null
    
    try {
      await ticketAPI.deleteTicket(id)
      tickets.value = tickets.value.filter(ticket => ticket.id !== id)
    } catch (err) {
      error.value = err.message || '删除工单失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const batchUpdateStatus = async (ticketIds, status) => {
    loading.value = true
    error.value = null
    
    try {
      const promises = ticketIds.map(id => updateTicketStatus(id, status))
      await Promise.all(promises)
      
      // 更新本地状态
      ticketIds.forEach(id => {
        const index = tickets.value.findIndex(ticket => ticket.id === id)
        if (index !== -1) {
          tickets.value[index].status = status
          tickets.value[index].updated_at = new Date().toISOString()
        }
      })
    } catch (err) {
      error.value = err.message || '批量更新失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
    currentPage.value = 1 // 重置到第一页
  }

  const clearFilters = () => {
    filters.value = {
      status: '',
      priority: '',
      assignee: '',
      dateRange: [],
      keyword: ''
    }
    currentPage.value = 1
  }

  const setSelectedTickets = (tickets) => {
    selectedTickets.value = tickets
  }

  const clearSelectedTickets = () => {
    selectedTickets.value = []
  }

  const setPage = (page) => {
    currentPage.value = page
  }

  const setPageSize = (size) => {
    pageSize.value = size
    currentPage.value = 1
  }

  return {
    // 状态
    tickets,
    loading,
    error,
    selectedTickets,
    currentPage,
    pageSize,
    total,
    filters,
    
    // 计算属性
    stats,
    filteredTickets,
    paginatedTickets,
    
    // Actions
    loadTickets,
    createTicket,
    updateTicket,
    updateTicketStatus,
    deleteTicket,
    batchUpdateStatus,
    setFilters,
    clearFilters,
    setSelectedTickets,
    clearSelectedTickets,
    setPage,
    setPageSize
  }
})
