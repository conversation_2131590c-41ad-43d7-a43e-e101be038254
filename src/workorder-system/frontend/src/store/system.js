import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { systemAPI } from '../api/system'

export const useSystemStore = defineStore('system', () => {
  // 状态
  const systemStatus = ref({
    status: 'unknown',
    uptime: 0,
    memory: {
      used: 0,
      total: 0,
      percentage: 0
    },
    database: {
      connected: false,
      tables: 0,
      records: 0
    },
    websocket: {
      connected: false,
      clients: 0
    },
    ai_assistant: {
      status: 'offline',
      workers: 0,
      queue_size: 0
    }
  })

  const loading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)

  // 应用设置
  const settings = ref({
    theme: 'light',
    language: 'zh-CN',
    autoRefresh: true,
    refreshInterval: 30000, // 30秒
    notifications: {
      enabled: true,
      sound: true,
      desktop: false
    },
    table: {
      pageSize: 20,
      showBorder: true,
      stripe: true
    }
  })

  // 用户偏好
  const userPreferences = ref({
    sidebarCollapsed: false,
    favoriteFilters: [],
    recentSearches: [],
    customColumns: []
  })

  // 计算属性
  const isSystemHealthy = computed(() => {
    return systemStatus.value.status === 'healthy' &&
           systemStatus.value.database.connected &&
           systemStatus.value.memory.percentage < 90
  })

  const systemStatusColor = computed(() => {
    switch (systemStatus.value.status) {
      case 'healthy': return 'success'
      case 'warning': return 'warning'
      case 'error': return 'danger'
      default: return 'info'
    }
  })

  const memoryUsageColor = computed(() => {
    const percentage = systemStatus.value.memory.percentage
    if (percentage < 60) return 'success'
    if (percentage < 80) return 'warning'
    return 'danger'
  })

  const aiAssistantStatusColor = computed(() => {
    switch (systemStatus.value.ai_assistant.status) {
      case 'online': return 'success'
      case 'busy': return 'warning'
      case 'error': return 'danger'
      default: return 'info'
    }
  })

  // Actions
  const loadSystemStatus = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await systemAPI.getStatus()
      systemStatus.value = response.data
      lastUpdated.value = new Date()
    } catch (err) {
      error.value = err.message || '获取系统状态失败'
      console.error('获取系统状态失败:', err)
    } finally {
      loading.value = false
    }
  }

  const updateSettings = (newSettings) => {
    settings.value = { ...settings.value, ...newSettings }
    saveSettingsToStorage()
  }

  const updateUserPreferences = (newPreferences) => {
    userPreferences.value = { ...userPreferences.value, ...newPreferences }
    savePreferencesToStorage()
  }

  const toggleTheme = () => {
    const newTheme = settings.value.theme === 'light' ? 'dark' : 'light'
    updateSettings({ theme: newTheme })
    applyTheme(newTheme)
  }

  const applyTheme = (theme) => {
    const html = document.documentElement
    if (theme === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  const toggleSidebar = () => {
    updateUserPreferences({ 
      sidebarCollapsed: !userPreferences.value.sidebarCollapsed 
    })
  }

  const addFavoriteFilter = (filter) => {
    const favorites = [...userPreferences.value.favoriteFilters]
    const exists = favorites.find(f => JSON.stringify(f) === JSON.stringify(filter))
    
    if (!exists) {
      favorites.push({
        ...filter,
        id: Date.now(),
        name: filter.name || '未命名筛选',
        createdAt: new Date().toISOString()
      })
      updateUserPreferences({ favoriteFilters: favorites })
    }
  }

  const removeFavoriteFilter = (filterId) => {
    const favorites = userPreferences.value.favoriteFilters.filter(f => f.id !== filterId)
    updateUserPreferences({ favoriteFilters: favorites })
  }

  const addRecentSearch = (searchTerm) => {
    if (!searchTerm.trim()) return

    const recent = [...userPreferences.value.recentSearches]
    const index = recent.indexOf(searchTerm)
    
    if (index > -1) {
      recent.splice(index, 1)
    }
    
    recent.unshift(searchTerm)
    
    // 只保留最近10个搜索
    if (recent.length > 10) {
      recent.splice(10)
    }
    
    updateUserPreferences({ recentSearches: recent })
  }

  const clearRecentSearches = () => {
    updateUserPreferences({ recentSearches: [] })
  }

  const saveSettingsToStorage = () => {
    try {
      localStorage.setItem('workorder_settings', JSON.stringify(settings.value))
    } catch (err) {
      console.error('保存设置失败:', err)
    }
  }

  const savePreferencesToStorage = () => {
    try {
      localStorage.setItem('workorder_preferences', JSON.stringify(userPreferences.value))
    } catch (err) {
      console.error('保存用户偏好失败:', err)
    }
  }

  const loadSettingsFromStorage = () => {
    try {
      const saved = localStorage.getItem('workorder_settings')
      if (saved) {
        const parsedSettings = JSON.parse(saved)
        settings.value = { ...settings.value, ...parsedSettings }
        applyTheme(settings.value.theme)
      }
    } catch (err) {
      console.error('加载设置失败:', err)
    }
  }

  const loadPreferencesFromStorage = () => {
    try {
      const saved = localStorage.getItem('workorder_preferences')
      if (saved) {
        const parsedPreferences = JSON.parse(saved)
        userPreferences.value = { ...userPreferences.value, ...parsedPreferences }
      }
    } catch (err) {
      console.error('加载用户偏好失败:', err)
    }
  }

  const resetSettings = () => {
    settings.value = {
      theme: 'light',
      language: 'zh-CN',
      autoRefresh: true,
      refreshInterval: 30000,
      notifications: {
        enabled: true,
        sound: true,
        desktop: false
      },
      table: {
        pageSize: 20,
        showBorder: true,
        stripe: true
      }
    }
    saveSettingsToStorage()
    applyTheme('light')
  }

  const resetPreferences = () => {
    userPreferences.value = {
      sidebarCollapsed: false,
      favoriteFilters: [],
      recentSearches: [],
      customColumns: []
    }
    savePreferencesToStorage()
  }

  // 格式化系统信息
  const formatUptime = computed(() => {
    const uptime = systemStatus.value.uptime
    const days = Math.floor(uptime / (24 * 60 * 60))
    const hours = Math.floor((uptime % (24 * 60 * 60)) / (60 * 60))
    const minutes = Math.floor((uptime % (60 * 60)) / 60)
    
    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟`
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  })

  const formatMemoryUsage = computed(() => {
    const { used, total } = systemStatus.value.memory
    const usedMB = Math.round(used / 1024 / 1024)
    const totalMB = Math.round(total / 1024 / 1024)
    return `${usedMB}MB / ${totalMB}MB`
  })

  // AI助手控制
  const startAIAssistant = async () => {
    try {
      const response = await systemAPI.startAIAssistant()

      // 立即更新系统状态中的AI助手状态
      systemStatus.value.ai_assistant.status = 'online'
      systemStatus.value.ai_assistant.workers = 1

      // 延迟重新加载完整状态，确保服务完全启动
      setTimeout(async () => {
        await loadSystemStatus()
      }, 2000)

      return response.data
    } catch (err) {
      error.value = err.message || '启动AI助手失败'
      throw err
    }
  }

  const stopAIAssistant = async () => {
    try {
      const response = await systemAPI.stopAIAssistant()

      // 立即更新系统状态中的AI助手状态
      systemStatus.value.ai_assistant.status = 'offline'
      systemStatus.value.ai_assistant.workers = 0
      systemStatus.value.ai_assistant.queue_size = 0

      // 延迟重新加载完整状态，确保服务完全停止
      setTimeout(async () => {
        await loadSystemStatus()
      }, 2000)

      return response.data
    } catch (err) {
      error.value = err.message || '停止AI助手失败'
      throw err
    }
  }

  const getSystemStatus = async () => {
    await loadSystemStatus()
    return systemStatus.value
  }

  // 初始化
  const initialize = () => {
    loadSettingsFromStorage()
    loadPreferencesFromStorage()
    loadSystemStatus()
  }

  return {
    // 状态
    systemStatus,
    loading,
    error,
    lastUpdated,
    settings,
    userPreferences,
    
    // 计算属性
    isSystemHealthy,
    systemStatusColor,
    memoryUsageColor,
    aiAssistantStatusColor,
    formatUptime,
    formatMemoryUsage,
    
    // Actions
    loadSystemStatus,
    updateSettings,
    updateUserPreferences,
    toggleTheme,
    toggleSidebar,
    addFavoriteFilter,
    removeFavoriteFilter,
    addRecentSearch,
    clearRecentSearches,
    resetSettings,
    resetPreferences,
    startAIAssistant,
    stopAIAssistant,
    getSystemStatus,
    initialize
  }
})
