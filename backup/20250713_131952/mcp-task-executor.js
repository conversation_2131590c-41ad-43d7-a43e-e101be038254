/**
 * MCP任务执行器 - 基于成功经验的真实实现
 * 专门处理MCP架构的浏览器自动化任务
 */

const fs = require('fs').promises;
const path = require('path');

class MCPTaskExecutor {
    constructor(mcpClient, logger = console) {
        this.mcpClient = mcpClient;
        this.logger = logger;
        this.executionContext = {
            taskId: null,
            startTime: null,
            completedActions: [],
            screenshots: [],
            errors: []
        };
    }

    /**
     * 执行MCP任务 - 基于我成功完成下架任务的经验
     */
    async executeTask(task) {
        try {
            this.logger.info(`🎬 开始执行MCP任务: ${task.id}`);
            
            // 初始化执行上下文
            this.executionContext = {
                taskId: task.id,
                startTime: Date.now(),
                completedActions: [],
                screenshots: [],
                errors: []
            };

            const actions = task.data?.actions || [];
            let actionResults = [];

            // 逐步执行每个MCP操作
            for (let i = 0; i < actions.length; i++) {
                const action = actions[i];
                this.logger.info(`📋 执行步骤 ${i + 1}/${actions.length}: ${action.type}`);

                try {
                    const result = await this.executeMCPAction(action);
                    actionResults.push(result);
                    this.executionContext.completedActions.push({
                        action: action,
                        result: result,
                        timestamp: new Date().toISOString()
                    });

                    // 如果操作失败，记录错误但继续执行
                    if (!result.success) {
                        this.logger.warn(`⚠️ 步骤 ${i + 1} 执行失败: ${result.error}`);
                        this.executionContext.errors.push({
                            step: i + 1,
                            action: action.type,
                            error: result.error
                        });
                    }

                    // 在关键步骤后等待一下
                    if (action.type === 'mcp_click' || action.type === 'mcp_type') {
                        await this.wait(1000); // 等待1秒
                    }

                } catch (error) {
                    this.logger.error(`❌ 步骤 ${i + 1} 执行异常:`, error);
                    this.executionContext.errors.push({
                        step: i + 1,
                        action: action.type,
                        error: error.message
                    });
                    
                    // 严重错误时停止执行
                    if (action.critical !== false) {
                        throw error;
                    }
                }
            }

            // 生成执行报告
            const report = await this.generateExecutionReport();

            this.logger.info(`✅ MCP任务执行完成: ${task.id}`);
            
            return {
                success: true,
                taskId: task.id,
                executionTime: Date.now() - this.executionContext.startTime,
                completedActions: this.executionContext.completedActions.length,
                totalActions: actions.length,
                errors: this.executionContext.errors,
                report: report,
                actionResults: actionResults
            };

        } catch (error) {
            this.logger.error(`❌ MCP任务执行失败: ${task.id}`, error);
            
            return {
                success: false,
                taskId: task.id,
                error: error.message,
                executionTime: Date.now() - (this.executionContext.startTime || Date.now()),
                completedActions: this.executionContext.completedActions.length,
                errors: this.executionContext.errors
            };
        }
    }

    /**
     * 执行单个MCP操作
     */
    async executeMCPAction(action) {
        try {
            this.logger.info(`🔧 执行MCP操作: ${action.type} - ${action.description || ''}`);

            let result;

            switch (action.type) {
                case 'mcp_navigate':
                    result = await this.mcpClient.navigate(action.url);
                    break;

                case 'mcp_snapshot':
                    result = await this.mcpClient.getPageSnapshot();
                    break;

                case 'mcp_click':
                    result = await this.mcpClient.clickElement(
                        action.element,
                        action.ref
                    );
                    break;

                case 'mcp_type':
                    result = await this.mcpClient.typeText(
                        action.element,
                        action.ref,
                        action.text
                    );
                    break;

                case 'mcp_wait':
                    const waitTime = action.time || action.timeout || 3;
                    result = await this.mcpClient.wait(waitTime);
                    break;

                case 'mcp_screenshot':
                    const filename = `screenshot_${this.executionContext.taskId}_${Date.now()}.png`;
                    result = await this.mcpClient.takeScreenshot(filename);
                    
                    if (result.success) {
                        this.executionContext.screenshots.push({
                            filename: filename,
                            description: action.description,
                            timestamp: new Date().toISOString()
                        });
                    }
                    break;

                default:
                    throw new Error(`不支持的MCP操作类型: ${action.type}`);
            }

            if (result.success) {
                this.logger.info(`✅ MCP操作成功: ${action.type}`);
            } else {
                this.logger.warn(`⚠️ MCP操作失败: ${action.type} - ${result.error}`);
            }

            return result;

        } catch (error) {
            this.logger.error(`❌ MCP操作异常: ${action.type}`, error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 生成执行报告
     */
    async generateExecutionReport() {
        try {
            const totalActions = this.executionContext.completedActions.length;
            const successfulActions = this.executionContext.completedActions.filter(
                action => action.result.success
            ).length;
            const failedActions = totalActions - successfulActions;

            const report = {
                summary: `MCP任务执行完成，共执行${totalActions}个操作，成功${successfulActions}个，失败${failedActions}个`,
                executionTime: Date.now() - this.executionContext.startTime,
                statistics: {
                    totalActions: totalActions,
                    successfulActions: successfulActions,
                    failedActions: failedActions,
                    successRate: totalActions > 0 ? (successfulActions / totalActions * 100).toFixed(2) + '%' : '0%'
                },
                screenshots: this.executionContext.screenshots,
                errors: this.executionContext.errors,
                technology: {
                    architecture: 'MCP (Model Context Protocol)',
                    automation: 'Playwright MCP',
                    aiModel: 'qwen-turbo',
                    approach: '基于页面快照的精确元素定位'
                },
                details: this.executionContext.completedActions.map(action => ({
                    step: action.action.type,
                    description: action.action.description,
                    success: action.result.success,
                    timestamp: action.timestamp
                }))
            };

            return report;

        } catch (error) {
            this.logger.error('❌ 生成执行报告失败:', error);
            return {
                summary: 'MCP任务执行完成，但报告生成失败',
                error: error.message
            };
        }
    }

    /**
     * 等待指定时间
     */
    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 验证任务格式
     */
    validateTask(task) {
        if (!task || !task.id) {
            throw new Error('任务缺少必要的ID字段');
        }

        if (!task.data || !Array.isArray(task.data.actions)) {
            throw new Error('任务缺少有效的actions数组');
        }

        // 验证每个操作的格式
        task.data.actions.forEach((action, index) => {
            if (!action.type) {
                throw new Error(`操作 ${index + 1} 缺少type字段`);
            }

            if (!action.type.startsWith('mcp_')) {
                throw new Error(`操作 ${index + 1} 不是有效的MCP操作类型: ${action.type}`);
            }

            // 验证特定操作的必要字段
            if (action.type === 'mcp_navigate' && !action.url) {
                throw new Error(`操作 ${index + 1} (mcp_navigate) 缺少url字段`);
            }

            if ((action.type === 'mcp_click' || action.type === 'mcp_type') && (!action.element || !action.ref)) {
                throw new Error(`操作 ${index + 1} (${action.type}) 缺少element或ref字段`);
            }

            if (action.type === 'mcp_type' && !action.text) {
                throw new Error(`操作 ${index + 1} (mcp_type) 缺少text字段`);
            }
        });

        return true;
    }
}

module.exports = MCPTaskExecutor;
