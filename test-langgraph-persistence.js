/**
 * LangGraph数据库状态持久化测试
 * 测试工作流状态的保存和恢复机制
 */

require('dotenv').config();
const logger = require('./src/ai-assistant/src/utils/logger');
const WorkflowStateManager = require('./src/ai-assistant/src/langgraph/state/workflow-state-manager');
const dbConnection = require('./src/shared/database/connection');

class LangGraphPersistenceTest {
    constructor() {
        this.stateManager = new WorkflowStateManager();
    }
    
    async runTest() {
        try {
            logger.info('🧪 开始LangGraph数据库状态持久化测试...');
            
            // 0. 初始化数据库连接
            logger.info('🔌 初始化数据库连接...');
            await dbConnection.connect();
            await dbConnection.initializeSchema();
            
            // 1. 首先创建一个测试工单（满足外键约束）
            const ticketId = Math.floor(Math.random() * 1000000); // 使用数字ID
            logger.info(`📝 创建测试工单: ${ticketId}`);
            
            // 插入测试工单
            await dbConnection.run(
                `INSERT INTO tickets (id, ticket_number, title, content, status) 
                 VALUES (?, ?, ?, ?, ?)`,
                [ticketId, `TEST-${ticketId}`, '测试工单', '测试内容', '待开始']
            );
            
            // 2. 创建测试工作流状态
            const initialContext = {
                taskGoal: '测试工单处理',
                browserState: null,
                evidenceHistory: []
            };
            
            logger.info(`📝 创建测试工作流状态: ticket ${ticketId}`);
            const workflowId = await this.stateManager.createWorkflowState(ticketId, initialContext);
            logger.info(`✅ 工作流ID: ${workflowId}`);
            
            // 2. 测试状态更新
            logger.info('📊 测试状态更新...');
            await this.stateManager.updateWorkflowState(workflowId, {
                current_node: 'planning_node',
                cycle_count: 1,
                pdca_phase: 'plan'
            });
            
            // 3. 测试检查点保存
            logger.info('💾 测试检查点保存...');
            const checkpointData = {
                timestamp: new Date().toISOString(),
                state: {
                    currentPlan: { intent: 'navigate', confidence: 0.8 },
                    currentExecution: null,
                    currentValidation: null
                }
            };
            
            await this.stateManager.saveCheckpoint(workflowId, checkpointData, 'planning_node');
            
            // 4. 测试状态获取
            logger.info('🔍 测试状态获取...');
            const retrievedState = await this.stateManager.getWorkflowState(workflowId);
            logger.info('📄 检索到的状态:', JSON.stringify(retrievedState, null, 2));
            
            // 5. 测试挂起
            logger.info('⏸️ 测试工作流挂起...');
            await this.stateManager.suspendWorkflow(workflowId, '测试挂起');
            
            // 6. 测试恢复
            logger.info('▶️ 测试工作流恢复...');
            const resumedState = await this.stateManager.resumeWorkflow(workflowId);
            logger.info('📄 恢复后的状态:', JSON.stringify(resumedState, null, 2));
            
            // 7. 测试根据工单ID获取状态
            logger.info('🔎 测试根据工单ID获取状态...');
            const stateByTicket = await this.stateManager.getWorkflowStateByTicketId(ticketId);
            logger.info('📄 根据工单ID获取的状态:', JSON.stringify(stateByTicket, null, 2));
            
            // 8. 测试活跃工作流列表
            logger.info('📋 测试活跃工作流列表...');
            const activeWorkflows = await this.stateManager.getActiveWorkflows();
            logger.info(`📊 活跃工作流数量: ${activeWorkflows.length}`);
            
            // 9. 测试工作流统计
            logger.info('📈 测试工作流统计...');
            const stats = await this.stateManager.getWorkflowStats();
            logger.info('📊 工作流统计:', JSON.stringify(stats, null, 2));
            
            // 10. 测试完成工作流
            logger.info('🎉 测试工作流完成...');
            const finalResult = {
                success: true,
                completedAt: new Date().toISOString(),
                totalCycles: 3,
                executionTime: 15000
            };
            await this.stateManager.completeWorkflow(workflowId, finalResult);
            
            // 11. 验证完成状态
            const completedState = await this.stateManager.getWorkflowState(workflowId);
            logger.info('✅ 完成后的状态:', JSON.stringify(completedState, null, 2));
            
            // 12. 清理测试数据
            logger.info('🧹 清理测试数据...');
            await dbConnection.run('DELETE FROM tickets WHERE id = ?', [ticketId]);
            
            logger.info('🎉 LangGraph数据库状态持久化测试完成！');
            
            return {
                success: true,
                workflowId: workflowId,
                ticketId: ticketId,
                tests: [
                    '创建工作流状态',
                    '更新状态',
                    '保存检查点',
                    '获取状态',
                    '挂起工作流',
                    '恢复工作流',
                    '根据工单ID获取状态',
                    '获取活跃工作流',
                    '获取统计信息',
                    '完成工作流'
                ]
            };
            
        } catch (error) {
            logger.error('❌ 持久化测试失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    async cleanup() {
        // 这里可以添加清理逻辑
        logger.info('🧹 清理测试数据...');
    }
}

// 运行测试
if (require.main === module) {
    const test = new LangGraphPersistenceTest();
    test.runTest().then(result => {
        if (result.success) {
            logger.info('✅ 持久化测试执行成功');
            console.log('测试结果:', result);
        } else {
            logger.error('❌ 持久化测试执行失败');
            console.error('错误:', result.error);
        }
        return test.cleanup();
    }).then(() => {
        logger.info('测试完成');
        process.exit(0);
    }).catch(error => {
        logger.error('测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = LangGraphPersistenceTest;