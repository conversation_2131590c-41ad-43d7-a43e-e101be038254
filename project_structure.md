# 项目结构设计（v2.1）

## 1. 整体目录结构
```
project_RPA_langGraph/
├── src/                           # 源代码目录
│   ├── shared/                    # 共享模块
│   │   ├── config/               # 配置管理
│   │   ├── database/             # 数据库相关
│   │   │   ├── connection.js     # 数据库连接
│   │   │   ├── ensure-connected.js # 连接管理
│   │   │   └── models/          # 数据模型
│   │   ├── core/                # 核心共享组件
│   │   │   ├── ai-assistant.js  # AI助手共享接口
│   │   │   ├── message-queue.js # 消息队列
│   │   │   └── ticket-manager.js # 工单管理器
│   │   ├── utils/               # 工具函数
│   │   └── types/               # TypeScript类型定义
│   ├── workorder-system/        # 工单系统
│   │   ├── backend/             # 后端服务
│   │   │   ├── src/
│   │   │   │   ├── controllers/ # 控制器
│   │   │   │   │   ├── ticket-controller.js
│   │   │   │   │   ├── task-controller.js
│   │   │   │   │   └── system-controller.js
│   │   │   │   ├── services/    # 业务逻辑
│   │   │   │   │   └── ticket-service.js
│   │   │   │   ├── routes/      # 路由定义
│   │   │   │   │   ├── tickets.js
│   │   │   │   │   ├── tasks.js
│   │   │   │   │   └── system.js
│   │   │   │   ├── middleware/  # 中间件
│   │   │   │   │   └── upload.js
│   │   │   │   └── websocket/   # WebSocket处理
│   │   │   │       └── websocket-service.js
│   │   │   ├── package.json
│   │   │   └── server.js
│   │   └── frontend/            # 前端应用 (Vue 3)
│   │       ├── src/
│   │       │   ├── components/  # Vue组件
│   │       │   │   ├── AIAssistantPanel.vue
│   │       │   │   ├── TicketFormDialog.vue
│   │       │   │   ├── TicketDetailDialog.vue
│   │       │   │   └── TaskProgress.vue
│   │       │   ├── views/       # 页面视图
│   │       │   │   ├── TicketList.vue
│   │       │   │   ├── TicketListNew.vue
│   │       │   │   └── TicketDetail.vue
│   │       │   ├── store/       # Pinia状态管理
│   │       │   │   ├── tickets.js
│   │       │   │   ├── system.js
│   │       │   │   └── websocket.js
│   │       │   ├── api/         # API接口
│   │       │   │   ├── tickets.js
│   │       │   │   ├── system.js
│   │       │   │   └── request.js
│   │       │   ├── router/      # Vue Router
│   │       │   │   └── index.js
│   │       │   ├── App.vue
│   │       │   └── main.js
│   │       ├── package.json
│   │       └── vite.config.js
│   ├── ai-assistant/            # AI助手系统 (v2.1多Agent架构)
│   │   ├── src/
│   │   │   ├── agents/          # 多Agent系统
│   │   │   │   ├── agent-base.js           # Agent基类
│   │   │   │   ├── orchestrator-agent.js   # 总控Agent
│   │   │   │   ├── observer-agent.js       # 观察Agent  
│   │   │   │   ├── decision-agent.js       # 决策Agent
│   │   │   │   ├── executor-agent.js       # 执行Agent
│   │   │   │   └── validator-agent.js      # 验证Agent
│   │   │   ├── core/            # 核心组件
│   │   │   │   ├── agent-coordinator.js    # Agent协调器
│   │   │   │   ├── ai-task-analyzer.js     # AI任务分析器
│   │   │   │   ├── intelligent-executor.js # 智能执行器
│   │   │   │   ├── execution-manager.js    # 执行管理器
│   │   │   │   └── error-recovery-engine.js # 错误恢复引擎
│   │   │   ├── langgraph/       # LangGraph工作流
│   │   │   │   ├── workflows/   # 工作流定义
│   │   │   │   │   └── rpa-workflow.js
│   │   │   │   ├── state/       # 状态管理
│   │   │   │   │   ├── workflow-state.js
│   │   │   │   │   └── workflow-state-manager.js
│   │   │   │   ├── nodes/       # 节点实现
│   │   │   │   │   ├── core-control-nodes.js
│   │   │   │   │   ├── pdca-execution-nodes.js
│   │   │   │   │   ├── multi-agent-execution-nodes.js
│   │   │   │   │   ├── intelligent-validation-engine.js
│   │   │   │   │   ├── adaptive-prompt-system.js
│   │   │   │   │   └── edge-case-handler.js
│   │   │   │   ├── processors/  # 处理器
│   │   │   │   │   ├── langgraph-task-processor.js
│   │   │   │   │   └── multi-agent-langgraph-processor.js
│   │   │   │   └── utils/       # 工具函数
│   │   │   ├── playwright-mcp/  # MCP客户端
│   │   │   │   ├── enhanced-simple-mcp-client.js
│   │   │   │   └── simple-mcp-client.js
│   │   │   ├── pdca/           # PDCA循环组件
│   │   │   │   ├── simple-llm-client.js    # LLM客户端
│   │   │   │   ├── checker-agent.js
│   │   │   │   ├── executor-agent.js
│   │   │   │   ├── planner-agent.js
│   │   │   │   └── pdca-cycle-manager.js
│   │   │   ├── monitoring/     # 性能监控
│   │   │   │   └── performance-monitor.js
│   │   │   ├── utils/          # 工具函数
│   │   │   │   └── logger.js
│   │   │   ├── enhanced-ai-controller.js   # 增强AI控制器
│   │   │   ├── task-parser.js             # 任务解析器
│   │   │   └── task-processor.js          # 任务处理器
│   │   ├── prompts/            # 提示词体系
│   │   │   ├── agents/         # Agent专属提示词
│   │   │   │   ├── orchestrator-prompt.md
│   │   │   │   ├── observer-prompt.md
│   │   │   │   ├── decision-prompt.md
│   │   │   │   ├── executor-prompt.md
│   │   │   │   └── validator-prompt.md
│   │   │   ├── shared/         # 共享提示词
│   │   │   │   ├── operation-patterns.md
│   │   │   │   ├── system-capabilities.md
│   │   │   │   └── error-handling.md
│   │   │   └── templates/      # 模板文件
│   │   │       ├── task-analysis.md
│   │   │       └── report-generation.md
│   │   ├── data/              # 数据存储
│   │   │   ├── logs/          # 日志文件
│   │   │   └── rpa.db         # SQLite数据库
│   │   ├── package.json
│   │   └── server.js
│   └── prompt/                 # 通用提示词模板
│       ├── main_model.md      # 主模型提示词
│       └── vl_model.md        # 视觉模型提示词
├── config/                     # 配置文件
│   └── database.sql           # 数据库初始化脚本
├── test-*.js                  # 测试文件 (精简后14个)
│   ├── test-system-basic.js
│   ├── test-simple-llm-client.js
│   ├── test-api-key.js
│   ├── test-langgraph-suspend-resume.js
│   ├── test-langgraph-persistence.js
│   ├── test-simple-langgraph.js
│   ├── test-multi-agent-e2e.js
│   ├── test-multi-agent-complete.js
│   ├── test-three-layer-validation.js
│   ├── test-mcp-integration.js
│   ├── test-fixes-validation.js
│   ├── test-optimization-features.js
│   ├── test-real-e2e.js
│   └── test-complete-workflow.js
├── docs/                      # 文档目录
│   ├── CLAUDE.md             # 开发指南
│   ├── README.md             # 项目介绍
│   ├── PRD.md                # 产品需求文档
│   ├── architecture.md       # 技术架构文档
│   ├── database_design.md    # 数据库设计文档
│   ├── system_guide.md       # 系统操作指南
│   ├── project_structure.md  # 项目结构说明
│   └── AI_RPA_CORE_PRINCIPLES.md # 核心原则
├── .env.example              # 环境变量示例
├── package.json              # 主项目配置
└── create-test-ticket.js     # 测试工单创建脚本
```

## 2. 核心架构特点

### 2.1 多Agent协同架构
- **分层设计**: 工单系统 + AI助手系统 + 目标管理后台
- **智能协作**: 5个专门化Agent分工合作
- **零硬编码**: 所有业务逻辑由AI动态生成

### 2.2 状态管理机制
- **LangGraph工作流**: 专业级状态机管理
- **SQLite持久化**: 支持断点续传和状态恢复
- **WebSocket同步**: 实时状态更新

### 2.3 验证保障体系
- **三层验证**: L1基础层 + L2功能层 + L3智能层
- **自适应优化**: 基于历史数据动态优化
- **错误恢复**: 智能错误分类和自动恢复

## 3. 技术特色

### 3.1 AI驱动
- 完全由AI理解和执行任务
- 自适应不同页面和业务流程
- 智能识别需要人工协助的场景

### 3.2 工作流管理
- 基于LangGraph的节点化流程控制
- PDCA循环确保任务质量
- 支持挂起恢复和并发执行

### 3.3 模块化设计
- 清晰的分层架构
- 组件间低耦合高内聚
- 易于扩展和维护

## 4. 数据流向

```
用户创建工单 → 工单系统入库 → AI助手接收
      ↓
多Agent协同分析 → LangGraph工作流编排 → 浏览器操作执行
      ↓
三层验证确认 → 状态实时同步 → 结果反馈用户
```

## 5. 部署结构

### 5.1 开发环境
- 工单后端: localhost:3001
- 工单前端: localhost:3000  
- AI助手: localhost:3002
- WebSocket: localhost:3003

### 5.2 生产环境
- 支持Docker容器化部署
- 支持分布式部署
- 支持负载均衡配置

---

**版本**: v2.1 多Agent架构  
**最后更新**: 2025-01-14