{"name": "rpa-automation-platform", "version": "1.0.0", "description": "RPA自动化平台 - 通过AI助手实现管理后台操作的自动化", "main": "index.js", "scripts": {"install:all": "npm install && npm run install:workorder && npm run install:ai", "install:workorder": "cd src/workorder-system/backend && npm install && cd ../frontend && npm install", "install:ai": "cd src/ai-assistant && npm install", "dev": "concurrently \"npm run dev:workorder-backend\" \"npm run dev:workorder-frontend\" \"npm run dev:ai-assistant\"", "dev:workorder-backend": "cd src/workorder-system/backend && npm run dev", "dev:workorder-frontend": "cd src/workorder-system/frontend && npm run dev", "dev:ai-assistant": "cd src/ai-assistant && npm run dev", "build": "npm run build:workorder && npm run build:ai", "build:workorder": "cd src/workorder-system/frontend && npm run build", "build:ai": "cd src/ai-assistant && npm run build", "start": "concurrently \"npm run start:workorder\" \"npm run start:ai\"", "start:workorder": "cd src/workorder-system/backend && npm start", "start:ai": "cd src/ai-assistant && npm start", "test": "npm run test:critical && npm run test:core", "test:critical": "npm run test:p0", "test:core": "npm run test:p1", "test:enhanced": "npm run test:p2", "test:p0": "node test-simple-langgraph.js && node test-langgraph-suspend-resume.js", "test:p1": "node test-multi-agent-e2e.js && node test-multi-agent-complete.js && node test-three-layer-validation.js", "test:p2": "node test-real-e2e.js && node test-complete-workflow.js && node test-optimization-features.js", "test:unit": "node test-system-basic.js && node test-simple-llm-client.js && node test-api-key.js", "test:integration": "node test-mcp-integration.js && node test-fixes-validation.js", "test:e2e": "node test-real-e2e.js && node test-complete-workflow.js", "test:watch": "npm run test:critical && echo 'Watching for changes...'", "test:coverage": "echo 'Coverage reporting not yet implemented'", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e && npm run test:p0 && npm run test:p1 && npm run test:p2", "test:workorder": "cd src/workorder-system && npm test", "test:ai": "cd src/ai-assistant && npm test", "setup": "node scripts/setup.js", "db:init": "node scripts/init-database.js", "db:seed": "node scripts/seed-database.js"}, "keywords": ["rpa", "automation", "ai", "workflow", "playwright", "vue", "nodejs"], "author": "RPA Team", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0", "eslint": "^8.57.0", "prettier": "^3.2.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/rpa-automation-platform.git"}, "bugs": {"url": "https://github.com/your-org/rpa-automation-platform/issues"}, "homepage": "https://github.com/your-org/rpa-automation-platform#readme", "dependencies": {"@langchain/anthropic": "^0.3.24", "@langchain/core": "^0.3.62", "@langchain/google-genai": "^0.2.14", "@langchain/langgraph": "^0.3.7", "@langchain/openai": "^0.5.18", "@vueup/vue-quill": "^1.2.0", "axios": "^1.10.0", "dotenv": "^17.2.0", "eventsource": "^4.0.0", "langchain": "^0.3.29", "openai": "^5.9.0", "playwright": "^1.40.0", "quill": "^2.0.3", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "ws": "^8.18.3"}}