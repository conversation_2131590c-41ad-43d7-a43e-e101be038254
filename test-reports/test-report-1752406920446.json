{"suiteName": "单元测试", "timestamp": "2025-07-13T11:42:00.445Z", "summary": {"total": 1, "passed": 0, "failed": 1, "errors": 0, "duration": 1}, "performance": {"avgDuration": 1, "slowest": {"name": "OrchestratorAgent基础功能测试", "duration": 1}, "peakMemory": 0}, "results": [{"name": "OrchestratorAgent基础功能测试", "status": "failed", "duration": 1, "memory": {"start": {"rss": 63389696, "heapTotal": 12877824, "heapUsed": 9336016, "external": 3293373, "arrayBuffers": 71167}, "end": {"rss": 63537152, "heapTotal": 12877824, "heapUsed": 9375736, "external": 3293413, "arrayBuffers": 71167}, "delta": {"heapUsed": 39720, "external": 40}}, "customMetrics": {}, "error": {"message": "断言失败: Agent名称应该是OrchestratorAgent", "stack": "Error: 断言失败: Agent名称应该是OrchestratorAgent\n    at TestOrchestratorAgent.assert (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/base-test.js:82:19)\n    at TestOrchestratorAgent.execute (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/unit/agents/test-orchestrator-agent.js:16:14)\n    at TestOrchestratorAgent.run (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/base-test.js:50:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TestRunner.runTest (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/test-runner.js:70:20)\n    at async TestRunner.runSequential (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/test-runner.js:51:28)\n    at async TestRunner.runSuite (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/test-runner.js:35:13)\n    at async runAllTests (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/run-all-tests.js:31:9)"}}]}