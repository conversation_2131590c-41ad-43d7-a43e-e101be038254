# AI驱动RPA项目核心原则

## 🎯 项目本质

这是一个**AI驱动**的RPA系统，不是传统的脚本自动化工具。

### 核心理念
1. **AI理解任务** - 理解工单的意图和目标
2. **AI感知状态** - 通过snapshot获取当前页面的结构化信息
3. **AI决策行动** - 根据当前状态和目标，动态决定下一步
4. **AI验证结果** - 判断任务是否完成，是否需要调整策略

## ❌ 错误方向（必须避免）

### 1. 死板的步骤执行
```javascript
// ❌ 错误：预定义11个步骤机械执行
for (let i = 0; i < 11; i++) {
    executeStep(steps[i]);
}
```

### 2. 固定的选择器
```javascript
// ❌ 错误：硬编码选择器
await click("input[placeholder*='门店']");
```

## ✅ 正确方向

### 1. 动态感知与决策
```javascript
// ✅ 正确：AI根据页面状态决策
const pageState = await getSnapshot();
const nextAction = await AI.decideNextAction(pageState, goal);
```

### 2. PDCA循环
- **Plan**: AI分析当前状态，规划下一步
- **Do**: 执行操作
- **Check**: 获取新状态，验证操作效果
- **Act**: 根据结果调整策略或继续

### 3. 状态驱动而非步骤驱动
不是"我在第3步"，而是"我在哪个页面，看到了什么，接下来该做什么"

## 🔧 技术要点

### 1. Snapshot的正确使用
```javascript
const snapshot = await mcp.snapshot();
// snapshot包含：
// - url: 当前页面URL
// - title: 页面标题
// - elements: 结构化的页面元素信息
//   - buttons: 所有按钮
//   - inputs: 所有输入框
//   - links: 所有链接
//   - text: 页面文本
```

### 2. AI决策流程
```javascript
async function makeDecision(snapshot, goal) {
    const prompt = `
当前页面状态：
URL: ${snapshot.url}
标题: ${snapshot.title}
可用元素：
- 按钮: ${Object.keys(snapshot.elements.buttons).join(', ')}
- 输入框: ${Object.keys(snapshot.elements.inputs).join(', ')}

任务目标：${goal}

请分析：
1. 我现在在哪个页面/步骤？
2. 接下来应该做什么？
3. 具体的操作是什么？

返回JSON格式的下一步操作。
`;
    
    return await llm.chat(prompt);
}
```

### 3. 视觉验证（需要时）
```javascript
// 当需要视觉确认时，使用VL模型
const screenshot = await mcp.screenshot();
const visualAnalysis = await vlModel.analyze(screenshot, "这个页面是否显示商品列表？");
```

## 📋 正确的执行流程

1. **理解任务**
   - 提取关键信息（门店名、商品名、操作类型）
   
2. **初始导航**
   - 打开目标网站
   
3. **循环执行直到完成**
   ```
   while (!taskCompleted) {
       // 获取当前状态
       snapshot = getSnapshot()
       
       // AI决策
       nextAction = AI.decide(snapshot, goal, history)
       
       // 执行操作
       result = execute(nextAction)
       
       // 更新历史
       history.add(action, result)
       
       // 检查完成条件
       taskCompleted = AI.checkCompletion(snapshot, goal)
   }
   ```

## 🚫 常见陷阱

1. **过度依赖固定步骤** - 网页可能改版，流程可能变化
2. **忽视页面状态** - 不看当前在哪就盲目执行
3. **缺少错误恢复** - 遇到问题就失败，而不是尝试其他方法
4. **验证不充分** - 只看操作是否执行，不看是否达到目标

## 💡 关键洞察

1. **系统指南(system_guide.md)是参考，不是脚本** - AI应该理解指南的意图，而不是逐字执行
2. **每一步都要验证** - 不是点击了就成功，而是达到预期状态才成功
3. **保持灵活性** - 同样的任务可能有多种完成路径

## 🎯 成功标准

任务成功的标准不是"执行了11个步骤"，而是：
- 找到了目标商品
- 商品状态从"下架"变为"上架"
- 系统显示操作成功的提示

## 📝 实现检查清单

- [ ] AI能根据snapshot理解当前页面状态
- [ ] AI能根据目标和当前状态决定下一步
- [ ] 系统能处理意外情况（如弹窗、加载慢等）
- [ ] 有明确的任务完成判断标准
- [ ] 操作历史可追溯，便于调试

记住：我们在构建一个**智能助手**，而不是一个**脚本执行器**！