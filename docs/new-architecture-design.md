# RPA系统重构架构设计

## 核心流程

### 1. 工单状态流转
```
待开始 → 排队中 → 处理中 → 已完成/待补充信息
```

### 2. 详细流程

#### 2.1 工单创建
- 用户创建工单，状态设为「待开始」
- 工单包含：标题、内容、优先级、创建时间

#### 2.2 队列管理
- 消息队列监控「待开始」状态的工单
- 按优先级（高→中→低）和创建时间（早→晚）排序
- 队列有空位时，自动取工单进入队列
- 工单状态更新为「排队中」

#### 2.3 AI助手处理
1. **获取工单**：从队列获取工单，状态设为「处理中」
2. **内容分析**：
   - 根据工单内容匹配系统指引
   - 判断信息是否完整
3. **分支处理**：
   - **信息完整**：生成任务清单 → 调用Playwright MCP执行 → 生成报告 → 状态「已完成」
   - **信息不完整**：生成补充信息要求 → 状态「待补充信息」

#### 2.4 任务执行
- AI助手调用Playwright MCP进行浏览器操作
- 实时更新任务清单进度
- 关键步骤截图保存
- 生成富文本总结报告

## 系统组件

### 1. 工单管理器 (TicketManager)
```javascript
class TicketManager {
  // 创建工单
  createTicket(title, content, priority)
  
  // 更新工单状态
  updateTicketStatus(ticketId, status, data)
  
  // 获取工单
  getTicket(ticketId)
  
  // 更新任务清单
  updateTaskList(ticketId, taskList)
  
  // 更新进度
  updateProgress(ticketId, progress)
}
```

### 2. 消息队列 (MessageQueue)
```javascript
class MessageQueue {
  // 添加工单到队列
  enqueue(ticketId)
  
  // 从队列获取工单
  dequeue()
  
  // 检查队列状态
  getQueueStatus()
  
  // 自动队列管理
  autoManageQueue()
}
```

### 3. AI助手核心 (AIAssistant)
```javascript
class AIAssistant {
  // 处理工单
  processTicket(ticketId)
  
  // 分析工单内容
  analyzeTicketContent(content)
  
  // 生成任务清单
  generateTaskList(content, guide)
  
  // 执行任务
  executeTask(task)
  
  // 调用Playwright MCP
  callPlaywrightMCP(action, params)
  
  // 生成报告
  generateReport(ticketId, results)
}
```

### 4. Playwright MCP集成
```javascript
class PlaywrightMCPClient {
  // 导航到页面
  navigate(url)
  
  // 点击元素
  click(selector)
  
  // 输入文本
  type(selector, text)
  
  // 截图
  screenshot(description)
  
  // 等待元素
  waitFor(selector)
}
```

## 数据库设计

### 工单表 (tickets)
```sql
CREATE TABLE tickets (
  id INTEGER PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  priority TEXT DEFAULT 'medium', -- high, medium, low
  status TEXT DEFAULT 'pending', -- pending, queued, processing, completed, need_info
  task_list TEXT, -- JSON格式的任务清单
  progress INTEGER DEFAULT 0, -- 进度百分比
  report TEXT, -- 富文本报告
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 队列表 (queue)
```sql
CREATE TABLE queue (
  id INTEGER PRIMARY KEY,
  ticket_id INTEGER REFERENCES tickets(id),
  priority_score INTEGER, -- 优先级分数
  queued_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 截图表 (screenshots)
```sql
CREATE TABLE screenshots (
  id INTEGER PRIMARY KEY,
  ticket_id INTEGER REFERENCES tickets(id),
  file_path TEXT NOT NULL,
  description TEXT,
  step_index INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 系统指引匹配

### 指引类型
1. **商品下架指引** - 匹配关键词：下架、商品、门店
2. **商品上架指引** - 匹配关键词：上架、商品、发布
3. **门店信息更新指引** - 匹配关键词：门店、信息、更新
4. **通用浏览器操作指引** - 默认指引

### 信息完整性检查
每个指引定义必需信息：
- 商品下架：门店名称、商品名称
- 商品上架：门店名称、商品信息、价格
- 门店信息更新：门店名称、更新内容

## 实现优先级

1. **第一阶段**：基础架构
   - 重构工单状态管理
   - 实现简单消息队列
   - 简化AI助手核心逻辑

2. **第二阶段**：MCP集成
   - 集成Playwright MCP
   - 实现截图功能
   - 任务执行引擎

3. **第三阶段**：完善功能
   - 进度跟踪
   - 报告生成
   - 错误处理

## 关键改进点

1. **简化架构**：移除不必要的抽象层
2. **清晰状态**：明确的工单状态流转
3. **直接集成**：AI助手直接调用Playwright MCP
4. **实时反馈**：实时更新进度和状态
5. **富文本报告**：包含截图的详细报告
