/**
 * LLM连接测试脚本
 * 验证阿里云百炼API的连通性和配置正确性
 */

require('dotenv').config();
const SimpleLLMClient = require('./src/ai-assistant/src/pdca/simple-llm-client');

async function testLLMConnection() {
    console.log('🧪 开始LLM连接测试...\n');
    
    // 显示当前配置
    console.log('📋 当前配置:');
    console.log(`  API Key: ${process.env.DASHSCOPE_API_KEY ? process.env.DASHSCOPE_API_KEY.substring(0, 10) + '...' : '未配置'}`);
    console.log(`  Base URL: ${process.env.DASHSCOPE_BASE_URL || '未配置'}`);
    console.log(`  Model: ${process.env.MAIN_MODEL || '未配置'}\n`);
    
    try {
        // 创建LLM客户端
        const llmClient = new SimpleLLMClient();
        
        // 测试1: 基础连通性测试
        console.log('🔗 测试1: API连通性测试');
        const connectionResult = await llmClient.testConnection();
        
        if (connectionResult.success) {
            console.log('✅ API连接成功');
            console.log(`📝 模型响应: ${connectionResult.response.substring(0, 100)}...`);
            console.log(`📊 Token使用: ${JSON.stringify(connectionResult.usage)}\n`);
        } else {
            console.log('❌ API连接失败');
            console.log(`💥 错误信息: ${connectionResult.error}\n`);
            return false;
        }
        
        // 测试2: 标准chat方法测试
        console.log('🔗 测试2: 标准chat方法测试');
        const chatResponse = await llmClient.chat('请简单介绍一下你自己');
        
        if (chatResponse && chatResponse.length > 0) {
            console.log('✅ Chat方法工作正常');
            console.log(`📝 响应: ${chatResponse.substring(0, 100)}...\n`);
        } else {
            console.log('❌ Chat方法失败\n');
            return false;
        }
        
        // 测试3: 健康检查测试
        console.log('🔗 测试3: 健康检查测试');
        const healthCheck = await llmClient.checkHealth();
        
        if (healthCheck) {
            console.log('✅ 健康检查通过\n');
        } else {
            console.log('❌ 健康检查失败\n');
            return false;
        }
        
        // 测试4: 错误处理测试
        console.log('🔗 测试4: 错误处理测试');
        try {
            // 创建一个有问题的客户端来测试错误处理
            const badClient = new SimpleLLMClient();
            badClient.apiKey = 'invalid-key';
            badClient.openai = require('openai')({
                apiKey: 'invalid-key',
                baseURL: badClient.baseURL
            });
            
            await badClient.testConnection();
            console.log('⚠️ 错误处理测试: 应该失败但没有失败');
        } catch (error) {
            console.log('✅ 错误处理正常工作');
            console.log(`📝 错误信息: ${error.message.substring(0, 50)}...\n`);
        }
        
        // 测试5: AI-First原则验证
        console.log('🔗 测试5: AI-First原则验证');
        const aiFirstPrompts = [
            '分析这个业务场景：上架榮記豆腐麵食的外卖商品菜遠牛肉飯',
            '制定操作计划：如何在电商平台添加新商品',
            '验证结果：确认商品上架是否成功'
        ];
        
        let aiFirstTestsPassed = 0;
        for (const prompt of aiFirstPrompts) {
            try {
                const response = await llmClient.chat(prompt);
                if (response && response.length > 20) { // 基本响应长度检查
                    aiFirstTestsPassed++;
                    console.log(`  ✓ AI-First测试通过: ${prompt.substring(0, 30)}...`);
                } else {
                    console.log(`  ✗ AI-First测试失败: ${prompt.substring(0, 30)}...`);
                }
            } catch (error) {
                console.log(`  ✗ AI-First测试错误: ${prompt.substring(0, 30)}... - ${error.message}`);
            }
        }
        
        if (aiFirstTestsPassed >= 2) { // 至少2/3通过
            console.log('✅ AI-First原则验证通过\n');
        } else {
            console.log('❌ AI-First原则验证失败\n');
            return false;
        }
        
        // 显示统计信息
        const stats = llmClient.getStats();
        console.log('📊 测试统计:');
        console.log(`  请求次数: ${stats.requestCount}`);
        console.log(`  错误次数: ${stats.errorCount}`);
        console.log(`  成功率: ${(stats.successRate * 100).toFixed(1)}%`);
        console.log(`  当前提供商: ${stats.currentProvider}`);
        
        console.log('\n🎉 所有LLM连接测试通过！');
        console.log('✨ 系统已准备好进行AI-First RPA测试');
        
        return true;
        
    } catch (error) {
        console.error('💥 LLM连接测试失败:', error);
        console.error('🔧 请检查以下配置:');
        console.error('  1. DASHSCOPE_API_KEY 是否正确');
        console.error('  2. DASHSCOPE_BASE_URL 是否可访问');
        console.error('  3. 网络连接是否正常');
        console.error('  4. API配额是否充足');
        
        return false;
    }
}

// 运行测试
if (require.main === module) {
    testLLMConnection()
        .then(success => {
            if (success) {
                console.log('\n✅ LLM连接验证完成，可以继续进行AI-First测试');
                process.exit(0);
            } else {
                console.log('\n❌ LLM连接验证失败，请修复配置后重试');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('\n💥 测试执行异常:', error);
            process.exit(1);
        });
}

module.exports = testLLMConnection;
