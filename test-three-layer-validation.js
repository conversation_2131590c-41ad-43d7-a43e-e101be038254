/**
 * 三层智能验证机制综合测试
 * 测试L1基础层、L2功能层、L3智能层的完整验证体系
 */

require('dotenv').config();
const logger = require('./src/ai-assistant/src/utils/logger');
const IntelligentValidationEngine = require('./src/ai-assistant/src/langgraph/nodes/intelligent-validation-engine');
const AdaptivePromptSystem = require('./src/ai-assistant/src/langgraph/nodes/adaptive-prompt-system');
const EdgeCaseHandler = require('./src/ai-assistant/src/langgraph/nodes/edge-case-handler');

// 模拟依赖
class MockLLMClient {
    async chat(prompt) {
        // 模拟不同复杂度的LLM响应
        const responses = {
            L2_FUNCTIONAL: `{
                "goalRelevance": 0.85,
                "stateConsistency": 0.90,
                "sideEffectCheck": 0.95,
                "progressAssessment": 0.75,
                "score": 0.86,
                "reason": "操作成功执行，符合预期目标",
                "suggestions": "建议继续当前策略"
            }`,
            L3_INTELLIGENT: `{
                "completionAssessment": 0.70,
                "pathOptimality": 0.80,
                "contextCoherence": 0.88,
                "completionProbability": 0.82,
                "score": 0.80,
                "reason": "任务进展良好，上下文连贯",
                "nextStepRecommendation": "CONTINUE",
                "strategicAdvice": "保持当前执行策略"
            }`
        };
        
        if (prompt.includes('L2功能层验证')) {
            return responses.L2_FUNCTIONAL;
        } else if (prompt.includes('L3智能层验证')) {
            return responses.L3_INTELLIGENT;
        }
        
        // 默认规划响应
        return `{
            "intent": "CLICK",
            "parameters": {"element": "按钮", "ref": "submit-btn"},
            "reasoning": "点击提交按钮完成操作",
            "confidence": 0.8
        }`;
    }
}

class MockMCPClient {
    async callTool(toolName, params) {
        // 模拟不同工具的响应
        switch (toolName) {
            case 'browser_navigate':
                await this.sleep(1000);
                return { success: true, url: params.url };
                
            case 'browser_click':
                await this.sleep(500);
                return { success: true, element: params.element };
                
            case 'browser_type':
                await this.sleep(300);
                return { success: true, text: params.text };
                
            case 'browser_screenshot':
                await this.sleep(200);
                return 'iVBORw0KGgoAAAANSUhEUgAAA...'; // Base64截图数据
                
            case 'browser_snapshot':
                await this.sleep(100);
                return '<html><body><div class="content">页面内容</div></body></html>';
                
            default:
                return { success: true };
        }
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 三层验证机制综合测试类
class ThreeLayerValidationTest {
    constructor() {
        this.llmClient = new MockLLMClient();
        this.mcpClient = new MockMCPClient();
        
        // 初始化测试组件
        this.validationEngine = new IntelligentValidationEngine({
            llmClient: this.llmClient,
            mcpClient: this.mcpClient
        });
        
        this.promptSystem = new AdaptivePromptSystem();
        this.edgeHandler = new EdgeCaseHandler({
            llmClient: this.llmClient,
            mcpClient: this.mcpClient
        });
        
        this.testResults = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            testDetails: []
        };
    }
    
    /**
     * 运行完整的三层验证测试套件
     */
    async runComprehensiveTest() {
        try {
            logger.info('🧪 开始三层智能验证机制综合测试');
            
            console.log('\n=== 三层智能验证机制综合测试 ===\n');
            
            // 测试套件
            await this.testL1BasicValidation();
            await this.testL2FunctionalValidation();
            await this.testL3IntelligentValidation();
            await this.testThreeLayerIntegration();
            await this.testAdaptivePromptSystem();
            await this.testEdgeCaseHandling();
            await this.testPerformanceAndScalability();
            
            // 输出测试结果
            this.outputTestSummary();
            
        } catch (error) {
            logger.error('综合测试执行失败:', error);
            throw error;
        }
    }
    
    /**
     * 测试L1基础层验证
     */
    async testL1BasicValidation() {
        console.log('📋 测试L1基础层验证...');
        
        const testCases = [
            {
                name: '技术成功执行',
                evidence: { success: true, duration: 1000 },
                expectedPass: true
            },
            {
                name: '技术执行失败',
                evidence: { success: false, error: { message: '网络错误' }, duration: 5000 },
                expectedPass: false
            },
            {
                name: '执行超时',
                evidence: { success: true, duration: 35000 },
                expectedPass: false
            }
        ];
        
        for (const testCase of testCases) {
            try {
                const result = await this.validationEngine.performL1BasicValidation(
                    { intent: 'CLICK', parameters: {} },
                    testCase.evidence,
                    { cycleCount: 1 }
                );
                
                const passed = result.passed === testCase.expectedPass;
                this.recordTestResult(`L1-${testCase.name}`, passed, {
                    expected: testCase.expectedPass,
                    actual: result.passed,
                    score: result.score,
                    reason: result.reason
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${testCase.name}: ${result.passed ? '通过' : '失败'} (分数: ${result.score})`);
                
            } catch (error) {
                this.recordTestResult(`L1-${testCase.name}`, false, { error: error.message });
                console.log(`  ❌ ${testCase.name}: 测试异常 - ${error.message}`);
            }
        }
    }
    
    /**
     * 测试L2功能层验证
     */
    async testL2FunctionalValidation() {
        console.log('\n📋 测试L2功能层验证...');
        
        const testCases = [
            {
                name: '高质量业务操作',
                plan: { intent: 'CLICK', parameters: { element: '提交按钮' }, reasoning: '完成表单提交' },
                evidence: { success: true, executionResult: { action: 'click' } },
                state: { taskGoal: '提交订单表单', cycleCount: 1 }
            },
            {
                name: '低质量业务操作',
                plan: { intent: 'WAIT', parameters: { duration: 1000 }, reasoning: '无明确目的等待' },
                evidence: { success: true, executionResult: { action: 'wait' } },
                state: { taskGoal: '完成支付流程', cycleCount: 3 }
            }
        ];
        
        for (const testCase of testCases) {
            try {
                const result = await this.validationEngine.performL2FunctionalValidation(
                    testCase.plan,
                    testCase.evidence,
                    testCase.state
                );
                
                const passed = result.score >= 0.5; // 基本通过标准
                this.recordTestResult(`L2-${testCase.name}`, passed, {
                    score: result.score,
                    analysis: result.analysis,
                    reason: result.reason
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${testCase.name}: 分数 ${result.score.toFixed(2)} - ${result.reason}`);
                
            } catch (error) {
                this.recordTestResult(`L2-${testCase.name}`, false, { error: error.message });
                console.log(`  ❌ ${testCase.name}: 测试异常 - ${error.message}`);
            }
        }
    }
    
    /**
     * 测试L3智能层验证
     */
    async testL3IntelligentValidation() {
        console.log('\n📋 测试L3智能层验证...');
        
        const testCases = [
            {
                name: '复杂上下文分析',
                plan: { intent: 'CLICK', parameters: {}, reasoning: '基于历史执行的智能决策' },
                evidence: { success: true, executionResult: { action: 'click' } },
                state: {
                    taskGoal: '完成多步骤业务流程',
                    cycleCount: 2,
                    maxCycles: 5,
                    executionContext: {
                        evidenceHistory: [
                            { plan: { intent: 'NAVIGATE' }, evidence: { success: true } },
                            { plan: { intent: 'TYPE' }, evidence: { success: true } }
                        ]
                    }
                }
            },
            {
                name: '简单任务分析',
                plan: { intent: 'SCREENSHOT', parameters: {}, reasoning: '截图验证' },
                evidence: { success: true, executionResult: { action: 'screenshot' } },
                state: {
                    taskGoal: '截图保存',
                    cycleCount: 1,
                    maxCycles: 2,
                    executionContext: { evidenceHistory: [] }
                }
            }
        ];
        
        for (const testCase of testCases) {
            try {
                const result = await this.validationEngine.performL3IntelligentValidation(
                    testCase.plan,
                    testCase.evidence,
                    testCase.state
                );
                
                const passed = result.score >= 0.6; // L3层较高标准
                this.recordTestResult(`L3-${testCase.name}`, passed, {
                    score: result.score,
                    analysis: result.analysis,
                    completionAssessment: result.completionAssessment,
                    recommendation: result.nextStepRecommendation
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${testCase.name}: 分数 ${result.score.toFixed(2)} - 建议: ${result.analysis?.nextStepRecommendation || 'N/A'}`);
                
            } catch (error) {
                this.recordTestResult(`L3-${testCase.name}`, false, { error: error.message });
                console.log(`  ❌ ${testCase.name}: 测试异常 - ${error.message}`);
            }
        }
    }
    
    /**
     * 测试三层验证集成
     */
    async testThreeLayerIntegration() {
        console.log('\n📋 测试三层验证集成...');
        
        const testCase = {
            plan: { intent: 'CLICK', parameters: { element: '确认按钮', ref: 'confirm-btn' }, confidence: 0.9 },
            evidence: { 
                success: true, 
                executionResult: { action: 'click', element: '确认按钮' },
                beforeState: { snapshot: '执行前快照' },
                afterState: { snapshot: '执行后快照' },
                duration: 1200
            },
            state: {
                taskGoal: '确认订单提交',
                cycleCount: 2,
                maxCycles: 5,
                executionContext: {
                    evidenceHistory: [
                        { plan: { intent: 'NAVIGATE' }, evidence: { success: true }, validation: { status: 'SUCCESS' } }
                    ]
                }
            }
        };
        
        try {
            const result = await this.validationEngine.performThreeLayerValidation(
                testCase.plan,
                testCase.evidence,
                testCase.state
            );
            
            const passed = result.status === 'SUCCESS' || result.status === 'NEEDS_IMPROVEMENT';
            this.recordTestResult('集成测试-三层验证', passed, {
                status: result.status,
                score: result.score,
                confidence: result.confidence,
                nextAction: result.nextAction,
                layerResults: {
                    l1: result.layerResults?.l1?.passed || false,
                    l2: result.layerResults?.l2?.passed || false,
                    l3: result.layerResults?.l3?.passed || false
                }
            });
            
            console.log(`  ${passed ? '✅' : '❌'} 三层验证集成: ${result.status}`);
            console.log(`    综合分数: ${result.score?.toFixed(2) || 'N/A'}`);
            console.log(`    置信度: ${result.confidence?.toFixed(2) || 'N/A'}`);
            console.log(`    L1/L2/L3通过: ${result.layerResults?.l1?.passed}/${result.layerResults?.l2?.passed}/${result.layerResults?.l3?.passed}`);
            console.log(`    下一步动作: ${result.nextAction}`);
            
        } catch (error) {
            this.recordTestResult('集成测试-三层验证', false, { error: error.message });
            console.log(`  ❌ 三层验证集成: 测试异常 - ${error.message}`);
        }
    }
    
    /**
     * 测试自适应提示词系统
     */
    async testAdaptivePromptSystem() {
        console.log('\n📋 测试自适应提示词系统...');
        
        const testCases = [
            {
                name: '规划提示词生成',
                state: { 
                    taskGoal: '登录系统',
                    cycleCount: 1,
                    maxCycles: 5,
                    executionContext: { evidenceHistory: [] },
                    errorHistory: []
                }
            },
            {
                name: '复杂场景提示词',
                state: {
                    taskGoal: '处理复杂业务流程',
                    cycleCount: 4,
                    maxCycles: 10,
                    executionContext: {
                        evidenceHistory: [
                            { plan: { intent: 'NAVIGATE' }, evidence: { success: false } },
                            { plan: { intent: 'CLICK' }, evidence: { success: true } }
                        ]
                    },
                    errorHistory: [
                        { error: '网络超时', node: 'execution_node' },
                        { error: '元素未找到', node: 'execution_node' }
                    ]
                }
            }
        ];
        
        for (const testCase of testCases) {
            try {
                const prompt = this.promptSystem.generatePlanningPrompt(testCase.state);
                
                const passed = prompt && prompt.length > 100 && prompt.includes(testCase.state.taskGoal);
                this.recordTestResult(`自适应提示词-${testCase.name}`, passed, {
                    promptLength: prompt.length,
                    containsGoal: prompt.includes(testCase.state.taskGoal),
                    preview: prompt.substring(0, 100) + '...'
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${testCase.name}: 生成 ${prompt.length} 字符提示词`);
                
            } catch (error) {
                this.recordTestResult(`自适应提示词-${testCase.name}`, false, { error: error.message });
                console.log(`  ❌ ${testCase.name}: 测试异常 - ${error.message}`);
            }
        }
    }
    
    /**
     * 测试边缘场景处理
     */
    async testEdgeCaseHandling() {
        console.log('\n📋 测试边缘场景处理...');
        
        const testCases = [
            {
                name: '动态等待机制',
                context: { type: 'PAGE_LOAD', maxWait: 5000 }
            },
            {
                name: '网络错误恢复',
                error: { message: '网络连接超时', type: 'NETWORK_ERROR' },
                context: { currentNode: 'execution_node', ticketId: 'test_123' }
            },
            {
                name: '元素等待处理',
                context: { type: 'ELEMENT_VISIBLE', selector: '#submit-btn', maxWait: 3000 }
            }
        ];
        
        for (const testCase of testCases) {
            try {
                let result;
                
                if (testCase.error) {
                    // 测试错误恢复
                    result = await this.edgeHandler.performErrorRecovery(testCase.error, testCase.context);
                } else {
                    // 测试动态等待
                    result = await this.edgeHandler.performDynamicWait(testCase.context);
                }
                
                const passed = result && (result.satisfied !== false) && (result.success !== false);
                this.recordTestResult(`边缘场景-${testCase.name}`, passed, {
                    result: result,
                    duration: result?.duration,
                    success: result?.success,
                    satisfied: result?.satisfied
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${testCase.name}: ${result?.success !== false ? '成功' : '失败'}`);
                
            } catch (error) {
                this.recordTestResult(`边缘场景-${testCase.name}`, false, { error: error.message });
                console.log(`  ❌ ${testCase.name}: 测试异常 - ${error.message}`);
            }
        }
    }
    
    /**
     * 测试性能和可扩展性
     */
    async testPerformanceAndScalability() {
        console.log('\n📋 测试性能和可扩展性...');
        
        const performanceTests = [
            {
                name: '并发验证处理',
                testFunc: () => this.testConcurrentValidation()
            },
            {
                name: '大量数据处理',
                testFunc: () => this.testLargeDataHandling()
            },
            {
                name: '内存使用优化',
                testFunc: () => this.testMemoryUsage()
            }
        ];
        
        for (const test of performanceTests) {
            try {
                const startTime = Date.now();
                const result = await test.testFunc();
                const duration = Date.now() - startTime;
                
                const passed = result && duration < 10000; // 10秒内完成
                this.recordTestResult(`性能测试-${test.name}`, passed, {
                    duration: duration,
                    result: result
                });
                
                console.log(`  ${passed ? '✅' : '❌'} ${test.name}: ${duration}ms`);
                
            } catch (error) {
                this.recordTestResult(`性能测试-${test.name}`, false, { error: error.message });
                console.log(`  ❌ ${test.name}: 测试异常 - ${error.message}`);
            }
        }
    }
    
    /**
     * 测试并发验证处理
     */
    async testConcurrentValidation() {
        const concurrentTasks = [];
        
        for (let i = 0; i < 5; i++) {
            const task = this.validationEngine.performThreeLayerValidation(
                { intent: 'CLICK', parameters: {} },
                { success: true, executionResult: { action: 'click' } },
                { taskGoal: `并发任务${i}`, cycleCount: 1 }
            );
            concurrentTasks.push(task);
        }
        
        const results = await Promise.all(concurrentTasks);
        return results.every(result => result && result.score > 0);
    }
    
    /**
     * 测试大量数据处理
     */
    async testLargeDataHandling() {
        const largeHistory = [];
        for (let i = 0; i < 100; i++) {
            largeHistory.push({
                plan: { intent: 'CLICK', parameters: {} },
                evidence: { success: true },
                validation: { status: 'SUCCESS' }
            });
        }
        
        const result = await this.validationEngine.performThreeLayerValidation(
            { intent: 'CLICK', parameters: {} },
            { success: true, executionResult: { action: 'click' } },
            { 
                taskGoal: '大数据处理测试',
                cycleCount: 50,
                executionContext: { evidenceHistory: largeHistory }
            }
        );
        
        return result && result.score > 0;
    }
    
    /**
     * 测试内存使用
     */
    async testMemoryUsage() {
        const startMemory = process.memoryUsage();
        
        // 执行多次验证操作
        for (let i = 0; i < 20; i++) {
            await this.validationEngine.performThreeLayerValidation(
                { intent: 'CLICK', parameters: {} },
                { success: true, executionResult: { action: 'click' } },
                { taskGoal: `内存测试${i}`, cycleCount: 1 }
            );
        }
        
        const endMemory = process.memoryUsage();
        const memoryIncrease = endMemory.heapUsed - startMemory.heapUsed;
        
        // 内存增长应该在合理范围内（< 50MB）
        return memoryIncrease < 50 * 1024 * 1024;
    }
    
    /**
     * 记录测试结果
     */
    recordTestResult(testName, passed, details = {}) {
        this.testResults.totalTests++;
        if (passed) {
            this.testResults.passedTests++;
        } else {
            this.testResults.failedTests++;
        }
        
        this.testResults.testDetails.push({
            name: testName,
            passed: passed,
            details: details,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * 输出测试总结
     */
    outputTestSummary() {
        console.log('\n=== 测试总结 ===');
        console.log(`总测试数: ${this.testResults.totalTests}`);
        console.log(`通过: ${this.testResults.passedTests}`);
        console.log(`失败: ${this.testResults.failedTests}`);
        console.log(`通过率: ${(this.testResults.passedTests / this.testResults.totalTests * 100).toFixed(1)}%`);
        
        // 输出失败的测试详情
        const failedTests = this.testResults.testDetails.filter(test => !test.passed);
        if (failedTests.length > 0) {
            console.log('\n失败的测试:');
            failedTests.forEach(test => {
                console.log(`  ❌ ${test.name}: ${test.details.error || '检查详细信息'}`);
            });
        }
        
        // 获取系统统计
        const edgeStats = this.edgeHandler.getEdgeStats();
        const validationStats = this.validationEngine.getValidationStats();
        const promptStats = this.promptSystem.getPromptStats();
        
        console.log('\n=== 系统统计 ===');
        console.log(`边缘场景处理: 恢复成功率 ${(edgeStats.recoverySuccessRate * 100).toFixed(1)}%`);
        console.log(`验证统计: 总验证 ${validationStats.totalValidations} 次`);
        console.log(`提示词系统: 各层通过率 L1:${(promptStats.successRates?.validation?.rate * 100 || 0).toFixed(1)}%`);
        
        logger.info('🎉 三层智能验证机制综合测试完成', {
            totalTests: this.testResults.totalTests,
            passedTests: this.testResults.passedTests,
            failedTests: this.testResults.failedTests,
            passRate: (this.testResults.passedTests / this.testResults.totalTests * 100).toFixed(1)
        });
    }
}

// 运行测试
if (require.main === module) {
    const test = new ThreeLayerValidationTest();
    test.runComprehensiveTest().then(() => {
        console.log('\n✅ 所有测试执行完成');
        process.exit(0);
    }).catch(error => {
        console.error('\n❌ 测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = ThreeLayerValidationTest;