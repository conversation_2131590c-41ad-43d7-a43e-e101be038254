/**
 * LLM客户端工厂测试 - 验证API降级和Mock系统
 * 这是TDD开发的第一步：验证我们的API credits解决方案
 */

require('dotenv').config();
const { llmClientFactory } = require('./src/ai-assistant/src/pdca/llm-client-factory');
const logger = require('./src/ai-assistant/src/utils/logger');

async function testLLMClientFactory() {
    console.log('🧪 开始LLM客户端工厂测试...\n');
    
    const results = {
        passed: [],
        failed: []
    };

    try {
        // 测试1: Mock客户端强制模式
        console.log('📋 测试1: Mock客户端强制模式');
        try {
            const mockClient = await llmClientFactory.createClient({ forceMock: true });
            const response = await mockClient.chat('分析工单：上架榮記豆腐麵食的外卖商品菜遠牛肉飯');
            
            if (response && response.includes('榮記豆腐麵食') && response.includes('菜遠牛肉飯')) {
                results.passed.push({
                    test: 'Mock客户端强制模式',
                    reason: 'Mock客户端正确理解业务语义'
                });
                console.log('✅ 测试1通过 - Mock客户端工作正常\n');
            } else {
                throw new Error('Mock响应不包含预期的业务信息');
            }
        } catch (error) {
            results.failed.push({
                test: 'Mock客户端强制模式',
                error: error.message
            });
            console.log('❌ 测试1失败\n');
        }

        // 测试2: 自动降级机制
        console.log('📋 测试2: 自动降级机制');
        try {
            // 重新创建客户端，让它尝试真实API然后降级
            const client = await llmClientFactory.createClient({ 
                autoFallback: true,
                enableHealthCheck: true 
            });
            
            const status = llmClientFactory.getStatus();
            console.log(`当前客户端类型: ${status.currentType}`);
            
            const response = await client.chat('页面观察：识别商品管理页面的可操作元素');
            
            if (response && response.length > 0) {
                results.passed.push({
                    test: '自动降级机制',
                    reason: `成功获得响应，客户端类型: ${status.currentType}`
                });
                console.log('✅ 测试2通过 - 自动降级机制工作正常\n');
            } else {
                throw new Error('未获得有效响应');
            }
        } catch (error) {
            results.failed.push({
                test: '自动降级机制',
                error: error.message
            });
            console.log('❌ 测试2失败\n');
        }

        // 测试3: 业务语义理解测试
        console.log('📋 测试3: 业务语义理解测试');
        try {
            const client = llmClientFactory.getCurrentClient();
            const testPrompts = [
                '分析工单：上架榮記豆腐麵食(官也街、招牌煲仔飯、嫩滑豆腐花)的外卖商品: 菜遠牛肉飯',
                '观察页面：商品管理界面，识别添加商品的操作元素',
                '制定操作计划：如何在系统中添加新的外卖商品',
                '验证结果：确认菜遠牛肉飯已成功上架'
            ];

            let semanticTestsPassed = 0;
            for (const prompt of testPrompts) {
                const response = await client.chat(prompt);
                if (response && response.length > 10) { // 基本响应长度检查
                    semanticTestsPassed++;
                    console.log(`  ✓ 语义测试通过: ${prompt.substring(0, 30)}...`);
                } else {
                    console.log(`  ✗ 语义测试失败: ${prompt.substring(0, 30)}...`);
                }
            }

            if (semanticTestsPassed >= 3) { // 至少75%通过率
                results.passed.push({
                    test: '业务语义理解测试',
                    reason: `${semanticTestsPassed}/${testPrompts.length}个语义测试通过`
                });
                console.log('✅ 测试3通过 - 业务语义理解正常\n');
            } else {
                throw new Error(`语义理解测试通过率过低: ${semanticTestsPassed}/${testPrompts.length}`);
            }
        } catch (error) {
            results.failed.push({
                test: '业务语义理解测试',
                error: error.message
            });
            console.log('❌ 测试3失败\n');
        }

        // 测试4: 客户端状态和统计
        console.log('📋 测试4: 客户端状态和统计');
        try {
            const status = llmClientFactory.getStatus();
            const stats = status.stats;

            if (stats && stats.requestCount > 0) {
                results.passed.push({
                    test: '客户端状态和统计',
                    reason: `请求计数: ${stats.requestCount}, 成功率: ${(stats.successRate * 100).toFixed(1)}%`
                });
                console.log('✅ 测试4通过 - 状态统计正常\n');
            } else {
                throw new Error('统计信息异常');
            }
        } catch (error) {
            results.failed.push({
                test: '客户端状态和统计',
                error: error.message
            });
            console.log('❌ 测试4失败\n');
        }

        // 测试5: 概率性响应验证
        console.log('📋 测试5: 概率性响应验证（AI系统特有）');
        try {
            const client = llmClientFactory.getCurrentClient();
            const responses = [];
            
            // 发送相同请求多次，验证响应的一致性和合理性
            for (let i = 0; i < 3; i++) {
                const response = await client.chat('决策：为榮記豆腐麵食上架菜遠牛肉飯需要哪些操作步骤？');
                responses.push(response);
            }

            // 验证响应的语义一致性（而非字符串完全匹配）
            const containsKeywords = responses.every(r => 
                r.includes('榮記豆腐麵食') || r.includes('菜遠牛肉飯') || r.includes('上架') || r.includes('商品')
            );

            if (containsKeywords) {
                results.passed.push({
                    test: '概率性响应验证',
                    reason: '多次请求的语义一致性良好'
                });
                console.log('✅ 测试5通过 - 概率性响应验证正常\n');
            } else {
                throw new Error('响应语义一致性不足');
            }
        } catch (error) {
            results.failed.push({
                test: '概率性响应验证',
                error: error.message
            });
            console.log('❌ 测试5失败\n');
        }

    } catch (error) {
        console.error('💥 测试执行异常:', error);
    }

    // 输出测试结果
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过: ${results.passed.length}个测试`);
    console.log(`❌ 失败: ${results.failed.length}个测试`);
    
    if (results.passed.length > 0) {
        console.log('\n🎉 通过的测试:');
        results.passed.forEach(result => {
            console.log(`  ✓ ${result.test}: ${result.reason}`);
        });
    }
    
    if (results.failed.length > 0) {
        console.log('\n💥 失败的测试:');
        results.failed.forEach(result => {
            console.log(`  ✗ ${result.test}: ${result.error}`);
        });
    }

    // 显示最终状态
    const finalStatus = llmClientFactory.getStatus();
    console.log('\n📈 最终状态:');
    console.log(`  客户端类型: ${finalStatus.currentType}`);
    console.log(`  自动降级: ${finalStatus.autoFallback ? '启用' : '禁用'}`);
    if (finalStatus.stats) {
        console.log(`  请求统计: ${finalStatus.stats.requestCount}次请求, ${(finalStatus.stats.successRate * 100).toFixed(1)}%成功率`);
    }

    const successRate = results.passed.length / (results.passed.length + results.failed.length);
    console.log(`\n🎯 总体成功率: ${(successRate * 100).toFixed(1)}%`);
    
    if (successRate >= 0.8) {
        console.log('🎉 LLM客户端工厂测试整体通过！API credits问题已解决。');
        return true;
    } else {
        console.log('⚠️ LLM客户端工厂测试存在问题，需要进一步优化。');
        return false;
    }
}

// 运行测试
if (require.main === module) {
    testLLMClientFactory().catch(console.error);
}

module.exports = testLLMClientFactory;
