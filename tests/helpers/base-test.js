// tests/helpers/base-test.js
// 基础测试类

class BaseTest {
    constructor(name, description = '') {
        this.name = name;
        this.description = description;
        this.startTime = null;
        this.endTime = null;
        this.metrics = {
            duration: 0,
            memory: {},
            custom: {}
        };
        this.status = 'pending';
        this.error = null;
    }

    /**
     * 测试前准备
     */
    async setup() {
        this.startTime = Date.now();
        this.metrics.memory.start = process.memoryUsage();
        console.log(`\n🧪 测试: ${this.name}`);
        if (this.description) {
            console.log(`   ${this.description}`);
        }
    }

    /**
     * 测试后清理
     */
    async teardown() {
        this.endTime = Date.now();
        this.metrics.duration = this.endTime - this.startTime;
        this.metrics.memory.end = process.memoryUsage();
        this.metrics.memory.delta = {
            heapUsed: this.metrics.memory.end.heapUsed - this.metrics.memory.start.heapUsed,
            external: this.metrics.memory.end.external - this.metrics.memory.start.external
        };
    }

    /**
     * 运行测试
     */
    async run() {
        try {
            await this.setup();
            await this.execute();
            this.status = 'passed';
            console.log(`   ✅ 通过 (${this.metrics.duration}ms)`);
        } catch (error) {
            this.status = 'failed';
            this.error = error;
            console.error(`   ❌ 失败: ${error.message}`);
        } finally {
            await this.teardown();
        }
        return this.getResult();
    }

    /**
     * 实际测试逻辑（子类需要实现）
     */
    async execute() {
        throw new Error('子类必须实现execute方法');
    }

    /**
     * 记录自定义指标
     */
    recordMetric(key, value) {
        this.metrics.custom[key] = value;
    }

    /**
     * 断言辅助方法
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`断言失败: ${message}`);
        }
    }

    assertEquals(actual, expected, message = '') {
        if (actual !== expected) {
            throw new Error(`期望 ${expected}, 实际 ${actual}. ${message}`);
        }
    }

    assertNotNull(value, message = '') {
        if (value === null || value === undefined) {
            throw new Error(`期望非空值. ${message}`);
        }
    }

    /**
     * 获取测试结果
     */
    getResult() {
        return {
            name: this.name,
            status: this.status,
            duration: this.metrics.duration,
            memory: this.metrics.memory,
            customMetrics: this.metrics.custom,
            error: this.error ? {
                message: this.error.message,
                stack: this.error.stack
            } : null
        };
    }
}

module.exports = BaseTest;