// tests/helpers/test-runner.js
// 测试运行器

const fs = require('fs').promises;
const path = require('path');

class TestRunner {
    constructor(options = {}) {
        this.options = {
            verbose: options.verbose || false,
            reportPath: options.reportPath || './test-reports',
            parallel: options.parallel || false,
            ...options
        };
        this.results = [];
        this.startTime = null;
        this.endTime = null;
    }

    /**
     * 运行测试套件
     */
    async runSuite(tests, suiteName = 'Test Suite') {
        console.log('\n' + '='.repeat(60));
        console.log(`📊 ${suiteName}`);
        console.log('='.repeat(60));
        console.log(`测试数量: ${tests.length}`);
        console.log(`运行模式: ${this.options.parallel ? '并行' : '串行'}`);
        
        this.startTime = Date.now();
        
        if (this.options.parallel) {
            await this.runParallel(tests);
        } else {
            await this.runSequential(tests);
        }
        
        this.endTime = Date.now();
        
        const report = await this.generateReport(suiteName);
        await this.saveReport(report);
        
        return report;
    }

    /**
     * 串行运行测试
     */
    async runSequential(tests) {
        for (const test of tests) {
            const result = await this.runTest(test);
            this.results.push(result);
        }
    }

    /**
     * 并行运行测试
     */
    async runParallel(tests) {
        const promises = tests.map(test => this.runTest(test));
        const results = await Promise.all(promises);
        this.results.push(...results);
    }

    /**
     * 运行单个测试
     */
    async runTest(test) {
        try {
            return await test.run();
        } catch (error) {
            return {
                name: test.name,
                status: 'error',
                error: {
                    message: error.message,
                    stack: error.stack
                }
            };
        }
    }

    /**
     * 生成测试报告
     */
    async generateReport(suiteName) {
        const totalDuration = this.endTime - this.startTime;
        const passed = this.results.filter(r => r.status === 'passed').length;
        const failed = this.results.filter(r => r.status === 'failed').length;
        const errors = this.results.filter(r => r.status === 'error').length;

        console.log('\n' + '-'.repeat(60));
        console.log('📈 测试结果汇总');
        console.log('-'.repeat(60));
        console.log(`总耗时: ${totalDuration}ms`);
        console.log(`通过: ${passed} | 失败: ${failed} | 错误: ${errors}`);
        
        if (failed > 0 || errors > 0) {
            console.log('\n❌ 失败的测试:');
            this.results
                .filter(r => r.status !== 'passed')
                .forEach(r => {
                    console.log(`- ${r.name}: ${r.error?.message}`);
                });
        }

        // 计算性能统计
        const performanceStats = this.calculatePerformanceStats();
        
        console.log('\n⚡ 性能统计:');
        console.log(`平均执行时间: ${performanceStats.avgDuration}ms`);
        console.log(`最慢测试: ${performanceStats.slowest.name} (${performanceStats.slowest.duration}ms)`);
        console.log(`内存使用峰值: ${performanceStats.peakMemory}MB`);

        return {
            suiteName,
            timestamp: new Date(),
            summary: {
                total: this.results.length,
                passed,
                failed,
                errors,
                duration: totalDuration
            },
            performance: performanceStats,
            results: this.results
        };
    }

    /**
     * 计算性能统计
     */
    calculatePerformanceStats() {
        const durations = this.results.map(r => r.duration || 0);
        const avgDuration = Math.round(durations.reduce((a, b) => a + b, 0) / durations.length);
        
        const slowest = this.results.reduce((prev, curr) => 
            (curr.duration || 0) > (prev.duration || 0) ? curr : prev
        );

        const peakMemory = Math.round(
            Math.max(...this.results.map(r => 
                r.memory?.delta?.heapUsed || 0
            )) / 1024 / 1024
        );

        return {
            avgDuration,
            slowest: {
                name: slowest.name,
                duration: slowest.duration
            },
            peakMemory
        };
    }

    /**
     * 保存测试报告
     */
    async saveReport(report) {
        await fs.mkdir(this.options.reportPath, { recursive: true });
        const filename = `test-report-${Date.now()}.json`;
        const filepath = path.join(this.options.reportPath, filename);
        
        await fs.writeFile(filepath, JSON.stringify(report, null, 2));
        console.log(`\n📄 报告已保存: ${filepath}`);
    }
}

module.exports = TestRunner;