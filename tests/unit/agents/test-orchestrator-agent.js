// tests/unit/agents/test-orchestrator-agent.js
// OrchestratorAgent单元测试

const BaseTest = require('../../helpers/base-test');
const OrchestratorAgent = require('../../../src/ai-assistant/src/agents/orchestrator-agent');

class TestOrchestratorAgent extends BaseTest {
    constructor() {
        super('OrchestratorAgent基础功能测试', '测试总控Agent的工单分析能力');
        this.agent = new OrchestratorAgent();
    }

    async execute() {
        // 测试1: Agent创建
        this.assertNotNull(this.agent, 'Agent应该被成功创建');
        this.assert(this.agent.agentName === 'OrchestratorAgent', 'Agent名称应该是OrchestratorAgent');
        
        // 测试2: 系统提示词验证
        this.assert(
            this.agent.systemPrompt.includes('总控制器'),
            '系统提示词应包含总控制器描述'
        );
        
        // 测试3: 增强功能验证（任务规划）
        this.assert(
            this.agent.systemPrompt.includes('任务规划功能'),
            '应包含新增的任务规划功能'
        );
        
        // 测试4: 方法存在性验证
        this.assert(
            typeof this.agent.analyzeWorkOrder === 'function',
            '应该有analyzeWorkOrder方法'
        );
        this.assert(
            typeof this.agent.coordinateNextStep === 'function',
            '应该有coordinateNextStep方法'
        );
        
        // 测试5: 模拟工单分析输入
        const mockInput = {
            action: 'analyze_workorder',
            workorder: {
                title: '测试工单',
                content: '请帮我搜索商品'
            }
        };
        
        // 由于需要LLM，这里只测试输入格式
        this.assert(
            typeof mockInput.workorder === 'object',
            '工单输入应该是对象'
        );
        
        this.recordMetric('promptLength', this.agent.systemPrompt.length);
        this.recordMetric('agentName', this.agent.agentName);
    }
}

// 如果直接运行此文件
if (require.main === module) {
    const test = new TestOrchestratorAgent();
    test.run().then(result => {
        console.log('\n测试完成:', result.status);
    });
}

module.exports = TestOrchestratorAgent;