// tests/run-all-tests.js
// 运行所有测试

const TestRunner = require('./helpers/test-runner');
const TestOrchestratorAgent = require('./unit/agents/test-orchestrator-agent');

// 导入所有测试类
const unitTests = [
    new TestOrchestratorAgent(),
    // 后续添加更多Agent测试
];

const integrationTests = [
    // 集成测试
];

const e2eTests = [
    // 端到端测试
];

async function runAllTests() {
    console.log('🚀 开始运行测试套件...\n');
    
    const runner = new TestRunner({
        verbose: true,
        reportPath: './test-reports'
    });

    // 运行单元测试
    if (unitTests.length > 0) {
        await runner.runSuite(unitTests, '单元测试');
    }

    // 运行集成测试
    if (integrationTests.length > 0) {
        await runner.runSuite(integrationTests, '集成测试');
    }

    // 运行端到端测试
    if (e2eTests.length > 0) {
        await runner.runSuite(e2eTests, '端到端测试');
    }

    console.log('\n✨ 所有测试完成！');
}

// 执行测试
runAllTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
});