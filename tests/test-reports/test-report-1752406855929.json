{"suiteName": "单元测试", "timestamp": "2025-07-13T11:40:55.929Z", "summary": {"total": 1, "passed": 0, "failed": 1, "errors": 0, "duration": 0}, "performance": {"avgDuration": 0, "slowest": {"name": "OrchestratorAgent基础功能测试", "duration": 0}, "peakMemory": 0}, "results": [{"name": "OrchestratorAgent基础功能测试", "status": "failed", "duration": 0, "memory": {"start": {"rss": 63520768, "heapTotal": 12615680, "heapUsed": 9339280, "external": 3293373, "arrayBuffers": 71167}, "end": {"rss": 63668224, "heapTotal": 12615680, "heapUsed": 9378776, "external": 3293413, "arrayBuffers": 71167}, "delta": {"heapUsed": 39496, "external": 40}}, "customMetrics": {}, "error": {"message": "断言失败: Agent角色应该是orchestrator", "stack": "Error: 断言失败: Agent角色应该是orchestrator\n    at TestOrchestratorAgent.assert (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/base-test.js:82:19)\n    at TestOrchestratorAgent.execute (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/unit/agents/test-orchestrator-agent.js:16:14)\n    at TestOrchestratorAgent.run (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/base-test.js:50:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TestRunner.runTest (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/test-runner.js:70:20)\n    at async TestRunner.runSequential (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/test-runner.js:51:28)\n    at async TestRunner.runSuite (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/helpers/test-runner.js:35:13)\n    at async runAllTests (/Users/<USER>/Downloads/roo/project_RPA_langGraph/tests/run-all-tests.js:31:9)"}}]}